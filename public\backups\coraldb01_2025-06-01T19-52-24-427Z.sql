-- MySQL dump created by Salon Del Reino Admin System
-- Host: localhost    Database: coraldb01
-- ------------------------------------------------------
-- Server version	8.0.28

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `congregation_settings`
--

DROP TABLE IF EXISTS `congregation_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `congregation_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `congregation_id` int NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting` (`congregation_id`,`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `congregation_settings`
--

LOCK TABLES `congregation_settings` WRITE;
/*!40000 ALTER TABLE `congregation_settings` DISABLE KEYS */;
INSERT INTO `congregation_settings` (`id`, `congregation_id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES (1, 1441, 'congregation_name', 'Coral Oeste', '2025-04-26 07:23:52', '2025-04-26 07:23:52'),
(2, 1441, 'congregation_pin', '1930', '2025-04-26 07:23:52', '2025-04-26 07:23:52'),
(3, 1441, 'number_of_groups', '7', '2025-04-26 07:23:52', '2025-04-26 07:23:52'),
(4, 1441, 'language', 'es', '2025-04-26 07:23:52', '2025-04-26 07:23:52'),
(5, 1441, 'timezone', 'America/New_York', '2025-04-26 07:23:52', '2025-04-26 07:23:52');
/*!40000 ALTER TABLE `congregation_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `congregations`
--

DROP TABLE IF EXISTS `congregations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `congregations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `region` varchar(50) DEFAULT NULL,
  `congregation_id` varchar(20) DEFAULT NULL,
  `congregation_pin` varchar(20) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `congregation_id` (`congregation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `congregations`
--

LOCK TABLES `congregations` WRITE;
/*!40000 ALTER TABLE `congregations` DISABLE KEYS */;
INSERT INTO `congregations` (`id`, `name`, `region`, `congregation_id`, `congregation_pin`, `created_at`, `updated_at`) VALUES (1, 'Coral Oeste', 'North America', '1441', '1930', '2025-04-24 02:01:19', '2025-04-24 02:18:07');
/*!40000 ALTER TABLE `congregations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `elder_permissions`
--

DROP TABLE IF EXISTS `elder_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `elder_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `elder_id` int NOT NULL,
  `permission` varchar(50) NOT NULL,
  `granted_by` int NOT NULL,
  `congregation_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `granted_by` (`granted_by`),
  KEY `congregation_id` (`congregation_id`),
  KEY `idx_elder_permissions_elder_id` (`elder_id`)
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `elder_permissions`
--

LOCK TABLES `elder_permissions` WRITE;
/*!40000 ALTER TABLE `elder_permissions` DISABLE KEYS */;
INSERT INTO `elder_permissions` (`id`, `elder_id`, `permission`, `granted_by`, `congregation_id`, `created_at`, `updated_at`) VALUES (1, 2, 'assign_ministerial_servant', 1, 1, '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(2, 1, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(3, 1, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(4, 1, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(5, 2, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(6, 2, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(7, 2, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(8, 3, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(9, 3, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(10, 3, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(11, 6, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(12, 6, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(13, 6, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(14, 7, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(15, 7, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(16, 7, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(17, 498, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(18, 498, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(19, 498, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(20, 680, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(21, 680, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(22, 680, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(23, 681, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(24, 681, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(25, 681, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(26, 682, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(27, 682, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(28, 682, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(29, 683, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(30, 683, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(31, 683, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(32, 684, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(33, 684, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(34, 684, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(35, 685, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(36, 685, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(37, 685, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(38, 686, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(39, 686, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(40, 686, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(41, 687, 'letters_edit', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(42, 687, 'letters_delete', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09'),
(43, 687, 'letters', 1, 1, '2025-04-25 09:54:09', '2025-04-25 09:54:09');
/*!40000 ALTER TABLE `elder_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `event_categories`
--

DROP TABLE IF EXISTS `event_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `event_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `event_categories`
--

LOCK TABLES `event_categories` WRITE;
/*!40000 ALTER TABLE `event_categories` DISABLE KEYS */;
INSERT INTO `event_categories` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES (1, 'Assembly', 'Circuit assemblies and other assembly events', '2025-04-26 02:20:24', '2025-04-26 02:20:24'),
(2, 'Convention', 'Regional conventions', '2025-04-26 02:20:24', '2025-04-26 02:20:24'),
(3, 'Special Event', 'Special events like Memorial and Special Talks', '2025-04-26 02:20:24', '2025-04-26 02:20:24'),
(4, 'Visit', 'Circuit overseer visits', '2025-04-26 02:20:24', '2025-04-26 02:20:24'),
(5, 'Other', 'Other events', '2025-04-26 02:20:24', '2025-04-26 02:20:24');
/*!40000 ALTER TABLE `event_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `events`
--

DROP TABLE IF EXISTS `events`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `events` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `category_id` int NOT NULL,
  `date` date NOT NULL,
  `time` time NOT NULL,
  `location` varchar(255) NOT NULL,
  `description` text,
  `status` enum('upcoming','completed') NOT NULL DEFAULT 'upcoming',
  `attendance` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `events_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `event_categories` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `events`
--

LOCK TABLES `events` WRITE;
/*!40000 ALTER TABLE `events` DISABLE KEYS */;
INSERT INTO `events` (`id`, `title`, `category_id`, `date`, `time`, `location`, `description`, `status`, `attendance`, `created_at`, `updated_at`) VALUES (1, 'Circuit Assembly', 1, '2024-06-15 04:00:00', '09:30:00', 'Assembly Hall', 'Circuit assembly for the Coral Oeste congregation', 'completed', NULL, '2025-04-26 02:20:24', '2025-05-01 03:14:14'),
(2, 'Regional Convention', 2, '2024-07-20 04:00:00', '09:00:00', 'Convention Center', 'Regional convention for all congregations in the area', 'completed', NULL, '2025-04-26 02:20:24', '2025-05-01 03:14:14'),
(3, 'Memorial', 3, '2023-04-10 04:00:00', '19:30:00', 'Kingdom Hall', 'Annual observance of the Memorial of Christ''s death', 'completed', NULL, '2025-04-26 02:20:24', '2025-04-27 10:36:49'),
(4, 'Special Talk', 3, '2024-03-15 04:00:00', '10:00:00', 'Kingdom Hall', 'Special talk for all congregations', 'completed', 126, '2025-04-26 02:20:24', '2025-04-27 10:30:28'),
(5, 'Circuit Overseer Visit', 4, '2024-02-10 05:00:00', '19:30:00', 'Kingdom Hall', 'Circuit overseer visit to the congregation', 'completed', 131, '2025-04-26 02:20:24', '2025-04-27 10:15:06'),
(6, 'ASAMBLEA DE CIRCUITO CON EL SUPERIENTENDENTE DE CIRCUITO', 1, '2025-03-29 04:00:00', '09:00:00', 'Salon de Asambleas de West Palm Beach', 'Asamblea de circuito con el superintendente de circuito. Se espera la asistencia de todas las congregaciones del circuito.', 'completed', NULL, '2025-04-26 02:20:24', '2025-05-01 03:14:14'),
(7, 'ASAMBLEA REGIONAL', 2, '2025-06-15 04:00:00', '09:00:00', 'Centro de Convenciones de Miami', 'Asamblea regional anual con el tema "Mantengámonos Firmes en la Fe". Se espera la asistencia de todos los circuitos de la región.', 'upcoming', NULL, '2025-04-26 02:20:24', '2025-04-26 02:20:24'),
(8, 'VISITA DEL SUPERINTENDENTE DE CIRCUITO', 4, '2025-05-10 04:00:00', '09:00:00', 'Salón del Reino local', 'Visita del superintendente de circuito a nuestra congregación. Habrá reuniones especiales y revisión de los registros de la congregación.', 'completed', NULL, '2025-04-26 02:20:24', '2025-05-14 20:41:51');
/*!40000 ALTER TABLE `events` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `feature_flags`
--

DROP TABLE IF EXISTS `feature_flags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `feature_flags` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `is_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `targeting_rules` json DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `feature_flags`
--

LOCK TABLES `feature_flags` WRITE;
/*!40000 ALTER TABLE `feature_flags` DISABLE KEYS */;
INSERT INTO `feature_flags` (`id`, `name`, `description`, `is_enabled`, `targeting_rules`, `created_at`, `updated_at`) VALUES (1, 'useReactMidweekMeetings', 'Use React for Midweek Meetings', 0, '[object Object]', '2025-05-14 17:53:05', '2025-05-14 17:53:05'),
(2, 'useReactMidweekMeetingsList', 'Use React for Midweek Meetings List', 0, '[object Object]', '2025-05-14 17:53:05', '2025-05-14 17:53:05'),
(3, 'useReactMidweekMeetingsForm', 'Use React for Midweek Meetings Form', 0, '[object Object]', '2025-05-14 17:53:05', '2025-05-14 17:53:05'),
(4, 'useReactMidweekMeetingsDetails', 'Use React for Midweek Meetings Details', 0, '[object Object]', '2025-05-14 17:53:05', '2025-05-14 17:53:05');
/*!40000 ALTER TABLE `feature_flags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `field_service_records`
--

DROP TABLE IF EXISTS `field_service_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `field_service_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `month` varchar(7) NOT NULL,
  `hours` int NOT NULL,
  `placements` int DEFAULT '0',
  `videos` int DEFAULT '0',
  `return_visits` int DEFAULT '0',
  `bible_studies` int DEFAULT '0',
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`,`month`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `field_service_records`
--

LOCK TABLES `field_service_records` WRITE;
/*!40000 ALTER TABLE `field_service_records` DISABLE KEYS */;
/*!40000 ALTER TABLE `field_service_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `groups`
--

DROP TABLE IF EXISTS `groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `groups` (
  `id` int NOT NULL AUTO_INCREMENT,
  `congregation_id` int NOT NULL,
  `group_number` int NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text,
  `leader_id` int DEFAULT NULL,
  `assistant_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_group` (`congregation_id`,`group_number`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `groups`
--

LOCK TABLES `groups` WRITE;
/*!40000 ALTER TABLE `groups` DISABLE KEYS */;
INSERT INTO `groups` (`id`, `congregation_id`, `group_number`, `name`, `description`, `leader_id`, `assistant_id`, `created_at`, `updated_at`) VALUES (1, 1441, 1, 'Grupo 1', 'Grupo de servicio 1', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16'),
(2, 1441, 2, 'Grupo 2', 'Grupo de servicio 2', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16'),
(3, 1441, 3, 'Grupo 3', 'Grupo de servicio 3', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16'),
(4, 1441, 4, 'Grupo 4', 'Grupo de servicio 4', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16'),
(5, 1441, 5, 'Grupo 5', 'Grupo de servicio 5', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16'),
(6, 1441, 6, 'Grupo 6', 'Grupo de servicio 6', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16'),
(7, 1441, 7, 'Grupo 7', 'Grupo de servicio 7', NULL, NULL, '2025-04-26 07:39:16', '2025-04-26 07:39:16');
/*!40000 ALTER TABLE `groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jw_scraper_logs`
--

DROP TABLE IF EXISTS `jw_scraper_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jw_scraper_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `url` varchar(255) NOT NULL,
  `status` varchar(20) NOT NULL,
  `message` text,
  `data` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jw_scraper_logs`
--

LOCK TABLES `jw_scraper_logs` WRITE;
/*!40000 ALTER TABLE `jw_scraper_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `jw_scraper_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `letters`
--

DROP TABLE IF EXISTS `letters`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `letters` (
  `id` int NOT NULL AUTO_INCREMENT,
  `filename` varchar(255) NOT NULL,
  `title` varchar(255) NOT NULL,
  `date` date NOT NULL,
  `category` varchar(50) NOT NULL DEFAULT 'General',
  `visibility` varchar(50) NOT NULL DEFAULT 'All Members',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `filename` (`filename`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `letters`
--

LOCK TABLES `letters` WRITE;
/*!40000 ALTER TABLE `letters` DISABLE KEYS */;
INSERT INTO `letters` (`id`, `filename`, `title`, `date`, `category`, `visibility`, `created_at`, `updated_at`) VALUES (4, 'vida_y_ministerio_cristianos_marzo_2019.pdf', 'Vida y Ministerio Cristianos - Marzo 2019', '2019-02-28 05:00:00', 'Importante', 'Todos los Miembros', '2025-04-25 20:16:23', '2025-04-25 20:16:23'),
(5, 'reserva_de_hoteles_para_la_asamblea_regional.pdf', 'Reserva de Hoteles para la Asamblea Regional', '2025-01-14 05:00:00', 'Anuncios', 'Todos los Miembros', '2025-04-25 20:16:23', '2025-04-25 20:16:23'),
(6, 'puntos_a_recordar_al_reservar_hotel_para_la_asamblea_regional.pdf', 'Puntos a recordar al reservar hotel para la asamblea regional', '2025-01-19 05:00:00', 'Importante', 'Todos los Miembros', '2025-04-25 20:16:23', '2025-04-25 20:16:23'),
(7, 'asignacion_asamblea_regional_2025.pdf', 'Asignacion Asamblea Regional 2025', '2025-01-31 05:00:00', 'Importante', 'Todos los Miembros', '2025-04-25 20:16:23', '2025-04-25 20:16:23'),
(11, '1745602360146-CA-copgm25_E.pdf', 'Instruccion para la Reunion Vida y Ministerio Cristianos', '2025-03-14 04:00:00', 'General', 'All Members', '2025-04-25 21:32:40', '2025-04-25 21:48:00'),
(12, '1745602387368-mwb_S_201903.pdf', 'Pautas para la Predicacion Publica', '2025-02-28 05:00:00', 'General', 'All Members', '2025-04-25 21:33:07', '2025-04-25 21:33:07'),
(13, '1745602457258-mwb_S_201903.pdf', 'Asambleas Especiales 2025', '2025-02-14 05:00:00', 'General', 'All Members', '2025-04-25 21:34:17', '2025-04-25 21:48:30');
/*!40000 ALTER TABLE `letters` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `meeting_parts`
--

DROP TABLE IF EXISTS `meeting_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `meeting_parts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `meeting_id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `duration` int NOT NULL,
  `assignee_id` int DEFAULT NULL,
  `assistant_id` int DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `meeting_id` (`meeting_id`),
  KEY `assignee_id` (`assignee_id`),
  KEY `assistant_id` (`assistant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `meeting_parts`
--

LOCK TABLES `meeting_parts` WRITE;
/*!40000 ALTER TABLE `meeting_parts` DISABLE KEYS */;
/*!40000 ALTER TABLE `meeting_parts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `meetings`
--

DROP TABLE IF EXISTS `meetings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `meetings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL,
  `type` enum('midweek','weekend') NOT NULL,
  `theme` varchar(255) DEFAULT NULL,
  `chairman_id` int DEFAULT NULL,
  `opening_prayer_id` int DEFAULT NULL,
  `closing_prayer_id` int DEFAULT NULL,
  `attendance` int DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `chairman_id` (`chairman_id`),
  KEY `opening_prayer_id` (`opening_prayer_id`),
  KEY `closing_prayer_id` (`closing_prayer_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `meetings`
--

LOCK TABLES `meetings` WRITE;
/*!40000 ALTER TABLE `meetings` DISABLE KEYS */;
/*!40000 ALTER TABLE `meetings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `member_congregations`
--

DROP TABLE IF EXISTS `member_congregations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `member_congregations` (
  `id` int NOT NULL AUTO_INCREMENT,
  `member_id` int NOT NULL,
  `congregation_id` int NOT NULL,
  `is_verified` tinyint(1) DEFAULT '0',
  `verification_pin` varchar(10) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `member_id` (`member_id`,`congregation_id`),
  KEY `idx_member_congregations_member_id` (`member_id`),
  KEY `idx_member_congregations_congregation_id` (`congregation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `member_congregations`
--

LOCK TABLES `member_congregations` WRITE;
/*!40000 ALTER TABLE `member_congregations` DISABLE KEYS */;
INSERT INTO `member_congregations` (`id`, `member_id`, `congregation_id`, `is_verified`, `verification_pin`, `created_at`, `updated_at`) VALUES (1, 1, 1, 1, NULL, '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(2, 2, 1, 1, NULL, '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(3, 3, 1, 1, NULL, '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(4, 4, 1, 1, NULL, '2025-04-24 02:01:19', '2025-04-24 02:01:19');
/*!40000 ALTER TABLE `member_congregations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `members`
--

DROP TABLE IF EXISTS `members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `members` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `name` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `phone` varchar(20) DEFAULT NULL,
  `role_id` int NOT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `congregation_id` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_members_role_id` (`role_id`),
  CONSTRAINT `fk_members_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5176 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `members`
--

LOCK TABLES `members` WRITE;
/*!40000 ALTER TABLE `members` DISABLE KEYS */;
INSERT INTO `members` (`id`, `username`, `password`, `name`, `email`, `phone`, `role_id`, `is_active`, `last_login`, `created_at`, `updated_at`, `congregation_id`) VALUES (1, 'richard', '5488', 'Richard Rubi', '<EMAIL>', '************', 1, 1, '2025-05-01 03:24:04', '2025-04-24 02:01:19', '2025-05-01 03:33:45', '1441'),
(2, 'horacio', '5447', 'Horacio Cerda', '<EMAIL>', '************', 2, 1, NULL, '2025-04-24 02:01:19', '2025-05-01 03:33:36', '1441'),
(3, 'yoan', '6574', 'Yoan Valiente', '<EMAIL>', '************', 3, 1, NULL, '2025-04-24 02:01:19', '2025-05-01 03:33:29', '1441'),
(4, 'lourdes', '0007', 'Lourdes Rubi', '<EMAIL>', '************', 4, 1, '2025-04-30 04:37:50', '2025-04-24 02:01:19', '2025-04-30 04:37:50', '1441'),
(5, 'danay', '9982', 'Danay Valiente', '<EMAIL>', '************', 4, 1, NULL, '2025-04-24 23:39:36', '2025-05-01 03:33:53', '1441'),
(6, 'developer', '5555', 'Yoan Developer', '<EMAIL>', '************', 1, 1, '2025-06-01 18:38:49', '2025-04-24 23:53:53', '2025-06-01 18:38:49', '1441'),
(7, 'james', '4445', 'James Rubi', '<EMAIL>', '************', 2, 1, '2025-05-01 04:12:26', '2025-04-25 02:17:50', '2025-05-01 04:12:26', '1441'),
(8, 'admin', '2030', 'Admin User', '<EMAIL>', '555-9999', 5, 1, '2025-05-02 02:56:20', '2025-04-25 08:20:09', '2025-05-02 02:56:20', '1441'),
(9, 'david.fernandez', '4455', 'David Fernandez', '<EMAIL>', '************', 2, 1, NULL, '2025-04-28 09:05:28', '2025-05-01 21:12:00', NULL),
(10, 'ester.fernandez', '7788', 'Ester Fernandez', '<EMAIL>', '************', 4, 1, NULL, '2025-04-28 09:05:57', '2025-05-01 21:12:02', NULL),
(11, 'jorge.diaz', '4103', 'Jorge Diaz', '<EMAIL>', '************', 2, 1, NULL, '2025-04-28 09:06:24', '2025-05-01 21:12:06', NULL),
(12, 'tania.diaz', '3214', 'Tania Diaz', '<EMAIL>', '************', 4, 1, NULL, '2025-04-28 09:06:43', '2025-05-01 21:12:08', NULL),
(14, 'javier.torrez', '3254', 'Javier Torrez', '<EMAIL>', '************', 2, 1, NULL, '2025-04-28 09:28:39', '2025-05-01 21:12:13', NULL),
(15, 'jessi.torrez', '9872', 'Jessi Torrez', '<EMAIL>', '************', 2, 1, NULL, '2025-04-28 09:29:02', '2025-05-01 21:12:16', NULL),
(16, 'jenny.torrez', '0457', 'Jenny Torrez', '<EMAIL>', '************', 4, 1, NULL, '2025-04-28 09:29:25', '2025-05-01 21:12:18', NULL),
(17, 'laura.torrez', '6577', 'Laura Torrez', '<EMAIL>', '305-123-1345', 4, 1, NULL, '2025-04-28 09:29:46', '2025-05-01 21:12:20', NULL),
(18, 'raul.ramirez', '5447', 'Raul Ramirez', '<EMAIL>', '************', 3, 1, NULL, '2025-04-28 09:30:27', '2025-05-01 21:12:23', NULL),
(19, 'aysa.ramirez', '1545', 'Aysa Ramirez', '<EMAIL>', '305-123-1345', 4, 1, '2025-05-01 03:35:03', '2025-04-28 09:30:48', '2025-05-01 21:12:25', NULL),
(21, 'luis.cantos', '3766', 'Luis Cantos', '<EMAIL>', '************', 2, 1, NULL, '2025-05-02 22:02:50', '2025-05-04 09:11:51', NULL),
(23, 'eduardo.luscinian.', '2412', 'Eduardo Luscinian ', '<EMAIL>', '************', 2, 1, NULL, '2025-05-02 22:05:37', '2025-05-04 10:38:50', NULL),
(24, 'carlos.de.la.torre', '3019', 'Carlos De La Torre', '<EMAIL>', '************', 3, 1, NULL, '2025-05-02 22:06:32', '2025-05-04 09:12:11', NULL),
(25, 'manuel.jimenez', '7927', 'Manuel Jimenez', '<EMAIL>', '************', 3, 1, NULL, '2025-05-02 22:07:24', '2025-05-04 09:12:16', NULL),
(26, 'maricel.jimenez', '6764', 'Maricel Jimenez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:07:56', '2025-05-04 09:12:20', NULL),
(28, 'ester.nuñez', '1268', 'Ester Nuñez', '<EMAIL>', '305-123-4123', 4, 1, NULL, '2025-05-02 22:09:04', '2025-05-04 09:12:38', NULL),
(29, 'esposa.cantos', '2985', 'Esmeralda Cantos', '<EMAIL>', '305-1345', 4, 1, NULL, '2025-05-02 22:09:51', '2025-05-04 09:12:43', NULL),
(30, 'cilene.cerda', '4156', 'Cilene Cerda', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:11:22', '2025-05-04 09:12:47', NULL),
(31, 'inez.lucinian', '8580', 'Inez Lucinian', '<EMAIL>', '305-123-1345', 4, 1, NULL, '2025-05-02 22:12:03', '2025-05-04 09:12:50', NULL),
(32, 'nancy.cruz', '5589', 'Nancy Cruz', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:13:03', '2025-05-04 09:12:54', NULL),
(33, 'hermana.collado', '1779', 'Adis Collado', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:13:34', '2025-05-04 09:13:06', NULL),
(34, 'hermana.marquetis', '7319', 'Yalexsis Marquetis', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:14:01', '2025-05-04 09:13:11', NULL),
(35, 'hermano.marquetis', '4685', 'Hermano Marquetis', '<EMAIL>', '305-123-1345', 4, 1, NULL, '2025-05-02 22:14:21', '2025-05-04 09:13:13', NULL),
(36, 'juana.gutierrez', '1758', 'Juana Gutierrez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:14:46', '2025-05-04 09:13:16', NULL),
(37, 'josefa.cejas', '7194', 'Josefa Cejas', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:15:14', '2025-05-04 09:13:20', NULL),
(38, 'belkis.perez', '6834', 'Belkis Perez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:15:53', '2025-05-04 09:13:23', NULL),
(39, 'yanina.gonzalez', '1409', 'Yanina Gonzalez', '<EMAIL>', '305-134-1234', 4, 1, NULL, '2025-05-02 22:16:23', '2025-05-04 09:13:26', NULL),
(40, 'juan.carlos.gonzalez', '8953', 'Juan Carlos Gonzalez', '<EMAIL>', '305-134-1234', 4, 1, NULL, '2025-05-02 22:16:47', '2025-05-04 09:13:29', NULL),
(41, 'antonela.gonzalez', '6835', 'Antonela Gonzalez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:17:18', '2025-05-04 09:13:34', NULL),
(42, 'marlon.rodriguez', '9568', 'Marlon Rodriguez', '<EMAIL>', '************', 3, 1, NULL, '2025-05-02 22:17:43', '2025-05-04 09:13:38', NULL),
(43, 'gema.rodriguez', '1912', 'Gema Rodriguez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:18:15', '2025-05-04 09:13:41', NULL),
(44, 'josefa.balbin', '9956', 'Josefa Balbin', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:18:46', '2025-05-04 09:13:45', NULL),
(45, 'natahrodriguez', '2502', 'Nathan Rodriguez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:19:20', '2025-05-04 09:14:15', NULL),
(46, 'pastor.rodriguez', '2422', 'Pastor Rodriguez', '<EMAIL>', '************', 2, 1, NULL, '2025-05-02 22:19:47', '2025-05-04 09:14:18', NULL),
(47, 'rudy.rodriguez', '5285', 'Rudy Rodriguez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:20:06', '2025-05-04 09:14:21', NULL),
(48, 'joennes.gil', '8403', 'Joennes Gil', '<EMAIL>', '305-123-2345', 4, 1, NULL, '2025-05-02 22:20:44', '2025-05-04 10:37:30', NULL),
(49, 'aniela.gil', '7576', 'Aniela Gil', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:21:11', '2025-05-04 10:37:33', NULL),
(50, 'william.gil', '6210', 'William Gil', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:21:29', '2025-05-04 10:37:35', NULL),
(51, 'natalia.gil', '3238', 'Natalia Gil', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:21:51', '2025-05-04 10:37:38', NULL),
(52, 'sheila.carballoza', '4400', 'Sheila Carballoza', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:25:12', '2025-05-04 10:37:40', NULL),
(53, 'carolina.corrales', '4825', 'Carolina Corrales', '<EMAIL>', '305-134-1234', 4, 1, NULL, '2025-05-02 22:25:43', '2025-05-04 10:37:42', NULL),
(54, 'hermano.arzola', '9559', 'Yosmel Arzola', '<EMAIL>', '305-123-1134', 4, 1, NULL, '2025-05-02 22:26:10', '2025-05-04 10:37:45', NULL),
(55, 'hermana.arzola', '5623', 'Naiquel Arzola', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:26:30', '2025-05-04 10:37:47', NULL),
(56, 'hijo.arzola', '2349', 'Yosmel Arzola Jr', '<EMAIL>', '305-123-1345', 4, 1, NULL, '2025-05-02 22:26:53', '2025-05-04 10:37:51', NULL),
(57, 'olga.cedeño', '6946', 'Olga Cedeño', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:27:29', '2025-05-04 10:37:56', NULL),
(58, 'natali.cedeño', '7226', 'Natali Cedeño', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:27:51', '2025-05-04 10:37:59', NULL),
(59, 'esposa.de.la.torre', '2261', 'Keren De La Torre', '<EMAIL>', '************', 4, 1, NULL, '2025-05-02 22:31:28', '2025-05-04 10:38:02', NULL),
(60, 'belkis', '8571', 'Belkis', '<EMAIL>', '************', 4, 1, NULL, '2025-05-03 06:19:55', '2025-05-04 10:38:04', NULL),
(61, 'mileidys.benites.', '2587', 'Mileidys Benites ', '<EMAIL>', '************', 4, 1, NULL, '2025-05-03 06:21:09', '2025-05-04 10:38:07', NULL),
(62, 'teresa.villa', '3362', 'Teresa Villa', '<EMAIL>', '************', 4, 1, NULL, '2025-05-03 06:21:34', '2025-05-04 10:38:11', NULL),
(63, 'laura.mama', '3999', 'Laura Mama', '<EMAIL>', '************', 4, 1, NULL, '2025-05-03 06:22:11', '2025-05-04 10:38:17', NULL),
(64, 'yasniel.rodriguez', '5867', 'Yasniel Rodriguez', '<EMAIL>', '************', 4, 1, NULL, '2025-05-03 07:02:36', '2025-05-04 10:38:20', NULL),
(65, 'luis.saumel.', '5682', 'Luis Saumel ', '<EMAIL>', '************', 4, 1, NULL, '2025-05-03 17:16:12', '2025-05-04 10:38:23', NULL),
(5130, 'fajardo.perez', '5188', 'Fajardo Perez', '<EMAIL>', '************', 2, 1, NULL, '2025-05-02 22:02:26', '2025-05-02 22:02:26', NULL),
(5132, 'omar.gutierrez', '7405', 'Omar Gutierrez', '<EMAIL>', '305-134-1345', 2, 1, NULL, '2025-05-02 22:03:31', '2025-05-02 22:03:31', NULL),
(5137, 'enrrique.nuñez', '6536', 'Enrrique Nuñez', '<EMAIL>', '************', 3, 1, NULL, '2025-05-02 22:08:30', '2025-05-02 22:08:30', NULL);
/*!40000 ALTER TABLE `members` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_assignments_view`
--

DROP TABLE IF EXISTS `midweek_assignments_view`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
undefined;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_assignments_view`
--

LOCK TABLES `midweek_assignments_view` WRITE;
/*!40000 ALTER TABLE `midweek_assignments_view` DISABLE KEYS */;
INSERT INTO `midweek_assignments_view` (`id`, `meeting_date`, `meeting_time`, `meeting_location`, `chairman_id`, `prayer_beginning_id`, `treasures_speaker_id`, `gems_presenter_id`, `bible_reading_id`, `first_ministry_student_id`, `first_ministry_assistant_id`, `second_ministry_student_id`, `second_ministry_assistant_id`, `ministry_speaker_id`, `christian_life_part_speaker_id`, `congregation_bible_study_id`, `prayer_end_id`, `audio_video_id`, `platform_id`, `microphone1_id`, `microphone2_id`, `opening_song_number`, `opening_song_title`, `middle_song_number`, `middle_song_title`, `closing_song_number`, `closing_song_title`, `bible_reading`, `cleaning_group`, `zoom_id`, `zoom_password`, `week_start_date`, `week_end_date`) VALUES (1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);
/*!40000 ALTER TABLE `midweek_assignments_view` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_attendance`
--

DROP TABLE IF EXISTS `midweek_attendance`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_attendance` (
  `id` int NOT NULL AUTO_INCREMENT,
  `meeting_id` int NOT NULL,
  `attendance_count` int NOT NULL DEFAULT '0',
  `recorded_by` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `meeting_id` (`meeting_id`),
  KEY `recorded_by` (`recorded_by`),
  CONSTRAINT `midweek_attendance_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `midweek_meetings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `midweek_attendance_ibfk_2` FOREIGN KEY (`recorded_by`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_attendance`
--

LOCK TABLES `midweek_attendance` WRITE;
/*!40000 ALTER TABLE `midweek_attendance` DISABLE KEYS */;
/*!40000 ALTER TABLE `midweek_attendance` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_assignments`
--

DROP TABLE IF EXISTS `midweek_meeting_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meeting_assignments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `part_id` int NOT NULL,
  `member_id` int DEFAULT NULL,
  `member_name` varchar(255) DEFAULT NULL,
  `assistant_id` int DEFAULT NULL,
  `assistant_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_part_id` (`part_id`),
  KEY `idx_member_id` (`member_id`),
  KEY `idx_assistant_id` (`assistant_id`),
  CONSTRAINT `midweek_meeting_assignments_ibfk_1` FOREIGN KEY (`part_id`) REFERENCES `midweek_meeting_parts` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_assignments`
--

LOCK TABLES `midweek_meeting_assignments` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_assignments` DISABLE KEYS */;
/*!40000 ALTER TABLE `midweek_meeting_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_parts`
--

DROP TABLE IF EXISTS `midweek_meeting_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meeting_parts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `meeting_id` int NOT NULL,
  `part_type` varchar(50) NOT NULL,
  `section` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `duration` int DEFAULT NULL,
  `description` text,
  `sort_order` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_meeting_id` (`meeting_id`),
  KEY `idx_part_type` (`part_type`),
  KEY `idx_section` (`section`),
  CONSTRAINT `midweek_meeting_parts_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `midweek_meetings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2793 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_parts`
--

LOCK TABLES `midweek_meeting_parts` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_parts` DISABLE KEYS */;
INSERT INTO `midweek_meeting_parts` (`id`, `meeting_id`, `part_type`, `section`, `title`, `duration`, `description`, `sort_order`, `created_at`, `updated_at`) VALUES (2023, 222, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2024, 222, 'treasures', 'treasures', 'Manténgase lejos de la inmoralidad sexual', 10, '', 1, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2025, 222, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2026, 222, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2027, 222, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2028, 222, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2029, 222, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2030, 222, 'ministry', 'ministry', 'Haga discípulos', 10, '', 3, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2031, 222, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2032, 222, 'living', 'christian_life', 'Canción 121', 10, '', 1, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2033, 222, 'living', 'christian_life', 'Haga todo lo posible por mantenerse casto durante el noviazgo', 10, '', 2, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2034, 222, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2035, 222, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 3 y oración', 3, '', 4, '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(2036, 223, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2037, 223, 'treasures', 'treasures', 'Sembraron con lágrimas, pero cosecharon con alegría', 10, '', 1, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2038, 223, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2039, 223, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2040, 223, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2041, 223, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2042, 223, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2043, 223, 'ministry', 'ministry', 'Haga discípulos', 10, '', 3, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2044, 223, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2045, 223, 'living', 'christian_life', 'Canción 155', 10, '', 1, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2046, 223, 'living', 'christian_life', 'Disfrute pensando en las promesas de Dios', 10, '', 2, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2047, 223, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2048, 223, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 58 y oración', 3, '', 4, '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(2049, 224, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2050, 224, 'treasures', 'treasures', 'Padres, sigan cuidando la herencia que Jehová les dio', 10, '', 1, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2051, 224, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2052, 224, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2053, 224, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2054, 224, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2055, 224, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2056, 224, 'ministry', 'ministry', 'Haga discípulos', 10, '', 3, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2057, 224, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2058, 224, 'living', 'christian_life', 'Canción 13', 10, '', 1, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2059, 224, 'living', 'christian_life', 'Padres, ¿están usando esta herramienta tan potente?', 10, '', 2, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2060, 224, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2061, 224, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 73 y oración', 3, '', 4, '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(2062, 225, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2063, 225, 'treasures', 'treasures', '“Nuestro Señor es más grande que todos los demás dioses”', 10, '', 1, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2064, 225, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2065, 225, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2066, 225, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2067, 225, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2068, 225, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2069, 225, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 3, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2070, 225, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2071, 225, 'living', 'christian_life', 'Canción 10', 10, '', 1, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2072, 225, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 2, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2073, 225, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2074, 225, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 90 y oración', 3, '', 4, '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(2075, 226, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2076, 226, 'treasures', 'treasures', '¿Qué hará después de orar?', 10, '', 1, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2077, 226, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2078, 226, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2079, 226, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2080, 226, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2081, 226, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2082, 226, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 3, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2083, 226, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2084, 226, 'living', 'christian_life', 'Canción 141', 10, '', 1, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2085, 226, 'living', 'christian_life', 'Esté preparado por si necesita atención médica o una intervención quirúrgica', 10, '', 2, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2086, 226, 'living', 'christian_life', '¿ESTÁ PREPARADO?', 10, '', 3, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2087, 226, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2088, 226, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 103 y oración', 3, '', 5, '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(2089, 227, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2090, 227, 'treasures', 'treasures', '“¡Feliz el pueblo que tiene por Dios a Jehová!”', 10, '', 1, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2091, 227, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2092, 227, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2093, 227, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2094, 227, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2095, 227, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2096, 227, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2097, 227, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2098, 227, 'living', 'christian_life', 'Canción 59', 10, '', 1, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2099, 227, 'living', 'christian_life', 'Jehová quiere que usted sea feliz', 10, '', 2, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2100, 227, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 3, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2101, 227, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2102, 227, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 85 y oración', 3, '', 5, '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(2103, 228, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2104, 228, 'treasures', 'treasures', 'Tenemos muchas razones para alabar a Jah', 10, '', 1, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2105, 228, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2106, 228, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2107, 228, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2108, 228, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2109, 228, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2110, 228, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2111, 228, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2112, 228, 'living', 'christian_life', 'Canción 159', 10, '', 1, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2113, 228, 'living', 'christian_life', 'Informe de servicio anual', 10, '', 2, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2114, 228, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2115, 228, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 37 y oración', 3, '', 4, '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(2116, 229, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2117, 229, 'treasures', 'treasures', 'Joven, ¿a quién escucharás?', 10, '', 1, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2118, 229, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2119, 229, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2120, 229, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2121, 229, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2122, 229, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:56:15', '2025-05-02 20:56:15');
INSERT INTO `midweek_meeting_parts` (`id`, `meeting_id`, `part_type`, `section`, `title`, `duration`, `description`, `sort_order`, `created_at`, `updated_at`) VALUES (2123, 229, 'ministry', 'ministry', 'Haga revisitas', 10, '', 3, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2124, 229, 'ministry', 'ministry', 'Haga discípulos', 10, '', 4, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2125, 229, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2126, 229, 'living', 'christian_life', 'Canción 89', 10, '', 1, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2127, 229, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 2, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2128, 229, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2129, 229, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 80 y oración', 3, '', 4, '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(2130, 230, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2131, 230, 'treasures', 'treasures', 'Por qué estudiar con ganas', 10, '', 1, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2132, 230, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2133, 230, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2134, 230, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2135, 230, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2136, 230, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2137, 230, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2138, 230, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2139, 230, 'living', 'christian_life', 'Canción 96', 10, '', 1, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2140, 230, 'living', 'christian_life', '¿Eres un cazatesoros?', 10, '', 2, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2141, 230, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2142, 230, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 102 y oración', 3, '', 4, '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(2143, 231, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2144, 231, 'treasures', 'treasures', 'Demuestre que confía en Jehová', 10, '', 1, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2145, 231, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2146, 231, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2147, 231, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2148, 231, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2149, 231, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2150, 231, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2151, 231, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2152, 231, 'living', 'christian_life', 'Canción 124', 10, '', 1, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2153, 231, 'living', 'christian_life', 'Demuestre que confía en la organización de Jehová', 10, '', 2, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2154, 231, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2155, 231, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 57 y oración', 3, '', 4, '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(2156, 232, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2157, 232, 'treasures', 'treasures', '“Protege tu corazón”', 10, '', 1, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2158, 232, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2159, 232, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2160, 232, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2161, 232, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2162, 232, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2163, 232, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 3, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2164, 232, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2165, 232, 'living', 'christian_life', 'Canción 16', 10, '', 1, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2166, 232, 'living', 'christian_life', 'Logros de la organización para el mes de marzo', 10, '', 2, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2167, 232, 'living', 'christian_life', 'El sábado 15 de marzo comienza la campaña de la Conmemoración', 10, '', 3, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2168, 232, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2169, 232, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 76 y oración', 3, '', 5, '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(2170, 233, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2171, 233, 'treasures', 'treasures', '¿Qué aprendemos de las hormigas?', 10, '', 1, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2172, 233, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2173, 233, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2174, 233, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2175, 233, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2176, 233, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2177, 233, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 3, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2178, 233, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2179, 233, 'living', 'christian_life', 'Canción 2', 10, '', 1, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2180, 233, 'living', 'christian_life', 'La creación demuestra que Jehová desea vernos felices. Los fascinantes animales', 10, '', 2, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2181, 233, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 3, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2182, 233, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2183, 233, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 126 y oración', 3, '', 5, '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(2184, 234, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2185, 234, 'treasures', 'treasures', 'Evitemos las situaciones peligrosas', 10, '', 1, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2186, 234, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2187, 234, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2188, 234, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2189, 234, 'ministry', 'ministry', 'Haga revisitas', 10, '', 1, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2190, 234, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2191, 234, 'ministry', 'ministry', 'Haga revisitas', 10, '', 3, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2192, 234, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2193, 234, 'living', 'christian_life', 'Canción 13', 10, '', 1, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2194, 234, 'living', 'christian_life', 'Un momento conveniente para tentarnos (Lu 4:6)', 10, '', 2, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2195, 234, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2196, 234, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 70 y oración', 3, '', 4, '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(2197, 235, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2198, 235, 'treasures', 'treasures', 'Escuchemos a la sabiduría personificada', 10, '', 1, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2199, 235, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2200, 235, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2201, 235, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2202, 235, 'ministry', 'ministry', 'Haga revisitas', 10, '', 1, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2203, 235, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2204, 235, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 3, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2205, 235, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2206, 235, 'living', 'christian_life', 'Canción 105', 10, '', 1, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2207, 235, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 2, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2208, 235, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2209, 235, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 7 y oración', 3, '', 4, '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(2210, 236, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2211, 236, 'treasures', 'treasures', 'No sea un burlón, sea sabio', 10, '', 1, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2212, 236, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2213, 236, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2214, 236, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2215, 236, 'ministry', 'ministry', 'Haga revisitas', 10, '', 1, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2216, 236, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2217, 236, 'ministry', 'ministry', 'Haga revisitas', 10, '', 3, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2218, 236, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2219, 236, 'living', 'christian_life', 'Canción 84', 10, '', 1, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2220, 236, 'living', 'christian_life', '¿Somos privilegiados por tener privilegios?', 10, '', 2, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2221, 236, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(2222, 236, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 42 y oración', 3, '', 4, '2025-05-02 20:58:18', '2025-05-02 20:58:18');
INSERT INTO `midweek_meeting_parts` (`id`, `meeting_id`, `part_type`, `section`, `title`, `duration`, `description`, `sort_order`, `created_at`, `updated_at`) VALUES (2223, 237, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2224, 237, 'treasures', 'treasures', '¿Qué nos hace verdaderamente ricos?', 10, '', 1, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2225, 237, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2226, 237, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2227, 237, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2228, 237, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2229, 237, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2230, 237, 'ministry', 'ministry', 'Haga revisitas', 10, '', 3, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2231, 237, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2232, 237, 'living', 'christian_life', 'Canción 111', 10, '', 1, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2233, 237, 'living', 'christian_life', '¿Qué bendiciones enriquecen a los siervos de Dios?', 10, '', 2, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2234, 237, 'living', 'christian_life', '2025 | Informe sobre la actividad del Departamento Local de Diseño y Construcción', 10, '', 3, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2235, 237, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2236, 237, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 115 y oración', 3, '', 5, '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(2251, 239, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2252, 239, 'treasures', 'treasures', '¡No lo diga!', 10, '', 1, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2253, 239, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2254, 239, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2255, 239, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2256, 239, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2257, 239, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2258, 239, 'ministry', 'ministry', 'Haga discípulos', 10, '', 3, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2259, 239, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2260, 239, 'living', 'christian_life', 'Canción 157', 10, '', 1, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2261, 239, 'living', 'christian_life', 'Que su lengua no destruya la paz', 10, '', 2, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2262, 239, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2263, 239, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción de la asamblea regional del 2025 y oración', 3, '', 4, '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(2533, 259, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2534, 259, 'treasures', 'treasures', 'El trabajo duro tiene su recompensa', 10, '', 1, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2535, 259, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2536, 259, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2537, 259, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2538, 259, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2539, 259, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2540, 259, 'ministry', 'ministry', 'Haga revisitas', 10, '', 3, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2541, 259, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 4, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2542, 259, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2543, 259, 'living', 'christian_life', 'Canción 21', 10, '', 1, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2544, 259, 'living', 'christian_life', 'Afronte los problemas económicos con la ayuda de Jehová', 10, '', 2, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2545, 259, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2546, 259, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 57 y oración', 3, '', 4, '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(2630, 267, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2631, 267, 'treasures', 'treasures', 'Que “la lámpara de los malvados” no lo deslumbre', 10, '', 1, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2632, 267, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2633, 267, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2634, 267, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2635, 267, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2636, 267, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2637, 267, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2638, 267, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2639, 267, 'living', 'christian_life', 'Canción 77', 10, '', 1, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2640, 267, 'living', 'christian_life', '“La luz de los justos brilla intensamente”', 10, '', 2, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2641, 267, 'living', 'christian_life', 'SUGERENCIA PARA LA ADORACIÓN EN FAMILIA:', 10, '', 3, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2642, 267, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 4, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2643, 267, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 5, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2644, 267, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 43 y oración', 3, '', 6, '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(2659, 269, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2660, 269, 'treasures', 'treasures', 'Ayude a los demás a tener un corazón alegre', 10, '', 1, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2661, 269, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2662, 269, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2663, 269, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2664, 269, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2665, 269, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2666, 269, 'ministry', 'ministry', 'Haga discípulos', 10, '', 3, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2667, 269, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2668, 269, 'living', 'christian_life', 'Canción 155', 10, '', 1, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2669, 269, 'living', 'christian_life', 'Podemos tener un corazón alegre a pesar de las pruebas', 10, '', 2, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2670, 269, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2671, 269, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 100 y oración', 3, '', 4, '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(2672, 270, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2673, 270, 'treasures', 'treasures', 'Tres preguntas que lo ayudarán a tomar buenas decisiones', 10, '', 1, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2674, 270, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2675, 270, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2676, 270, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2677, 270, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2678, 270, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2679, 270, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2680, 270, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2681, 270, 'living', 'christian_life', 'Canción 32', 10, '', 1, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2682, 270, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 2, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2683, 270, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2684, 270, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 68 y oración', 3, '', 4, '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(2685, 271, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2686, 271, 'treasures', 'treasures', 'Cómo tener paz en su matrimonio', 10, '', 1, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2687, 271, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2688, 271, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2689, 271, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2690, 271, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2691, 271, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2692, 271, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2693, 271, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2694, 271, 'living', 'christian_life', 'Canción 113', 10, '', 1, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2695, 271, 'living', 'christian_life', 'Cómo promover la buena comunicación', 10, '', 2, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2696, 271, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2697, 271, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 35 y oración', 3, '', 4, '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(2698, 272, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2699, 272, 'treasures', 'treasures', 'Anime con sus palabras a quienes tienen problemas de salud', 10, '', 1, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2700, 272, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2701, 272, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2702, 272, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 08:28:06', '2025-05-14 08:28:06');
INSERT INTO `midweek_meeting_parts` (`id`, `meeting_id`, `part_type`, `section`, `title`, `duration`, `description`, `sort_order`, `created_at`, `updated_at`) VALUES (2703, 272, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2704, 272, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2705, 272, 'ministry', 'ministry', 'Haga revisitas', 10, '', 3, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2706, 272, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 4, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2707, 272, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2708, 272, 'living', 'christian_life', 'Canción 144', 10, '', 1, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2709, 272, 'living', 'christian_life', 'Ayudemos “sin una palabra” a nuestros seres queridos a acercarse a Jehová', 10, '', 2, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2710, 272, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2711, 272, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 60 y oración', 3, '', 4, '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(2712, 273, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2713, 273, 'treasures', 'treasures', 'Sea un auténtico amigo para los hermanos', 10, '', 1, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2714, 273, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2715, 273, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2716, 273, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2717, 273, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2718, 273, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2719, 273, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2720, 273, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2721, 273, 'living', 'christian_life', 'Canción 40', 10, '', 1, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2722, 273, 'living', 'christian_life', 'Necesidades de la congregación', 10, '', 2, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2723, 273, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2724, 273, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 61 y oración', 3, '', 4, '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(2725, 274, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2726, 274, 'treasures', 'treasures', 'Claves para que el noviazgo cumpla su objetivo', 10, '', 1, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2727, 274, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2728, 274, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2729, 274, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2730, 274, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2731, 274, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2732, 274, 'ministry', 'ministry', 'Explique sus creencias', 10, '', 3, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2733, 274, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2734, 274, 'living', 'christian_life', 'Canción 78', 10, '', 1, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2735, 274, 'living', 'christian_life', 'Anime a la gente a probar nuestro curso de la Biblia', 10, '', 2, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2736, 274, 'living', 'christian_life', 'Logros de la organización para el mes de junio', 10, '', 3, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2737, 274, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2738, 274, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 150 y oración', 3, '', 5, '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(2739, 275, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2740, 275, 'treasures', 'treasures', 'Ante una situación de emergencia, mida bien sus pasos', 10, '', 1, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2741, 275, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2742, 275, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2743, 275, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2744, 275, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2745, 275, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2746, 275, 'ministry', 'ministry', 'Haga discípulos', 10, '', 3, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2747, 275, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2748, 275, 'living', 'christian_life', 'Canción 126', 10, '', 1, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2749, 275, 'living', 'christian_life', 'Esté preparado para una situación de emergencia', 10, '', 2, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2750, 275, 'living', 'christian_life', 'PREGÚNTESE:', 10, '', 3, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2751, 275, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 4, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2752, 275, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 116 y oración', 3, '', 5, '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(2753, 276, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2754, 276, 'treasures', 'treasures', 'Sabios consejos para criar a los hijos', 10, '', 1, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2755, 276, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2756, 276, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2757, 276, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2758, 276, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2759, 276, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 2, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2760, 276, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2761, 276, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2762, 276, 'living', 'christian_life', 'Canción 134', 10, '', 1, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2763, 276, 'living', 'christian_life', 'Sean pacientes, pero no permisivos', 10, '', 2, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2764, 276, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2765, 276, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 6 y oración', 3, '', 4, '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(2766, 277, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2767, 277, 'treasures', 'treasures', 'Cómo nos beneficia tener amigos verdaderos', 10, '', 1, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2768, 277, 'treasures', 'treasures', 'Busquemos perlas escondidas', 10, '', 2, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2769, 277, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 3, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2770, 277, 'ministry', 'ministry', 'Seamos Mejores Maestros', 15, '', 0, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2771, 277, 'ministry', 'ministry', 'Empiece conversaciones', 10, '', 1, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2772, 277, 'ministry', 'ministry', 'Haga revisitas', 10, '', 2, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2773, 277, 'ministry', 'ministry', 'Discurso', 10, '', 3, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2774, 277, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2775, 277, 'living', 'christian_life', 'Canción 109', 10, '', 1, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2776, 277, 'living', 'christian_life', '“Un hermano en tiempos de angustia”', 10, '', 2, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2777, 277, 'living', 'christian_life', 'Estudio bíblico de la congregación', 10, '', 3, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2778, 277, 'living', 'christian_life', 'Palabras de conclusión (3 mins.) | Canción 118 y oración', 3, '', 4, '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(2779, 278, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2780, 278, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 1, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2781, 278, 'ministry', 'ministry', 'Primera conversación', 2, '', 0, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2782, 278, 'ministry', 'ministry', 'Revisita', 4, '', 1, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2783, 278, 'ministry', 'ministry', 'Curso bíblico', 6, '', 2, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2784, 278, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2785, 278, 'living', 'christian_life', 'Estudio bíblico de la congregación', 30, '', 1, '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(2786, 279, 'treasures', 'treasures', 'Tesoros de la Biblia', 10, '', 0, '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(2787, 279, 'bible_reading', 'treasures', 'Lectura de la Biblia', 4, '', 1, '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(2788, 279, 'ministry', 'ministry', 'Primera conversación', 2, '', 0, '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(2789, 279, 'ministry', 'ministry', 'Revisita', 4, '', 1, '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(2790, 279, 'ministry', 'ministry', 'Curso bíblico', 6, '', 2, '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(2791, 279, 'living', 'christian_life', 'Nuestra Vida Cristiana', 15, '', 0, '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(2792, 279, 'living', 'christian_life', 'Estudio bíblico de la congregación', 30, '', 1, '2025-05-14 17:55:23', '2025-05-14 17:55:23');
/*!40000 ALTER TABLE `midweek_meeting_parts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_settings`
--

DROP TABLE IF EXISTS `midweek_meeting_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meeting_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `congregation_id` int NOT NULL,
  `default_day_of_week` tinyint NOT NULL DEFAULT '5',
  `default_time` time NOT NULL DEFAULT '19:30:00',
  `default_location` varchar(255) NOT NULL DEFAULT 'Salón del Reino',
  `default_zoom_meeting_id` varchar(255) DEFAULT NULL,
  `default_zoom_meeting_password` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_congregation` (`congregation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=438 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_settings`
--

LOCK TABLES `midweek_meeting_settings` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_settings` DISABLE KEYS */;
INSERT INTO `midweek_meeting_settings` (`id`, `congregation_id`, `default_day_of_week`, `default_time`, `default_location`, `default_zoom_meeting_id`, `default_zoom_meeting_password`, `created_at`, `updated_at`) VALUES (1, 1, 5, '19:30:00', 'Salón del Reino', NULL, NULL, '2025-04-28 19:06:26', '2025-04-28 19:06:26');
/*!40000 ALTER TABLE `midweek_meeting_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_songs`
--

DROP TABLE IF EXISTS `midweek_meeting_songs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meeting_songs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `meeting_id` int NOT NULL,
  `song_number` int NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `display_order` int NOT NULL,
  `section` varchar(50) DEFAULT '',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `meeting_id` (`meeting_id`),
  CONSTRAINT `midweek_meeting_songs_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `midweek_meetings` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=406 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_songs`
--

LOCK TABLES `midweek_meeting_songs` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_songs` DISABLE KEYS */;
INSERT INTO `midweek_meeting_songs` (`id`, `meeting_id`, `song_number`, `title`, `display_order`, `section`, `created_at`, `updated_at`) VALUES (235, 222, 122, '122 - ¡Mantengámonos firmes, inmovibles!', 0, '', '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(236, 222, 121, '121 - Necesitamos autodominio', 1, '', '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(237, 222, 3, '3 - Tú me das fuerza, confianza y valor', 2, '', '2025-05-02 20:54:12', '2025-05-02 20:54:12'),
(238, 223, 144, '144 - No dejes de mirar allí', 0, '', '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(239, 223, 155, '155 - Mi mayor felicidad', 1, '', '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(240, 223, 58, '58 - Voy a buscar a los amigos de la paz', 2, '', '2025-05-02 20:54:21', '2025-05-02 20:54:21'),
(241, 224, 134, '134 - Los hijos son un regalo de Dios', 0, '', '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(242, 224, 13, '13 - Cristo es nuestro modelo', 1, '', '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(243, 224, 73, '73 - Danos fuerzas y valor', 2, '', '2025-05-02 20:54:30', '2025-05-02 20:54:30'),
(244, 225, 2, '2 - Tu nombre es Jehová', 0, '', '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(245, 225, 10, '10 - ¡Alabemos a nuestro Dios, Jehová!', 1, '', '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(246, 225, 90, '90 - Animémonos unos a otros', 2, '', '2025-05-02 20:54:39', '2025-05-02 20:54:39'),
(247, 226, 44, '44 - Una súplica ferviente', 0, '', '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(248, 226, 141, '141 - El maravilloso regalo de la vida', 1, '', '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(249, 226, 103, '103 - Nuestros pastores son un regalo de Dios', 2, '', '2025-05-02 20:55:05', '2025-05-02 20:55:05'),
(250, 227, 145, '145 - Dios prometió un Paraíso', 0, '', '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(251, 227, 59, '59 - Ven a alabar a Jehová', 1, '', '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(252, 227, 85, '85 - “Recíbanse con gusto unos a otros”', 2, '', '2025-05-02 20:55:36', '2025-05-02 20:55:36'),
(253, 228, 12, '12 - Jehová, nuestro gran Dios', 0, '', '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(254, 228, 159, '159 - Demos gloria a Jehová', 1, '', '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(255, 228, 37, '37 - Serviré a Jehová con el corazón', 2, '', '2025-05-02 20:55:48', '2025-05-02 20:55:48'),
(256, 229, 88, '88 - Hazme conocer tus caminos', 0, '', '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(257, 229, 89, '89 - Jehová bendice al que escucha y obedece', 1, '', '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(258, 229, 80, '80 - “Prueben y vean que Jehová es bueno”', 2, '', '2025-05-02 20:56:15', '2025-05-02 20:56:15'),
(259, 230, 35, '35 - Asegurémonos de lo más importante', 0, '', '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(260, 230, 96, '96 - El libro de Dios es un tesoro', 1, '', '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(261, 230, 102, '102 - Ayudemos a los débiles', 2, '', '2025-05-02 20:56:25', '2025-05-02 20:56:25'),
(262, 231, 8, '8 - Jehová es mi Refugio', 0, '', '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(263, 231, 124, '124 - Siempre fieles y leales', 1, '', '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(264, 231, 57, '57 - Predicamos a toda clase de personas', 2, '', '2025-05-02 20:56:35', '2025-05-02 20:56:35'),
(265, 232, 36, '36 - Cuidemos nuestro corazón', 0, '', '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(266, 232, 16, '16 - Alabemos a Jehová por su Hijo, el Ungido', 1, '', '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(267, 232, 76, '76 - Cuéntame lo que sientes', 2, '', '2025-05-02 20:57:06', '2025-05-02 20:57:06'),
(268, 233, 11, '11 - La creación alaba a Jehová', 0, '', '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(269, 233, 2, '2 - Tu nombre es Jehová', 1, '', '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(270, 233, 126, '126 - Siempre fuertes, fieles y firmes', 2, '', '2025-05-02 20:57:26', '2025-05-02 20:57:26'),
(271, 234, 34, '34 - Caminaré en integridad', 0, '', '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(272, 234, 13, '13 - Cristo es nuestro modelo', 1, '', '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(273, 234, 70, '70 - Busquemos a los merecedores', 2, '', '2025-05-02 20:57:39', '2025-05-02 20:57:39'),
(274, 235, 89, '89 - Jehová bendice al que escucha y obedece', 0, '', '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(275, 235, 105, '105 - “Dios es amor”', 1, '', '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(276, 235, 7, '7 - Jehová es mi fuerza y mi salvación', 2, '', '2025-05-02 20:58:06', '2025-05-02 20:58:06'),
(277, 236, 56, '56 - Vive la verdad', 0, '', '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(278, 236, 84, '84 - Servimos donde se nos necesite', 1, '', '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(279, 236, 42, '42 - La oración del siervo de Dios', 2, '', '2025-05-02 20:58:18', '2025-05-02 20:58:18'),
(280, 237, 76, '76 - Cuéntame lo que sientes', 0, '', '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(281, 237, 111, '111 - Los motivos de nuestro gozo', 1, '', '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(282, 237, 115, '115 - Gratitud por la paciencia de Dios', 2, '', '2025-05-02 20:58:26', '2025-05-02 20:58:26'),
(286, 239, 90, '90 - Animémonos unos a otros', 0, '', '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(287, 239, 157, '157 - Solo paz', 1, '', '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(288, 239, 0, 'Canción de cierre', 2, '', '2025-05-02 21:29:45', '2025-05-02 21:29:45'),
(346, 259, 101, '101 - Sirvamos a Dios en unidad', 0, '', '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(347, 259, 21, '21 - Busquemos primero el Reino', 1, '', '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(348, 259, 57, '57 - Predicamos a toda clase de personas', 2, '', '2025-05-06 03:31:20', '2025-05-06 03:31:20'),
(367, 267, 34, '34 - Caminaré en integridad', 0, '', '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(368, 267, 77, '77 - Luz en un mundo oscuro', 1, '', '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(369, 267, 43, '43 - Una oración para dar gracias a Dios', 2, '', '2025-05-14 07:58:57', '2025-05-14 07:58:57'),
(373, 269, 102, '102 - Ayudemos a los débiles', 0, '', '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(374, 269, 155, '155 - Mi mayor felicidad', 1, '', '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(375, 269, 100, '100 - Seamos hospitalarios', 2, '', '2025-05-14 07:59:52', '2025-05-14 07:59:52'),
(376, 270, 36, '36 - Cuidemos nuestro corazón', 0, '', '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(377, 270, 32, '32 - ¡Ponte de parte de Dios!', 1, '', '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(378, 270, 68, '68 - Sembremos semillas del Reino', 2, '', '2025-05-14 08:00:57', '2025-05-14 08:00:57'),
(379, 271, 157, '157 - Solo paz', 0, '', '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(380, 271, 113, '113 - La paz del pueblo de Dios', 1, '', '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(381, 271, 35, '35 - Asegurémonos de lo más importante', 2, '', '2025-05-14 08:01:33', '2025-05-14 08:01:33'),
(382, 272, 90, '90 - Animémonos unos a otros', 0, '', '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(383, 272, 144, '144 - No dejes de mirar allí', 1, '', '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(384, 272, 60, '60 - Hay vidas en juego', 2, '', '2025-05-14 08:28:06', '2025-05-14 08:28:06'),
(385, 273, 154, '154 - Un amor sin final', 0, '', '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(386, 273, 40, '40 - ¿A quién servimos?', 1, '', '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(387, 273, 61, '61 - ¡Avancen, Testigos!', 2, '', '2025-05-14 08:28:27', '2025-05-14 08:28:27'),
(388, 274, 131, '131 - “Lo que Dios ha unido”', 0, '', '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(389, 274, 78, '78 - Enseñemos la Palabra de Dios', 1, '', '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(390, 274, 150, '150 - ¡Jehová será tu Salvador!', 2, '', '2025-05-14 08:28:47', '2025-05-14 08:28:47'),
(391, 275, 89, '89 - Jehová bendice al que escucha y obedece', 0, '', '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(392, 275, 126, '126 - Siempre fuertes, fieles y firmes', 1, '', '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(393, 275, 116, '116 - Seamos amables y bondadosos', 2, '', '2025-05-14 08:43:39', '2025-05-14 08:43:39'),
(394, 276, 79, '79 - Que sigan firmes en la fe', 0, '', '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(395, 276, 134, '134 - Los hijos son un regalo de Dios', 1, '', '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(396, 276, 6, '6 - Los cielos proclaman la gloria de Dios', 2, '', '2025-05-14 11:09:27', '2025-05-14 11:09:27'),
(397, 277, 102, '102 - Ayudemos a los débiles', 0, '', '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(398, 277, 109, '109 - Amémonos de todo corazón', 1, '', '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(399, 277, 118, '118 - “Danos más fe”', 2, '', '2025-05-14 11:12:27', '2025-05-14 11:12:27'),
(400, 278, 0, 'Canción de apertura', 0, '', '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(401, 278, 0, 'Canción intermedia', 1, '', '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(402, 278, 0, 'Canción de cierre', 2, '', '2025-05-14 17:48:01', '2025-05-14 17:48:01'),
(403, 279, 0, 'Canción de apertura', 0, '', '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(404, 279, 0, 'Canción intermedia', 1, '', '2025-05-14 17:55:23', '2025-05-14 17:55:23'),
(405, 279, 0, 'Canción de cierre', 2, '', '2025-05-14 17:55:23', '2025-05-14 17:55:23');
/*!40000 ALTER TABLE `midweek_meeting_songs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_view`
--

DROP TABLE IF EXISTS `midweek_meeting_view`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
undefined;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_view`
--

LOCK TABLES `midweek_meeting_view` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_view` DISABLE KEYS */;
INSERT INTO `midweek_meeting_view` (`id`, `week_start`, `week_end`, `workbook_id`, `week_start_date`, `week_end_date`, `theme`, `url_path`, `chairman_id`, `prayer_beginning_id`, `prayer_end_id`, `meeting_date`, `meeting_time`, `meeting_location`, `zoom_link`, `zoom_id`, `zoom_password`, `status`, `created_at`, `updated_at`, `opening_song_number`, `opening_song_title`, `middle_song_number`, `middle_song_title`, `closing_song_number`, `closing_song_title`, `treasures_speaker_id`, `gems_presenter_id`, `bible_reading_id`, `bible_reading`, `first_ministry_student_id`, `first_ministry_assistant_id`, `second_ministry_student_id`, `second_ministry_assistant_id`, `ministry_speaker_id`, `christian_life_part_speaker_id`, `congregation_bible_study_id`, `audio_video_id`, `platform_id`, `microphone1_id`, `microphone2_id`, `cleaning_group`, `chairman_id_from_parts`, `prayer_beginning_id_from_parts`, `prayer_end_id_from_parts`, `treasures_speaker_id_from_parts`, `gems_presenter_id_from_parts`, `bible_reading_id_from_parts`, `first_ministry_student_id_from_parts`, `first_ministry_assistant_id_from_parts`, `second_ministry_student_id_from_parts`, `second_ministry_assistant_id_from_parts`, `ministry_speaker_id_from_parts`, `christian_life_part_speaker_id_from_parts`, `congregation_bible_study_id_from_parts`, `audio_video_id_from_parts`, `platform_id_from_parts`, `microphone1_id_from_parts`, `microphone2_id_from_parts`) VALUES (1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1);
/*!40000 ALTER TABLE `midweek_meeting_view` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_weeks`
--

DROP TABLE IF EXISTS `midweek_meeting_weeks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meeting_weeks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `workbook_id` varchar(50) NOT NULL,
  `week_id` varchar(100) NOT NULL,
  `title` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `url` varchar(255) NOT NULL,
  `content` longtext,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_week_id` (`week_id`),
  KEY `idx_workbook_id` (`workbook_id`),
  CONSTRAINT `midweek_meeting_weeks_ibfk_1` FOREIGN KEY (`workbook_id`) REFERENCES `midweek_meeting_workbooks` (`workbook_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_weeks`
--

LOCK TABLES `midweek_meeting_weeks` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_weeks` DISABLE KEYS */;
/*!40000 ALTER TABLE `midweek_meeting_weeks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meeting_workbooks`
--

DROP TABLE IF EXISTS `midweek_meeting_workbooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meeting_workbooks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `workbook_id` varchar(50) NOT NULL,
  `title` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `url` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_workbook_id` (`workbook_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meeting_workbooks`
--

LOCK TABLES `midweek_meeting_workbooks` WRITE;
/*!40000 ALTER TABLE `midweek_meeting_workbooks` DISABLE KEYS */;
/*!40000 ALTER TABLE `midweek_meeting_workbooks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_meetings`
--

DROP TABLE IF EXISTS `midweek_meetings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_meetings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `week_start` datetime DEFAULT NULL,
  `week_end` datetime DEFAULT NULL,
  `workbook_id` int NOT NULL,
  `week_start_date` date NOT NULL,
  `week_end_date` date NOT NULL,
  `theme` varchar(255) NOT NULL,
  `url_path` varchar(255) NOT NULL,
  `chairman_id` int DEFAULT NULL,
  `prayer_beginning_id` int DEFAULT NULL,
  `prayer_end_id` int DEFAULT NULL,
  `meeting_date` date DEFAULT NULL,
  `meeting_time` time DEFAULT '19:00:00',
  `meeting_location` varchar(255) DEFAULT NULL,
  `zoom_link` varchar(255) DEFAULT NULL,
  `zoom_id` varchar(50) DEFAULT NULL,
  `zoom_password` varchar(50) DEFAULT NULL,
  `status` enum('draft','scheduled','completed') DEFAULT 'draft',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `opening_song_number` varchar(10) DEFAULT NULL,
  `opening_song_title` varchar(255) DEFAULT NULL,
  `middle_song_number` varchar(10) DEFAULT NULL,
  `middle_song_title` varchar(255) DEFAULT NULL,
  `closing_song_number` varchar(10) DEFAULT NULL,
  `closing_song_title` varchar(255) DEFAULT NULL,
  `treasures_speaker_id` int DEFAULT NULL,
  `gems_presenter_id` int DEFAULT NULL,
  `bible_reading_id` int DEFAULT NULL,
  `bible_reading` varchar(255) DEFAULT NULL,
  `first_ministry_student_id` int DEFAULT NULL,
  `first_ministry_assistant_id` int DEFAULT NULL,
  `second_ministry_student_id` int DEFAULT NULL,
  `second_ministry_assistant_id` int DEFAULT NULL,
  `ministry_speaker_id` int DEFAULT NULL,
  `christian_life_part_speaker_id` int DEFAULT NULL,
  `congregation_bible_study_id` int DEFAULT NULL,
  `audio_video_id` int DEFAULT NULL,
  `platform_id` int DEFAULT NULL,
  `microphone1_id` int DEFAULT NULL,
  `microphone2_id` int DEFAULT NULL,
  `cleaning_group` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `week_start_date` (`week_start_date`),
  KEY `workbook_id` (`workbook_id`),
  KEY `chairman_id` (`chairman_id`),
  KEY `prayer_beginning_id_fk` (`prayer_beginning_id`),
  KEY `prayer_end_id_fk` (`prayer_end_id`),
  CONSTRAINT `midweek_meetings_ibfk_1` FOREIGN KEY (`workbook_id`) REFERENCES `midweek_workbooks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `midweek_meetings_ibfk_2` FOREIGN KEY (`chairman_id`) REFERENCES `members` (`id`) ON DELETE SET NULL,
  CONSTRAINT `prayer_beginning_id_fk` FOREIGN KEY (`prayer_beginning_id`) REFERENCES `members` (`id`),
  CONSTRAINT `prayer_end_id_fk` FOREIGN KEY (`prayer_end_id`) REFERENCES `members` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=280 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_meetings`
--

LOCK TABLES `midweek_meetings` WRITE;
/*!40000 ALTER TABLE `midweek_meetings` DISABLE KEYS */;
INSERT INTO `midweek_meetings` (`id`, `week_start`, `week_end`, `workbook_id`, `week_start_date`, `week_end_date`, `theme`, `url_path`, `chairman_id`, `prayer_beginning_id`, `prayer_end_id`, `meeting_date`, `meeting_time`, `meeting_location`, `zoom_link`, `zoom_id`, `zoom_password`, `status`, `created_at`, `updated_at`, `opening_song_number`, `opening_song_title`, `middle_song_number`, `middle_song_title`, `closing_song_number`, `closing_song_title`, `treasures_speaker_id`, `gems_presenter_id`, `bible_reading_id`, `bible_reading`, `first_ministry_student_id`, `first_ministry_assistant_id`, `second_ministry_student_id`, `second_ministry_assistant_id`, `ministry_speaker_id`, `christian_life_part_speaker_id`, `congregation_bible_study_id`, `audio_video_id`, `platform_id`, `microphone1_id`, `microphone2_id`, `cleaning_group`) VALUES (222, NULL, NULL, 1, '2025-03-17 04:00:00', '2025-03-23 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, 1, 1, '2025-03-21 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:54:12', '2025-05-03 09:06:46', '61', 'Test Opening Song 61', '49', 'Test Middle Song 49', '17', 'Test Closing Song 17', 1, NULL, 1, NULL, NULL, NULL, NULL, NULL, 1, 1, NULL, NULL, NULL, NULL, NULL, NULL),
(223, NULL, NULL, 1, '2025-12-30 05:00:00', '2025-01-05 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-01-03 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:54:21', '2025-05-02 20:54:21', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(224, NULL, NULL, 1, '2025-01-06 05:00:00', '2025-01-12 05:00:00', 'Vida y Ministerio Cristianos', '', 1, 1, 6, '2025-01-10 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:54:30', '2025-05-03 07:41:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 2, NULL, NULL, NULL, NULL, NULL, NULL, 7, 6, NULL, NULL, NULL, NULL, NULL),
(225, NULL, NULL, 1, '2025-01-13 05:00:00', '2025-01-19 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-01-17 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:54:39', '2025-05-02 20:54:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(226, NULL, NULL, 1, '2025-01-27 05:00:00', '2025-02-02 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-01-31 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:55:05', '2025-05-02 20:55:05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(227, NULL, NULL, 1, '2025-02-03 05:00:00', '2025-02-09 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-02-07 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:55:36', '2025-05-02 20:55:36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(228, NULL, NULL, 1, '2025-02-10 05:00:00', '2025-02-16 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-02-14 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:55:48', '2025-05-02 20:55:48', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(229, NULL, NULL, 1, '2025-02-17 05:00:00', '2025-02-23 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-02-21 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:56:15', '2025-05-02 20:56:15', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(230, NULL, NULL, 1, '2025-02-24 05:00:00', '2025-03-02 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-02-28 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:56:25', '2025-05-02 20:56:25', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(231, NULL, NULL, 1, '2025-03-03 05:00:00', '2025-03-09 05:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-03-07 05:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:56:35', '2025-05-02 20:56:35', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(232, NULL, NULL, 1, '2025-03-10 04:00:00', '2025-03-16 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-03-14 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:57:06', '2025-05-02 20:57:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(233, NULL, NULL, 1, '2025-03-24 04:00:00', '2025-03-30 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-03-28 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:57:26', '2025-05-02 20:57:26', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(234, NULL, NULL, 1, '2025-03-31 04:00:00', '2025-04-06 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-04-04 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:57:39', '2025-05-02 20:57:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(235, NULL, NULL, 1, '2025-04-07 04:00:00', '2025-04-13 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-04-11 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:58:06', '2025-05-02 20:58:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(236, NULL, NULL, 1, '2025-04-14 04:00:00', '2025-04-20 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-04-18 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:58:18', '2025-05-02 20:58:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(237, NULL, NULL, 1, '2025-04-21 04:00:00', '2025-04-27 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-04-25 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-02 20:58:26', '2025-05-02 20:58:26', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(239, NULL, NULL, 1, '2025-04-28 04:00:00', '2025-05-04 04:00:00', 'Vida y Ministerio Cristianos', '', 2, 2, 1, '2025-05-02 04:00:00', '19:30:00', 'kingdom_hall', '', '', '', 'draft', '2025-05-02 21:29:45', '2025-05-03 07:25:58', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(259, NULL, NULL, 1, '2025-05-05 04:00:00', '2025-05-11 04:00:00', 'Vida y Ministerio Cristianos', '', 5130, 5130, 5137, '2025-05-09 04:00:00', '19:30:00', 'kingdom_hall', NULL, NULL, NULL, 'draft', '2025-05-06 03:31:20', '2025-05-06 09:50:29', NULL, NULL, NULL, NULL, NULL, NULL, 11, 24, 45, NULL, 32, 33, 19, 5, 5137, 46, 9, 14, 15, 11, 21, '4'),
(267, NULL, NULL, 1, '2025-05-12 04:00:00', '2025-05-18 04:00:00', 'Vida y Ministerio Cristianos', '', 5132, 5132, 5130, '2025-05-16 04:00:00', '19:30:00', 'kingdom_hall', NULL, NULL, NULL, 'draft', '2025-05-14 07:58:57', '2025-05-14 09:09:16', NULL, NULL, NULL, NULL, NULL, NULL, 23, 18, 50, NULL, 38, 12, 19, 32, 24, NULL, 46, NULL, NULL, NULL, NULL, NULL),
(269, NULL, NULL, 1, '2025-05-26 04:00:00', '2025-06-01 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-05-30 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 07:59:52', '2025-05-14 07:59:52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(270, NULL, NULL, 1, '2025-06-02 04:00:00', '2025-06-08 04:00:00', 'Vida y Ministerio Cristianos', '', 2, 2, 5130, '2025-06-06 04:00:00', '19:30:00', 'kingdom_hall', NULL, NULL, NULL, 'draft', '2025-05-14 08:00:57', '2025-06-01 19:21:58', NULL, NULL, NULL, NULL, NULL, NULL, 7, 18, 45, NULL, 16, 33, 28, 37, 24, 5132, 21, 56, 3, 50, 64, '5'),
(271, NULL, NULL, 1, '2025-06-09 04:00:00', '2025-06-15 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-06-13 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 08:01:33', '2025-05-14 08:01:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(272, NULL, NULL, 1, '2025-06-16 04:00:00', '2025-06-22 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-06-20 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 08:28:06', '2025-05-14 08:28:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(273, NULL, NULL, 1, '2025-06-23 04:00:00', '2025-06-29 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-06-27 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 08:28:27', '2025-05-14 08:28:27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(274, NULL, NULL, 1, '2025-06-30 04:00:00', '2025-07-06 04:00:00', 'Vida y Ministerio Cristianos', '', 2, 2, 46, '2025-07-04 04:00:00', '19:30:00', 'kingdom_hall', NULL, NULL, NULL, 'draft', '2025-05-14 08:28:47', '2025-05-14 09:06:33', NULL, NULL, NULL, NULL, NULL, NULL, 21, 25, 45, NULL, 10, 49, 17, 32, 24, 1, 11, NULL, NULL, NULL, NULL, NULL),
(275, NULL, NULL, 1, '2025-05-19 04:00:00', '2025-05-25 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-05-23 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 08:43:39', '2025-05-14 08:43:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(276, NULL, NULL, 1, '2025-07-14 04:00:00', '2025-07-20 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-07-18 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 11:09:27', '2025-05-14 11:09:27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(277, NULL, NULL, 1, '2025-08-18 04:00:00', '2025-08-24 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-08-22 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 11:12:27', '2025-05-14 11:12:27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(278, NULL, NULL, 1, '2025-07-21 04:00:00', '2025-07-27 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-07-25 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 17:48:01', '2025-05-14 17:48:01', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(279, NULL, NULL, 1, '2025-08-11 04:00:00', '2025-08-17 04:00:00', 'Vida y Ministerio Cristianos', '', NULL, NULL, NULL, '2025-08-15 04:00:00', '19:30:00', 'Salón del Reino', NULL, NULL, NULL, 'draft', '2025-05-14 17:55:23', '2025-05-14 17:55:23', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
/*!40000 ALTER TABLE `midweek_meetings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_members_settings`
--

DROP TABLE IF EXISTS `midweek_members_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_members_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `congregation_id` int NOT NULL,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_setting` (`congregation_id`,`setting_key`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_members_settings`
--

LOCK TABLES `midweek_members_settings` WRITE;
/*!40000 ALTER TABLE `midweek_members_settings` DISABLE KEYS */;
INSERT INTO `midweek_members_settings` (`id`, `congregation_id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES (1, 1, 'prayer_members', '["1","2","7","9","11","14","15","21","23","46","5130","5132","3","18","24","25","42","5137"]', '2025-06-01 19:16:41', '2025-06-01 19:16:41'),
(2, 1, 'treasures_members', '["1","2","7","9","11","14","15","21","23","46","5130","5132"]', '2025-06-01 19:16:41', '2025-06-01 19:16:41'),
(3, 1, 'gems_members', '["3","18","24","25","42","5137"]', '2025-06-01 19:16:41', '2025-06-01 19:16:41'),
(4, 1, 'bible_reading_members', '["40","45","48","50","56","64","65"]', '2025-06-01 19:16:41', '2025-06-01 19:19:09'),
(5, 1, 'ministry_members', '["4","5","10","12","16","17","19","26","28","29","30","31","32","33","34","35","36","37","38","39","40","41","43","44","45","47","48","49","50","51","52","53","54","55","56","57","58","59","60","61","62","63","64"]', '2025-06-01 19:16:41', '2025-06-01 19:16:41'),
(6, 1, 'ministry_talk_members', '["3","18","24","25","42","5137","55","64"]', '2025-06-01 19:16:41', '2025-06-01 19:19:09'),
(7, 1, 'christian_life_members', '["1","2","7","9","11","14","15","21","46","5130","5132"]', '2025-06-01 19:16:41', '2025-06-01 19:16:41'),
(8, 1, 'congregation_bible_study_members', '["1","2","7","9","11","14","15","21","46","5130","5132"]', '2025-06-01 19:16:41', '2025-06-01 19:16:41');
/*!40000 ALTER TABLE `midweek_members_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_part_definitions`
--

DROP TABLE IF EXISTS `midweek_part_definitions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_part_definitions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `congregation_id` int DEFAULT NULL COMMENT 'NULL means applies to all congregations',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Part type identifier',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Display name of the part',
  `duration` int NOT NULL DEFAULT '5' COMMENT 'Duration in minutes',
  `order_index` int NOT NULL DEFAULT '0' COMMENT 'Order in the meeting',
  `elder_only` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether only elders can be assigned',
  `ms_only` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Whether only ministerial servants can be assigned',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_part_type_congregation` (`type`,`congregation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_part_definitions`
--

LOCK TABLES `midweek_part_definitions` WRITE;
/*!40000 ALTER TABLE `midweek_part_definitions` DISABLE KEYS */;
INSERT INTO `midweek_part_definitions` (`id`, `congregation_id`, `type`, `name`, `duration`, `order_index`, `elder_only`, `ms_only`, `created_at`, `updated_at`) VALUES (1, NULL, 'chairman', 'Presidente', 0, 1, 1, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(2, NULL, 'opening-prayer', 'Oración inicial', 2, 2, 1, 1, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(3, NULL, 'opening-song', 'Canción inicial', 3, 3, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(4, NULL, 'treasures-talk', 'Discurso de Tesoros', 10, 4, 1, 1, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(5, NULL, 'gems', 'Busquemos perlas escondidas', 8, 5, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(6, NULL, 'bible-reading', 'Lectura de la Biblia', 4, 6, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(7, NULL, 'first-ministry', 'Primera parte del ministerio', 5, 7, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(8, NULL, 'second-ministry', 'Segunda parte del ministerio', 5, 8, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(9, NULL, 'ministry-talk', 'Discurso del ministerio', 5, 9, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(10, NULL, 'middle-song', 'Canción intermedia', 3, 10, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(11, NULL, 'christian-life-part', 'Parte de Vida Cristiana', 15, 11, 1, 1, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(12, NULL, 'congregation-study', 'Estudio bíblico de la congregación', 30, 12, 1, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(13, NULL, 'closing-song', 'Canción final', 3, 13, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(14, NULL, 'closing-prayer', 'Oración final', 2, 14, 1, 1, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(15, NULL, 'audio-video', 'Audio/Video', 0, 15, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(16, NULL, 'platform', 'Plataforma', 0, 16, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(17, NULL, 'microphones', 'Micrófonos', 0, 17, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55'),
(18, NULL, 'cleaning', 'Limpieza del Salón', 0, 18, 0, 0, '2025-05-02 07:46:55', '2025-05-02 07:46:55');
/*!40000 ALTER TABLE `midweek_part_definitions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_part_roles`
--

DROP TABLE IF EXISTS `midweek_part_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_part_roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `part_type` varchar(50) NOT NULL,
  `role_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `role_id` (`role_id`),
  CONSTRAINT `midweek_part_roles_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_part_roles`
--

LOCK TABLES `midweek_part_roles` WRITE;
/*!40000 ALTER TABLE `midweek_part_roles` DISABLE KEYS */;
INSERT INTO `midweek_part_roles` (`id`, `part_type`, `role_id`, `created_at`, `updated_at`) VALUES (1, 'chairman', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(2, 'chairman', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(3, 'opening_prayer', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(4, 'opening_prayer', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(5, 'opening_prayer', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(6, 'closing_prayer', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(7, 'closing_prayer', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(8, 'closing_prayer', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(9, 'treasures_talk', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(10, 'treasures_talk', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(11, 'digging', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(12, 'digging', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(13, 'bible_reading', 4, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(14, 'bible_reading', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(15, 'bible_reading', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(16, 'bible_reading', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(17, 'initial_call', 4, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(18, 'initial_call', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(19, 'initial_call', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(20, 'initial_call', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(21, 'return_visit', 4, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(22, 'return_visit', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(23, 'return_visit', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(24, 'return_visit', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(25, 'bible_study', 4, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(26, 'bible_study', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(27, 'bible_study', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(28, 'bible_study', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(29, 'talk', 4, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(30, 'talk', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(31, 'talk', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(32, 'talk', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(33, 'christian_life_talk', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(34, 'christian_life_talk', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(35, 'christian_life_talk', 3, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(36, 'congregation_bible_study', 2, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(37, 'congregation_bible_study', 1, '2025-05-01 08:35:35', '2025-05-01 08:35:35'),
(38, 'chairman', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(39, 'chairman', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(40, 'opening_prayer', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(41, 'opening_prayer', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(42, 'opening_prayer', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(43, 'closing_prayer', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(44, 'closing_prayer', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(45, 'closing_prayer', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(46, 'treasures_talk', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(47, 'treasures_talk', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(48, 'digging', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(49, 'digging', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(50, 'bible_reading', 4, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(51, 'bible_reading', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(52, 'bible_reading', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(53, 'bible_reading', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(54, 'initial_call', 4, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(55, 'initial_call', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(56, 'initial_call', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(57, 'initial_call', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(58, 'return_visit', 4, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(59, 'return_visit', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(60, 'return_visit', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(61, 'return_visit', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(62, 'bible_study', 4, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(63, 'bible_study', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(64, 'bible_study', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(65, 'bible_study', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(66, 'talk', 4, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(67, 'talk', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(68, 'talk', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(69, 'talk', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(70, 'christian_life_talk', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(71, 'christian_life_talk', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(72, 'christian_life_talk', 3, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(73, 'congregation_bible_study', 2, '2025-05-01 08:36:08', '2025-05-01 08:36:08'),
(74, 'congregation_bible_study', 1, '2025-05-01 08:36:08', '2025-05-01 08:36:08');
/*!40000 ALTER TABLE `midweek_part_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_parts`
--

DROP TABLE IF EXISTS `midweek_parts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_parts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `meeting_id` int NOT NULL,
  `section_id` int NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text,
  `duration` int NOT NULL,
  `assignee_id` int DEFAULT NULL,
  `assistant_id` int DEFAULT NULL,
  `display_order` int NOT NULL,
  `jw_number` int DEFAULT NULL,
  `is_section_header` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `meeting_id` (`meeting_id`),
  KEY `section_id` (`section_id`),
  KEY `assignee_id` (`assignee_id`),
  KEY `assistant_id` (`assistant_id`),
  CONSTRAINT `midweek_parts_ibfk_1` FOREIGN KEY (`meeting_id`) REFERENCES `midweek_meetings` (`id`) ON DELETE CASCADE,
  CONSTRAINT `midweek_parts_ibfk_2` FOREIGN KEY (`section_id`) REFERENCES `midweek_sections` (`id`) ON DELETE CASCADE,
  CONSTRAINT `midweek_parts_ibfk_3` FOREIGN KEY (`assignee_id`) REFERENCES `members` (`id`) ON DELETE SET NULL,
  CONSTRAINT `midweek_parts_ibfk_4` FOREIGN KEY (`assistant_id`) REFERENCES `members` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=430 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_parts`
--

LOCK TABLES `midweek_parts` WRITE;
/*!40000 ALTER TABLE `midweek_parts` DISABLE KEYS */;
INSERT INTO `midweek_parts` (`id`, `meeting_id`, `section_id`, `title`, `description`, `duration`, `assignee_id`, `assistant_id`, `display_order`, `jw_number`, `is_section_header`, `created_at`, `updated_at`) VALUES (294, 239, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(295, 239, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(296, 239, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(297, 239, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(298, 239, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(299, 239, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(300, 239, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(301, 239, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(302, 237, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(303, 237, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(304, 237, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(305, 237, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(306, 237, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(307, 237, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(308, 237, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(309, 237, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(310, 236, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(311, 236, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(312, 236, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(313, 236, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(314, 236, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(315, 236, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(316, 236, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(317, 236, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(318, 235, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(319, 235, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(320, 235, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(321, 235, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(322, 235, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(323, 235, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(324, 235, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(325, 235, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(326, 234, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(327, 234, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(328, 234, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(329, 234, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(330, 234, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(331, 234, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(332, 234, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(333, 234, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(334, 233, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(335, 233, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(336, 233, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(337, 233, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(338, 233, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(339, 233, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(340, 233, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(341, 233, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(342, 222, 1, 'Tesoros de la Biblia', NULL, 10, 1, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(343, 222, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(344, 222, 1, 'Lectura de la Biblia', NULL, 4, 1, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(345, 222, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(346, 222, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(347, 222, 2, 'Curso bíblico', NULL, 5, 1, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(348, 222, 3, 'Necesidades de la congregación', NULL, 8, 1, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(349, 222, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(350, 232, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(351, 232, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(352, 232, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(353, 232, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(354, 232, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(355, 232, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(356, 232, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(357, 232, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(358, 231, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(359, 231, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(360, 231, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(361, 231, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(362, 231, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(363, 231, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(364, 231, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(365, 231, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(366, 230, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(367, 230, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(368, 230, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(369, 230, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(370, 230, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(371, 230, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(372, 230, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(373, 230, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(374, 229, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(375, 229, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(376, 229, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(377, 229, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(378, 229, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(379, 229, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(380, 229, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(381, 229, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(382, 228, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(383, 228, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(384, 228, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(385, 228, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(386, 228, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(387, 228, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(388, 228, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(389, 228, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(390, 227, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(391, 227, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(392, 227, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(393, 227, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37');
INSERT INTO `midweek_parts` (`id`, `meeting_id`, `section_id`, `title`, `description`, `duration`, `assignee_id`, `assistant_id`, `display_order`, `jw_number`, `is_section_header`, `created_at`, `updated_at`) VALUES (394, 227, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(395, 227, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(396, 227, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(397, 227, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(398, 226, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(399, 226, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(400, 226, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(401, 226, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(402, 226, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(403, 226, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(404, 226, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(405, 226, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(406, 225, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(407, 225, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(408, 225, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(409, 225, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(410, 225, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(411, 225, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(412, 225, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(413, 225, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(414, 224, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(415, 224, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(416, 224, 1, 'Lectura de la Biblia', NULL, 4, 2, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:38'),
(417, 224, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(418, 224, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(419, 224, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:37'),
(420, 224, 3, 'Necesidades de la congregación', NULL, 8, 7, NULL, 1, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:38'),
(421, 224, 3, 'Estudio bíblico de la congregación', NULL, 30, 6, NULL, 2, NULL, 0, '2025-05-04 10:47:37', '2025-05-04 10:47:38'),
(422, 223, 1, 'Tesoros de la Biblia', NULL, 10, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(423, 223, 1, 'Busquemos perlas escondidas', NULL, 8, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(424, 223, 1, 'Lectura de la Biblia', NULL, 4, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(425, 223, 2, 'Primera conversación', NULL, 2, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(426, 223, 2, 'Revisita', NULL, 4, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(427, 223, 2, 'Curso bíblico', NULL, 5, NULL, NULL, 3, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(428, 223, 3, 'Necesidades de la congregación', NULL, 8, NULL, NULL, 1, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38'),
(429, 223, 3, 'Estudio bíblico de la congregación', NULL, 30, NULL, NULL, 2, NULL, 0, '2025-05-04 10:47:38', '2025-05-04 10:47:38');
/*!40000 ALTER TABLE `midweek_parts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_sections`
--

DROP TABLE IF EXISTS `midweek_sections`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_sections` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `display_order` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1320 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_sections`
--

LOCK TABLES `midweek_sections` WRITE;
/*!40000 ALTER TABLE `midweek_sections` DISABLE KEYS */;
INSERT INTO `midweek_sections` (`id`, `name`, `display_name`, `display_order`, `created_at`, `updated_at`) VALUES (1, 'treasures', 'Tesoros de la Palabra de Dios', 1, '2025-04-27 16:10:13', '2025-04-27 16:10:13'),
(2, 'ministry', 'Seamos Mejores Maestros', 2, '2025-04-27 16:10:13', '2025-04-27 16:10:13'),
(3, 'christian_life', 'Nuestra Vida Cristiana', 3, '2025-04-27 16:10:13', '2025-04-27 16:10:13');
/*!40000 ALTER TABLE `midweek_sections` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_settings`
--

DROP TABLE IF EXISTS `midweek_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `congregation_id` int NOT NULL,
  `default_day_of_week` tinyint DEFAULT '5' COMMENT 'Day of week (0=Sunday, 1=Monday, etc.)',
  `default_time` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '19:30' COMMENT 'Default meeting time (24-hour format)',
  `default_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'Salon del Reino' COMMENT 'Default meeting location',
  `default_zoom_meeting_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Default Zoom meeting ID',
  `default_zoom_meeting_password` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Default Zoom meeting password',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_congregation` (`congregation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_settings`
--

LOCK TABLES `midweek_settings` WRITE;
/*!40000 ALTER TABLE `midweek_settings` DISABLE KEYS */;
INSERT INTO `midweek_settings` (`id`, `congregation_id`, `default_day_of_week`, `default_time`, `default_location`, `default_zoom_meeting_id`, `default_zoom_meeting_password`, `created_at`, `updated_at`) VALUES (1, 1, 5, '19:30', 'Salon del Reino', NULL, NULL, '2025-05-02 07:46:55', '2025-05-02 07:46:55');
/*!40000 ALTER TABLE `midweek_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `midweek_workbooks`
--

DROP TABLE IF EXISTS `midweek_workbooks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `midweek_workbooks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `month_period` varchar(50) NOT NULL,
  `year` int NOT NULL,
  `url_path` varchar(255) NOT NULL,
  `source_url` varchar(255) NOT NULL,
  `is_processed` tinyint(1) DEFAULT '0',
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `month_period` (`month_period`,`year`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `midweek_workbooks`
--

LOCK TABLES `midweek_workbooks` WRITE;
/*!40000 ALTER TABLE `midweek_workbooks` DISABLE KEYS */;
INSERT INTO `midweek_workbooks` (`id`, `month_period`, `year`, `url_path`, `source_url`, `is_processed`, `processed_at`, `created_at`, `updated_at`) VALUES (1, 'Mayo-Junio', 2025, '/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/', 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/', 0, NULL, '2025-04-28 09:39:48', '2025-04-28 09:39:48'),
(2, 'Enero-Diciembre 2026', 2026, 'https://wol.jw.org/es/wol/meetings/r4/lp-s/2026', 'https://wol.jw.org/es/wol/meetings/r4/lp-s/2026', 0, NULL, '2025-04-30 08:00:05', '2025-04-30 08:00:05'),
(3, 'Enero-Diciembre 2024', 2024, 'https://wol.jw.org/es/wol/meetings/r4/lp-s/2024', 'https://wol.jw.org/es/wol/meetings/r4/lp-s/2024', 0, NULL, '2025-04-30 08:06:46', '2025-04-30 08:06:46');
/*!40000 ALTER TABLE `midweek_workbooks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES (1, 'view', 'Basic view access', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(2, 'field_service', 'Access to field service section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(3, 'midweek_meeting', 'Access to midweek meeting section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(4, 'weekend_meeting', 'Access to weekend meeting section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(5, 'tasks', 'Access to tasks section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(6, 'members', 'Access to members section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(7, 'members_edit', 'Edit members', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(8, 'members_reset_pin', 'Reset member PINs', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(9, 'letters', 'Access to letters section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(10, 'letters_edit', 'Edit letters', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(11, 'letters_delete', 'Delete letters', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(12, 'programs', 'Access to programs section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(13, 'events', 'Access to events section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(14, 'assignments', 'Access to assignments section', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(15, 'congregation', 'Access to congregation settings', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(16, 'database', 'Access to database management', '2025-04-25 22:08:04', '2025-04-25 22:08:04'),
(17, 'permissions', 'Access to permissions management', '2025-04-25 22:08:04', '2025-04-25 22:08:04');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `created_at`) VALUES (1, 1, '2025-04-25 22:08:04'),
(1, 2, '2025-04-25 22:08:04'),
(1, 3, '2025-04-25 22:08:04'),
(1, 4, '2025-04-25 22:08:04'),
(1, 5, '2025-04-25 22:08:04'),
(1, 6, '2025-04-25 22:08:04'),
(1, 7, '2025-04-25 22:08:04'),
(1, 8, '2025-04-25 22:08:04'),
(1, 9, '2025-04-25 22:08:04'),
(1, 10, '2025-04-25 22:08:04'),
(1, 11, '2025-04-25 22:08:04'),
(1, 12, '2025-04-25 22:08:04'),
(1, 13, '2025-04-25 22:08:04'),
(1, 14, '2025-04-25 22:08:04'),
(1, 15, '2025-04-25 22:08:04'),
(2, 1, '2025-04-25 22:08:04'),
(2, 2, '2025-04-25 22:08:04'),
(2, 3, '2025-04-25 22:08:04'),
(2, 4, '2025-04-25 22:08:04'),
(2, 5, '2025-04-25 22:08:04'),
(2, 9, '2025-04-25 22:08:04'),
(2, 12, '2025-04-25 22:08:04'),
(2, 13, '2025-04-25 22:08:04'),
(2, 14, '2025-04-25 22:08:04'),
(3, 1, '2025-04-25 22:08:04'),
(3, 2, '2025-04-25 22:08:04'),
(3, 5, '2025-04-25 22:08:04'),
(4, 1, '2025-04-25 22:08:04'),
(5, 1, '2025-04-25 22:08:04'),
(5, 2, '2025-04-25 22:08:04'),
(5, 3, '2025-04-25 22:08:04'),
(5, 4, '2025-04-25 22:08:04'),
(5, 5, '2025-04-25 22:08:04'),
(5, 6, '2025-04-25 22:08:04'),
(5, 7, '2025-04-25 22:08:04'),
(5, 8, '2025-04-25 22:08:04'),
(5, 9, '2025-04-25 22:08:04'),
(5, 10, '2025-04-25 22:08:04'),
(5, 11, '2025-04-25 22:08:04'),
(5, 12, '2025-04-25 22:08:04'),
(5, 13, '2025-04-25 22:08:04'),
(5, 14, '2025-04-25 22:08:04'),
(5, 15, '2025-04-25 22:08:04'),
(5, 16, '2025-04-25 22:08:04'),
(5, 17, '2025-04-25 22:08:04');
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `roles`
--

DROP TABLE IF EXISTS `roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `roles` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=3310 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `roles`
--

LOCK TABLES `roles` WRITE;
/*!40000 ALTER TABLE `roles` DISABLE KEYS */;
INSERT INTO `roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES (1, 'overseer_coordinator', 'Overseer/Coordinator with full system access', '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(2, 'elder', 'Elder with administrative access', '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(3, 'ministerial_servant', 'Ministerial Servant with limited administrative access', '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(4, 'publisher', 'Regular publisher with basic access', '2025-04-24 02:01:19', '2025-04-24 02:01:19'),
(5, 'developer', 'Developer with full system access', '2025-04-24 23:53:44', '2025-04-24 23:53:44');
/*!40000 ALTER TABLE `roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `service_groups`
--

DROP TABLE IF EXISTS `service_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `service_groups` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `group_number` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `service_groups`
--

LOCK TABLES `service_groups` WRITE;
/*!40000 ALTER TABLE `service_groups` DISABLE KEYS */;
INSERT INTO `service_groups` (`id`, `name`, `group_number`, `created_at`, `updated_at`) VALUES (1, 'Grupo 1', 1, '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(2, 'Grupo 2', 2, '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(3, 'Grupo 3', 3, '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(4, 'Grupo 4', 4, '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(5, 'Grupo 5', 5, '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(6, 'Grupo 6', 6, '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(7, 'Grupo 7', 7, '2025-04-26 09:21:57', '2025-04-26 09:21:57');
/*!40000 ALTER TABLE `service_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `songs`
--

DROP TABLE IF EXISTS `songs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `songs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `number` int NOT NULL,
  `title_es` varchar(255) NOT NULL,
  `title_en` varchar(255) DEFAULT NULL,
  `is_custom` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_song_number` (`number`)
) ENGINE=InnoDB AUTO_INCREMENT=188 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `songs`
--

LOCK TABLES `songs` WRITE;
/*!40000 ALTER TABLE `songs` DISABLE KEYS */;
INSERT INTO `songs` (`id`, `number`, `title_es`, `title_en`, `is_custom`, `created_at`, `updated_at`) VALUES (1, 1, 'Las cualidades principales de Jehová', 'Jehovah''s Attributes', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(2, 2, 'Tu nombre es Jehová', 'Jehovah Is Your Name', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(3, 3, 'Tú me das fuerza, confianza y valor', 'Our Strength, Our Hope, Our Confidence', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(4, 4, '“Jehová es mi Pastor”', '"Jehovah Is My Shepherd"', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(5, 5, 'Las maravillosas obras de Dios', 'God''s Wondrous Works', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(6, 10, '¡Alabemos a nuestro Dios, Jehová!', 'Praise Jehovah Our God!', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(7, 21, 'Busquemos primero el Reino', 'Keep On Seeking First the Kingdom', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(8, 57, 'Predicamos a toda clase de personas', 'Preaching to All Sorts of People', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(9, 100, 'Seamos hospitalarios', 'Receive Them With Hospitality', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(10, 144, 'No dejes de mirar allí', 'Keep Your Eyes on the Prize!', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(11, 155, 'Mi mayor felicidad', 'Our Joy Eternally', 0, '2025-05-01 03:58:44', '2025-05-01 07:02:17'),
(27, 6, 'Los cielos proclaman la gloria de Dios', 'The Heavens Declare God''s Glory', 0, '2025-05-01 04:59:30', '2025-05-01 07:02:17'),
(28, 7, 'Jehová es mi fuerza y mi salvación', 'Jehovah, Our Strength', 0, '2025-05-01 04:59:30', '2025-05-01 07:02:17'),
(29, 8, 'Jehová es mi Refugio', 'Jehovah Is Our Refuge', 0, '2025-05-01 04:59:30', '2025-05-01 07:02:17'),
(30, 9, '¡Jehová es nuestro Rey!', 'Jehovah Is Our King!', 0, '2025-05-01 04:59:30', '2025-05-01 07:02:17'),
(42, 11, 'La creación alaba a Jehová', 'Creation Praises God', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(43, 12, 'Jehová, nuestro gran Dios', 'Great God, Jehovah', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(44, 13, 'Cristo es nuestro modelo', 'Christ, Our Model', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(45, 14, 'Honremos al nuevo Rey de la Tierra', 'Praising Earth''s New King', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(46, 15, 'Alabemos al Primogénito de Jehová', 'Praise Jehovah''s Firstborn!', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(47, 16, 'Alabemos a Jehová por su Hijo, el Ungido', 'Praise Jah for His Son, the Anointed', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(48, 17, '“Quiero”', '"I Want To"', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(49, 18, 'Gracias por el rescate', 'Grateful for the Ransom', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(50, 19, 'La Cena del Señor', 'The Lord''s Evening Meal', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(51, 20, 'Enviaste a Jesús, tu Hijo amado', 'You Gave Your Precious Son', 0, '2025-05-01 06:32:40', '2025-05-01 07:02:17'),
(52, 22, '¡Que venga el Reino que Dios ha establecido!', 'The Kingdom Is in Place—Let It Come!', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(53, 23, 'Jehová ha empezado su gobierno', 'Jehovah Begins His Rule', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(54, 24, 'Subamos a la montaña de Jehová', 'Come to Jehovah''s Mountain', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(55, 25, 'Una posesión especial', 'A Special Possession', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(56, 26, '“Si lo haces por él, lo haces por mí”', 'You Did It for Me', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(57, 27, 'La revelación de los hijos de Dios', 'The Revealing of God''s Sons', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(58, 28, 'Cómo hacernos amigos de Jehová', 'Gaining Jehovah''s Friendship', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(59, 29, 'Hagamos honor a nuestro nombre', 'Living Up to Our Name', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(60, 30, 'Mi Amigo, mi Padre, mi Dios', 'My Father, My God and Friend', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(61, 31, 'Camina siempre con Jehová', 'Oh, Walk With God!', 0, '2025-05-01 06:54:32', '2025-05-01 07:02:17'),
(62, 32, '¡Ponte de parte de Dios!', 'Take Sides With Jehovah!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(63, 33, 'Echa sobre Dios tu carga', 'Throw Your Burden on Jehovah', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(64, 34, 'Caminaré en integridad', 'Walking in Integrity', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(65, 35, 'Asegurémonos de lo más importante', '"Make Sure of the More Important Things"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(66, 36, 'Cuidemos nuestro corazón', 'We Guard Our Hearts', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(67, 37, 'Serviré a Jehová con el corazón', 'Serving Jehovah Whole-Souled', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(68, 38, 'Jehová te cuidará', 'He Will Make You Strong', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(69, 39, 'Un buen nombre ante Dios', 'Make a Good Name With God', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(70, 40, '¿A quién servimos?', 'To Whom Do We Belong?', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(71, 41, 'Padre, escucha mi oración', 'Please Hear My Prayer', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(72, 42, 'La oración del siervo de Dios', 'The Prayer of God''s Servant', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(73, 43, 'Una oración para dar gracias a Dios', 'A Prayer of Thanks', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(74, 44, 'Una súplica ferviente', 'A Prayer of the Lowly One', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(75, 45, '“La meditación de mi corazón”', 'The Meditation of My Heart', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(76, 46, 'Gracias, Jehová', 'We Thank You, Jehovah', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(77, 47, 'Oremos a Dios todos los días', 'Pray to Jehovah Each Day', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(78, 48, 'Caminemos diariamente con Jehová', 'Daily Walking With Jehovah', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(79, 49, 'Alegremos el corazón de Jehová', 'Making Jehovah''s Heart Glad', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(80, 50, 'Mi oración de dedicación', 'My Prayer of Dedication', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(81, 51, 'Estamos dedicados a Dios', 'To God We Are Dedicated!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(82, 52, 'La dedicación cristiana', 'Christian Dedication', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(83, 53, 'Hoy voy a salir a predicar', 'Preparing to Preach', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(84, 54, '“Este es el camino”', '"This Is the Way"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(85, 55, '¡No los temas!', 'Fear Them Not!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(86, 56, 'Vive la verdad', 'Make the Truth Your Own', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(87, 58, 'Voy a buscar a los amigos de la paz', 'Searching for Friends of Peace', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(88, 59, 'Ven a alabar a Jehová', 'Praise Jah With Me', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(89, 60, 'Hay vidas en juego', 'It Means Their Life', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(90, 61, '¡Avancen, Testigos!', 'Forward, You Witnesses!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(91, 62, 'La nueva canción', 'The New Song', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(92, 63, '¡Soy testigo de Jehová!', 'We''re Jehovah''s Witnesses!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(93, 64, 'Participemos con gozo en la cosecha', 'Sharing Joyfully in the Harvest', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:17'),
(94, 65, 'Lucha por progresar', 'Move Ahead!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(95, 66, 'Proclamemos las buenas noticias', 'Declare the Good News', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(96, 67, '“Predica la palabra”', '"Preach the Word"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(97, 68, 'Sembremos semillas del Reino', 'Sowing Kingdom Seed', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(98, 69, 'Prediquen las nuevas del Reino', 'Go Forward in Preaching the Kingdom!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(99, 70, 'Busquemos a los merecedores', 'Search Out Deserving Ones', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(100, 71, '¡Somos los guerreros de Jehová!', 'We Are Jehovah''s Army!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(101, 72, 'Anunciaré la verdad del Reino', 'Making Known the Kingdom Truth', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(102, 73, 'Danos fuerzas y valor', 'Grant Us Boldness', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(103, 74, 'Ven a cantar la gran canción del Reino', 'Join in the Kingdom Song!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(104, 75, '“¡Aquí estoy, envíame!”', '"Here I Am! Send Me!"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(105, 76, 'Cuéntame lo que sientes', 'How Does It Make You Feel?', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(106, 77, 'Luz en un mundo oscuro', 'Light in a Darkened World', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(107, 78, 'Enseñemos la Palabra de Dios', '"Teaching the Word of God"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(108, 79, 'Que sigan firmes en la fe', 'Teach Them to Stand Firm', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(109, 80, '“Prueben y vean que Jehová es bueno”', '"Taste and See That Jehovah Is Good"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(110, 81, 'La vida del precursor', 'The Life of a Pioneer', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(111, 82, 'Hagamos que brille nuestra luz', '"Let Your Light Shine"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(112, 83, '“De casa en casa”', '"From House to House"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(113, 84, 'Servimos donde se nos necesite', 'Reaching Out', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(114, 85, '“Recíbanse con gusto unos a otros”', 'Welcome One Another', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(115, 86, 'Necesitamos que Jehová nos enseñe', 'We Must Be Taught', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(116, 87, 'Ven a recibir ánimo', 'Come! Be Refreshed', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(117, 88, 'Hazme conocer tus caminos', 'Make Me Know Your Ways', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(118, 89, 'Jehová bendice al que escucha y obedece', 'Listen, Obey, and Be Blessed', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(119, 90, 'Animémonos unos a otros', 'Encourage One Another', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(120, 91, 'Una obra de amor', 'Our Labor of Love', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(121, 92, 'Un lugar que lleva tu nombre', 'A Place Bearing Your Name', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(122, 93, 'Bendice nuestras reuniones', 'Bless Our Meeting Together', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(123, 94, 'Agradecidos por la Palabra de Dios', 'Grateful for God''s Word', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(124, 95, 'La luz brilla más cada día', 'The Light Gets Brighter', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(125, 96, 'El libro de Dios es un tesoro', 'God''s Own Book—A Treasure', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(126, 97, 'Nuestra vida depende de la Palabra de Dios', 'Life Depends on God''s Word', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18');
INSERT INTO `songs` (`id`, `number`, `title_es`, `title_en`, `is_custom`, `created_at`, `updated_at`) VALUES (127, 98, 'Las Escrituras están inspiradas por Dios', 'The Scriptures—Inspired of God', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(128, 99, 'Miles de fieles hermanos', 'Myriads of Brothers', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(129, 101, 'Sirvamos a Dios en unidad', 'Working Together in Unity', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(130, 102, 'Ayudemos a los débiles', '"Assist Those Who Are Weak"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(131, 103, 'Nuestros pastores son un regalo de Dios', 'Shepherds—Gifts in Men', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(132, 104, 'El espíritu santo es un regalo de Dios', 'God''s Gift of Holy Spirit', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(133, 105, '“Dios es amor”', '"God Is Love"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(134, 106, 'Cultivemos amor verdadero', 'Cultivating the Quality of Love', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(135, 107, 'Dios nos enseñó a amar', 'The Divine Pattern of Love', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(136, 108, 'El amor leal de Jehová', 'God''s Loyal Love', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(137, 109, 'Amémonos de todo corazón', 'Love Intensely From the Heart', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(138, 110, 'El gozo de Jehová', '"The Joy of Jehovah"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(139, 111, 'Los motivos de nuestro gozo', 'Our Reasons for Joy', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(140, 112, 'Jehová, el Dios de la paz', 'Jehovah, God of Peace', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(141, 113, 'La paz del pueblo de Dios', 'Our Possession of Peace', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(142, 114, 'Demostremos paciencia', '"Exercise Patience"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(143, 115, 'Gratitud por la paciencia de Dios', 'Gratitude for Divine Patience', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(144, 116, 'Seamos amables y bondadosos', 'The Power of Kindness', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(145, 117, 'Imitemos la bondad de Jehová', 'The Quality of Goodness', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(146, 118, '“Danos más fe”', '"Give Us More Faith"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(147, 119, 'Necesitamos una fe fuerte', 'We Must Have Faith', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(148, 120, 'Seamos apacibles y humildes como Cristo', 'Imitate Christ''s Mildness', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(149, 121, 'Necesitamos autodominio', 'We Need Self-Control', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(150, 122, '¡Mantengámonos firmes, inmovibles!', 'Be Steadfast, Immovable!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(151, 123, 'Seamos leales y sumisos al orden teocrático', 'Loyally Submitting to Theocratic Order', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(152, 124, 'Siempre fieles y leales', 'Ever Loyal', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(153, 125, 'Felices los misericordiosos', '"Happy Are the Merciful!"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(154, 126, 'Siempre fuertes, fieles y firmes', 'Stay Awake, Stand Firm, Grow Mighty', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(155, 127, 'La clase de persona que debo ser', 'The Sort of Person I Should Be', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(156, 128, 'Aguantemos hasta el fin', 'Enduring to the End', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(157, 129, 'Servimos con aguante', 'We Will Keep Enduring', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(158, 130, 'Aprendamos a perdonar', 'Be Forgiving', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(159, 131, '“Lo que Dios ha unido”', '"What God Has Yoked Together"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(160, 132, 'Ahora ya somos uno', 'Now We Are One', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(161, 133, 'Sirvamos a Jehová en nuestra juventud', 'Worship Jehovah During Youth', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(162, 134, 'Los hijos son un regalo de Dios', 'Children Are a Trust From God', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(163, 135, 'Jehová te pide: “Sé sabio, hijo mío”', 'Jehovah''s Warm Appeal: "Be Wise, My Son"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(164, 136, 'Que Jehová te bendiga', '"A Perfect Wage" From Jehovah', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(165, 137, 'Fieles, valiosas, amadas', 'Faithful Women, Christian Sisters', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(166, 138, 'Los cabellos blancos, una hermosa corona', 'Beauty in Gray-Headedness', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(167, 139, '¿Te ves en el nuevo mundo?', 'See Yourself When All Is New', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(168, 140, '¡Vida sin fin, al fin!', 'Life Without End—At Last!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(169, 141, 'El maravilloso regalo de la vida', 'The Miracle of Life', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(170, 142, 'Aferrémonos a nuestra esperanza', 'Holding Fast to Our Hope', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(171, 143, '¡Hay que seguir vigilantes!', 'Keep Working, Watching, and Waiting', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(172, 145, 'Dios prometió un Paraíso', 'God''s Promise of Paradise', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(173, 146, '“Estoy haciendo todo nuevo”', '"Making All Things New"', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(174, 147, 'Dios nos promete la vida eterna', 'Life Everlasting Is Promised', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(175, 148, 'Jehová es mi Roca de salvación', 'Jehovah Provides Escape', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(176, 149, 'La canción de la victoria', 'A Victory Song', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(177, 150, '¡Jehová será tu Salvador!', 'Seek God for Your Deliverance', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(178, 151, 'Jehová los llamará', 'He Will Call', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(179, 152, 'Un lugar que te honrará', 'A Place That Will Bring You Praise', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(180, 153, 'Por ti seré valiente', 'Give Me Courage', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(181, 154, 'Un amor sin final', 'Unfailing Love', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(182, 156, 'Si tienes fe', 'With Eyes of Faith', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(183, 157, 'Solo paz', 'Peace at Last!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(184, 158, 'Danos paciencia', '“It Will Not Be Late!”', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(185, 159, 'Demos gloria a Jehová', 'Give Jehovah Glory', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(186, 160, '¡Buenas noticias!', '“Good News”!', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18'),
(187, 161, 'Hacer tu voluntad', 'To Do Your Will Is My Delight', 0, '2025-05-01 06:54:33', '2025-05-01 07:02:18');
/*!40000 ALTER TABLE `songs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `special_songs`
--

DROP TABLE IF EXISTS `special_songs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `special_songs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `key_name` varchar(50) NOT NULL,
  `title_es` varchar(255) NOT NULL,
  `title_en` varchar(255) DEFAULT NULL,
  `is_custom` tinyint(1) DEFAULT '0',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_song_key` (`key_name`)
) ENGINE=InnoDB AUTO_INCREMENT=829 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `special_songs`
--

LOCK TABLES `special_songs` WRITE;
/*!40000 ALTER TABLE `special_songs` DISABLE KEYS */;
INSERT INTO `special_songs` (`id`, `key_name`, `title_es`, `title_en`, `is_custom`, `created_at`, `updated_at`) VALUES (1, 'asamblea', 'Canción de la asamblea', 'Assembly Song', 0, '2025-05-01 03:55:30', '2025-05-01 03:55:30'),
(2, 'asamblea2025', 'Canción de la asamblea regional del 2025', 'Song from the 2025 Regional Convention', 0, '2025-05-01 03:55:30', '2025-05-01 03:55:30'),
(3, 'especial', 'Canción especial', 'Special Song', 0, '2025-05-01 03:55:30', '2025-05-01 03:55:30');
/*!40000 ALTER TABLE `special_songs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_assignment_members`
--

DROP TABLE IF EXISTS `task_assignment_members`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_assignment_members` (
  `id` int NOT NULL AUTO_INCREMENT,
  `assignment_id` int NOT NULL,
  `user_id` int DEFAULT NULL,
  `service_group_id` int DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `assignment_id` (`assignment_id`),
  KEY `user_id` (`user_id`),
  KEY `service_group_id` (`service_group_id`),
  CONSTRAINT `task_assignment_members_ibfk_1` FOREIGN KEY (`assignment_id`) REFERENCES `task_assignments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `task_assignment_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `members` (`id`) ON DELETE SET NULL,
  CONSTRAINT `task_assignment_members_ibfk_3` FOREIGN KEY (`service_group_id`) REFERENCES `service_groups` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_assignment_members`
--

LOCK TABLES `task_assignment_members` WRITE;
/*!40000 ALTER TABLE `task_assignment_members` DISABLE KEYS */;
INSERT INTO `task_assignment_members` (`id`, `assignment_id`, `user_id`, `service_group_id`, `created_at`, `updated_at`) VALUES (1, 32, 3, NULL, '2025-04-27 02:04:18', '2025-04-27 02:04:18'),
(2, 33, NULL, 1, '2025-04-27 02:04:18', '2025-04-27 02:04:18'),
(8, 33, NULL, 1, '2025-04-27 02:04:53', '2025-04-27 02:04:53'),
(17, 34, NULL, 3, '2025-04-27 07:37:33', '2025-04-27 07:37:33'),
(32, 49, 7, NULL, '2025-04-27 07:42:44', '2025-04-27 07:42:44'),
(36, 35, NULL, 2, '2025-04-27 08:02:56', '2025-04-27 08:02:56'),
(37, 35, NULL, 3, '2025-04-27 08:02:56', '2025-04-27 08:02:56'),
(39, 51, 1, NULL, '2025-04-27 08:14:48', '2025-04-27 08:14:48'),
(40, 51, 2, NULL, '2025-04-27 08:14:48', '2025-04-27 08:14:48'),
(41, 52, NULL, 5, '2025-04-27 08:15:54', '2025-04-27 08:15:54'),
(42, 52, NULL, 6, '2025-04-27 08:15:54', '2025-04-27 08:15:54'),
(43, 53, 2, NULL, '2025-04-27 08:19:15', '2025-04-27 08:19:15'),
(44, 54, 1, NULL, '2025-04-27 08:21:22', '2025-04-27 08:21:22'),
(45, 44, 7, NULL, '2025-04-27 08:22:03', '2025-04-27 08:22:03'),
(46, 55, 3, NULL, '2025-04-27 08:37:05', '2025-04-27 08:37:05'),
(47, 56, 3, NULL, '2025-04-27 08:37:49', '2025-04-27 08:37:49'),
(48, 57, 1, NULL, '2025-04-27 08:38:13', '2025-04-27 08:38:13'),
(49, 58, 1, NULL, '2025-04-27 08:38:53', '2025-04-27 08:38:53');
/*!40000 ALTER TABLE `task_assignment_members` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_assignments`
--

DROP TABLE IF EXISTS `task_assignments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_assignments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL,
  `assignment_date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `task_id` (`task_id`),
  CONSTRAINT `task_assignments_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=59 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_assignments`
--

LOCK TABLES `task_assignments` WRITE;
/*!40000 ALTER TABLE `task_assignments` DISABLE KEYS */;
INSERT INTO `task_assignments` (`id`, `task_id`, `assignment_date`, `created_at`, `updated_at`) VALUES (32, 3, '2025-04-25 04:00:00', '2025-04-27 01:43:38', '2025-04-27 01:43:38'),
(33, 2, '2025-04-25 04:00:00', '2025-04-27 01:47:42', '2025-04-27 01:47:42'),
(34, 2, '2025-05-02 04:00:00', '2025-04-27 01:47:55', '2025-04-27 07:37:33'),
(35, 1, '2025-05-10 04:00:00', '2025-04-27 01:49:13', '2025-04-27 02:10:40'),
(44, 5, '2025-05-09 04:00:00', '2025-04-27 07:41:46', '2025-04-27 07:41:46'),
(49, 6, '2025-05-30 04:00:00', '2025-04-27 07:42:44', '2025-04-27 07:42:44'),
(51, 4, '2025-05-23 04:00:00', '2025-04-27 07:44:07', '2025-04-27 07:44:07'),
(52, 8, '2025-06-07 04:00:00', '2025-04-27 08:15:54', '2025-04-27 08:15:54'),
(53, 6, '2025-07-04 04:00:00', '2025-04-27 08:19:15', '2025-04-27 08:19:15'),
(54, 5, '2025-06-13 04:00:00', '2025-04-27 08:21:22', '2025-04-27 08:21:22'),
(55, 7, '2025-04-27 04:00:00', '2025-04-27 08:37:05', '2025-04-27 08:37:05'),
(56, 7, '2025-04-28 04:00:00', '2025-04-27 08:37:49', '2025-04-27 08:37:49'),
(57, 6, '2025-04-27 04:00:00', '2025-04-27 08:38:13', '2025-04-27 08:38:13'),
(58, 6, '2025-04-28 04:00:00', '2025-04-27 08:38:53', '2025-04-27 08:38:53');
/*!40000 ALTER TABLE `task_assignments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_categories`
--

DROP TABLE IF EXISTS `task_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_categories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_categories`
--

LOCK TABLES `task_categories` WRITE;
/*!40000 ALTER TABLE `task_categories` DISABLE KEYS */;
INSERT INTO `task_categories` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES (1, 'Limpieza', 'Tareas de limpieza del Salón del Reino', '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(2, 'Audio/Video', 'Tareas relacionadas con el sistema de audio y video', '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(3, 'Plataforma', 'Tareas relacionadas con la plataforma', '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(4, 'Servicio', 'Tareas relacionadas con el servicio del campo', '2025-04-26 09:21:57', '2025-04-26 09:21:57'),
(5, 'Reuniones', 'Tareas relacionadas con las reuniones', '2025-04-26 09:21:57', '2025-04-26 09:21:57');
/*!40000 ALTER TABLE `task_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `task_settings`
--

DROP TABLE IF EXISTS `task_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(50) NOT NULL,
  `setting_value` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_settings`
--

LOCK TABLES `task_settings` WRITE;
/*!40000 ALTER TABLE `task_settings` DISABLE KEYS */;
INSERT INTO `task_settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES (5, 'task_members', '[object Object]', '2025-05-14 20:42:33', '2025-05-14 20:42:33'),
(6, 'task_members', '[object Object]', '2025-05-14 20:42:33', '2025-05-14 20:42:33');
/*!40000 ALTER TABLE `task_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tasks` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `category_id` int DEFAULT NULL,
  `description` text,
  `status` enum('pending','completed') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `display_order` int DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `category_id` (`category_id`),
  CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `task_categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` (`id`, `name`, `category_id`, `description`, `status`, `created_at`, `updated_at`, `display_order`) VALUES (1, 'Limpieza del salón interior -congregacion', 1, 'Limpieza general del interior del Salón del Reino', 'pending', '2025-04-26 09:21:57', '2025-04-26 19:45:35', 0),
(2, 'Limpieza despues de la reunion (Grupo)', 1, 'Limpieza del Salón del Reino despues de la reunion', 'pending', '2025-04-26 09:21:57', '2025-04-26 19:43:02', 0),
(3, 'Audio/Video', 2, 'Manejo del sistema de audio/video para la reunión', 'pending', '2025-04-26 09:21:57', '2025-04-26 17:01:37', 0),
(4, 'Manejo de micrófonos', 2, 'Manejar los micrófonos durante la reunión', 'pending', '2025-04-26 09:21:57', '2025-04-26 09:21:57', 0),
(5, 'Plataforma', 3, 'Servicio en la plataforma', 'pending', '2025-04-26 09:21:57', '2025-04-26 09:21:57', 0),
(6, 'Servicio del campo', 4, 'Organización del servicio del campo', 'pending', '2025-04-26 09:21:57', '2025-04-26 09:21:57', 0),
(7, 'Preparación de la reunión', 5, 'Preparar el Salón para la reunión', 'pending', '2025-04-26 09:21:57', '2025-04-26 09:21:57', 0),
(8, 'Limpieza del salón exterior -congregacion', 1, 'Limpieza del salon por la congregacion', 'pending', '2025-04-26 19:44:28', '2025-04-26 19:45:24', 0);
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `territories`
--

DROP TABLE IF EXISTS `territories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `territories` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` enum('available','assigned','completed') DEFAULT 'available',
  `assigned_to` int DEFAULT NULL,
  `last_worked` date DEFAULT NULL,
  `notes` text,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `assigned_to` (`assigned_to`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `territories`
--

LOCK TABLES `territories` WRITE;
/*!40000 ALTER TABLE `territories` DISABLE KEYS */;
/*!40000 ALTER TABLE `territories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_permissions`
--

DROP TABLE IF EXISTS `user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_permissions` (
  `user_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `user_permissions_ibfk_1` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_permissions`
--

LOCK TABLES `user_permissions` WRITE;
/*!40000 ALTER TABLE `user_permissions` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-01 19:52:24
