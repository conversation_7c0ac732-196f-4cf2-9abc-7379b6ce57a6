<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>Administración de Reuniones Entre Semana - Coral Oeste</title>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link href="/css/admin-mobile.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: #673ab7;
      --secondary-color: #4285f4;
      --background-color: #f8f9fa;
      --surface-color: #ffffff;
      --error-color: #b00020;
      --success-color: #0f9d58;
      --warning-color: #f4b400;
      --text-primary: #202124;
      --text-secondary: #5f6368;
      --text-disabled: #9aa0a6;
      --border-color: #dadce0;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: 'Roboto', sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
      line-height: 1.5;
    }

    .app-header {
      background: linear-gradient(135deg, #673ab7, #5e35b1);
      color: white;
      padding: 16px;
      text-align: center;
      position: relative;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .app-title {
      font-size: 20px;
      font-weight: 500;
    }

    .back-button {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: white;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 16px;
    }

    .back-button i {
      margin-right: 4px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 16px;
    }

    .card {
      background-color: var(--surface-color);
      border-radius: 8px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      padding: 16px;
      margin-bottom: 16px;
    }

    .card-title {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .tabs {
      display: flex;
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 16px;
    }

    .tab {
      padding: 12px 16px;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      font-weight: 500;
      transition: all 0.3s;
    }

    .tab.active {
      color: var(--primary-color);
      border-bottom-color: var(--primary-color);
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.3s;
    }

    .button:hover {
      background-color: #5e35b1;
    }

    .button i {
      margin-right: 8px;
    }

    .button-secondary {
      background-color: transparent;
      color: var(--primary-color);
      border: 1px solid var(--primary-color);
    }

    .button-secondary:hover {
      background-color: rgba(103, 58, 183, 0.04);
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      text-align: left;
      padding: 12px;
      border-bottom: 1px solid var(--border-color);
    }

    th {
      font-weight: 500;
      color: var(--text-secondary);
    }

    tr:hover {
      background-color: rgba(0, 0, 0, 0.02);
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }

    .modal-content {
      background-color: var(--surface-color);
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      width: 100%;
      max-width: 500px;
      padding: 24px;
      position: relative;
      max-height: 90vh;
      overflow-y: auto;
    }

    /* Make modal wider on desktop */
    @media (min-width: 768px) {
      .modal-content {
        max-width: 800px;
      }
    }

    /* Make modal even wider on larger screens */
    @media (min-width: 1200px) {
      .modal-content {
        max-width: 1000px;
      }
    }

    /* Special styling for the add meeting modal */
    #addMeetingModal .modal-content {
      max-width: 600px;
    }

    @media (min-width: 768px) {
      #addMeetingModal .modal-content {
        max-width: 900px;
      }
    }

    @media (min-width: 1200px) {
      #addMeetingModal .modal-content {
        max-width: 1200px;
      }
    }

    /* Special styling for the view meeting modal */
    #viewMeetingModal .modal-content {
      max-width: 480px;
    }

    @media (min-width: 768px) {
      #viewMeetingModal .modal-content {
        max-width: 480px;
      }
    }

    @media (min-width: 1200px) {
      #viewMeetingModal .modal-content {
        max-width: 480px;
      }
    }

    /* View meeting styles to match frontend */
    .view-meeting-modal .modal-body {
      padding: 0;
    }

    .view-meeting-modal .modal-header {
      background-color: var(--primary-color);
      color: white;
    }

    .view-meeting-modal .close {
      color: white;
    }

    .meeting-view {
      font-family: 'Roboto', sans-serif;
      background-color: var(--background-color, #f5f5f5);
    }

    .meeting-card {
      background-color: white;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .meeting-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }

    .meeting-title {
      font-size: 18px;
      font-weight: 500;
    }

    .meeting-date {
      font-size: 14px;
      color: #757575;
    }

    .meeting-section-title {
      background-color: #8e24aa;
      color: white;
      padding: 8px 16px;
      font-weight: 500;
      font-size: 14px;
      text-transform: uppercase;
    }

    .assignment-item {
      display: flex;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .assignment-item:last-child {
      border-bottom: none;
    }

    .assignment-details {
      flex: 1;
    }

    .assignment-title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .assignment-person {
      font-size: 14px;
      color: #757575;
    }

    .song-number {
      font-weight: bold;
      margin-right: 8px;
    }

    .tasks-title {
      background-color: #0f9d58;
      color: white;
      padding: 8px 16px;
      font-weight: 500;
      font-size: 14px;
      text-transform: uppercase;
    }

    .tasks-section {
      background-color: #e8f5e9;
      padding: 12px 16px;
    }

    .task-item {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      border-bottom: 1px solid #c8e6c9;
    }

    .task-item:last-child {
      border-bottom: none;
    }

    .task-name {
      font-weight: 500;
    }

    .task-assignee {
      color: #757575;
    }

    .modal-title {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 16px;
    }

    .modal-close {
      position: absolute;
      top: 16px;
      right: 16px;
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: var(--text-secondary);
    }

    .form-group {
      margin-bottom: 16px;
    }

    .form-label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .form-input {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 16px;
    }

    .form-select {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 16px;
      background-color: white;
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      gap: 8px;
      margin-top: 24px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }

    .form-header {
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-header h3 {
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 16px;
      color: var(--primary-color);
    }

    .form-section {
      margin-bottom: 24px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .form-section h4 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      padding: 8px 12px;
      background-color: var(--primary-color);
      color: white;
      border-radius: 4px;
    }

    .form-part {
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px dashed var(--border-color);
    }

    .form-part:last-child {
      border-bottom: none;
    }

    .form-row {
      display: flex;
      gap: 16px;
    }

    .form-col {
      flex: 1;
    }

    .message {
      padding: 12px 16px;
      border-radius: 4px;
      margin-bottom: 16px;
      font-weight: 500;
    }

    .loading-message {
      background-color: #e3f2fd;
      color: #1976d2;
      border-left: 4px solid #1976d2;
    }

    .success-message {
      background-color: #e8f5e9;
      color: #2e7d32;
      border-left: 4px solid #2e7d32;
    }

    .error-message {
      background-color: #ffebee;
      color: #c62828;
      border-left: 4px solid #c62828;
    }

    /* Meeting preview styles */
    .meeting-preview-container {
      background-color: #f5f5f5;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      max-height: 500px;
      overflow-y: auto;
    }

    .meeting-preview-content {
      font-size: 14px;
    }

    /* Styles for the meeting preview dropdowns */
    .meeting-preview-content .form-select {
      width: 100%;
      padding: 6px 10px;
      font-size: 13px;
      margin-top: 4px;
    }

    .meeting-preview-content .assignment-person {
      margin-top: 4px;
    }

    .meeting-preview-content .task-assignee .form-select,
    .meeting-preview-content .cleaning-group .form-select {
      margin-top: 0;
    }

    .meeting-preview-content .zoom-info input {
      width: 100%;
      padding: 6px 10px;
      font-size: 13px;
      margin-top: 4px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
    }

    /* Meeting section styles */
    .meeting-header {
      margin-bottom: 16px;
      text-align: center;
    }

    .meeting-title {
      font-size: 18px;
      font-weight: bold;
      color: var(--primary-color);
    }

    .meeting-date {
      font-size: 16px;
      margin-top: 4px;
    }

    .meeting-section-title {
      background-color: var(--primary-color);
      color: white;
      padding: 8px 12px;
      font-weight: bold;
      margin: 16px 0 8px 0;
      border-radius: 4px;
    }

    .assignment-item {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .assignment-title {
      font-weight: 500;
      margin-bottom: 4px;
    }

    .tasks-title, .cleaning-title, .zoom-title {
      background-color: #9c27b0;
      color: white;
      padding: 8px 12px;
      font-weight: bold;
      margin: 16px 0 8px 0;
      border-radius: 4px;
    }

    .task-item, .cleaning-item, .zoom-info {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
    }

    .task-name, .cleaning-area {
      font-weight: 500;
      margin-bottom: 4px;
    }

    /* Styles for the meeting preview dropdowns */
    .meeting-preview-content .form-select {
      width: 100%;
      padding: 6px 10px;
      font-size: 13px;
      margin-top: 4px;
    }

    .meeting-preview-content .assignment-person {
      margin-top: 4px;
    }

    .meeting-preview-content .task-assignee .form-select,
    .meeting-preview-content .cleaning-group .form-select {
      margin-top: 0;
    }

    .meeting-preview-content .zoom-info input {
      width: 100%;
      padding: 6px 10px;
      font-size: 13px;
      margin-top: 4px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
    }

    .meeting-section-title {
      background-color: var(--primary-color);
      color: white;
      padding: 8px 12px;
      margin: 16px 0 8px;
      border-radius: 4px;
      font-weight: 500;
    }

    .assignment-item {
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
    }

    .assignment-title {
      font-weight: 500;
    }

    .assignment-person {
      color: #666;
      font-size: 13px;
    }

    .song-number {
      font-weight: 700;
      color: var(--primary-color);
    }
  </style>
</head>
<body>
  <div class="app-header">
    <button class="back-button" onclick="window.location.href='/admin/'">
      <i class="material-icons">arrow_back</i>
      <span>Atrás</span>
    </button>
    <div class="app-title">Administración de Reuniones Entre Semana</div>
  </div>

  <div class="container">
    <div class="card">
      <h2 class="card-title">Administración de Reuniones Entre Semana</h2>

      <div class="tabs">
        <div class="tab active" data-tab="schedule">Calendario de Reuniones</div>
        <div class="tab" data-tab="history">Reuniones Pasadas</div>
        <div class="tab" data-tab="members">Miembros</div>
        <div class="tab settings-tab" data-tab="settings">
          <i class="material-icons">settings</i>
        </div>
      </div>

      <!-- Meeting Schedule Tab -->
      <div id="schedule-tab" class="tab-content active">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3>Calendario de Reuniones</h3>
          <div style="display: flex; gap: 8px;">
            <button class="button button-primary" onclick="openAddWolMeetingModal()">
              <i class="material-icons">add_circle</i>
              Agregar Reunión
            </button>
          </div>
        </div>

        <div class="table-responsive">
          <table id="meetingTable">
            <thead>
              <tr>
                <th data-label="Fecha">Fecha</th>
                <th data-label="Hora">Hora</th>
                <th data-label="Lugar">Lugar</th>
                <th data-label="Presidente">Presidente</th>
                <th data-label="Acciones">Acciones</th>
              </tr>
            </thead>
            <tbody id="meetingTableBody">
              <!-- Table rows will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>

      <!-- Past Meetings Tab -->
      <div id="history-tab" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3>Reuniones Pasadas</h3>
        </div>

        <div class="table-responsive">
          <table id="historyTable">
            <thead>
              <tr>
                <th data-label="Fecha">Fecha</th>
                <th data-label="Hora">Hora</th>
                <th data-label="Lugar">Lugar</th>
                <th data-label="Presidente">Presidente</th>
                <th data-label="Acciones">Acciones</th>
              </tr>
            </thead>
            <tbody id="historyTableBody">
              <!-- Table rows will be populated by JavaScript -->
            </tbody>
          </table>
        </div>
      </div>

      <!-- Members Tab -->
      <div id="members-tab" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3>Configuración de Miembros</h3>
        </div>

        <div class="form-section">
          <h4>Miembros para Asignaciones</h4>

          <!-- Two-column layout for desktop -->
          <div style="display: flex; flex-wrap: wrap; gap: 20px;">
            <!-- Column 1 -->
            <div style="flex: 1; min-width: 300px;">
              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Oración
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden hacer oraciones (Elders, Ministerial Servants)"></i>
                </label>
                <select class="form-select" id="mainPrayerMembers" name="prayer_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>

              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Busquemos Perlas Escondidas
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden presentar la parte de Busquemos Perlas Escondidas (Ministerial Servants, Elders)"></i>
                </label>
                <select class="form-select" id="mainGemsMembers" name="gems_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>

              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Lectura de la Biblia
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden hacer la lectura de la Biblia (Ministerial Servants, Publishers)"></i>
                </label>
                <select class="form-select" id="mainBibleReadingMembers" name="bible_reading_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>

              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Nuestra Vida Cristiana
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden presentar partes de Nuestra Vida Cristiana (Elders, Ministerial Servants)"></i>
                </label>
                <select class="form-select" id="mainChristianLifeMembers" name="christian_life_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>

            <!-- Column 2 -->
            <div style="flex: 1; min-width: 300px;">
              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Tesoros de la Biblia
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden presentar partes de Tesoros de la Biblia (Elders, Ministerial Servants)"></i>
                </label>
                <select class="form-select" id="mainTreasuresMembers" name="treasures_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>



              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Seamos Mejores Maestros
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden presentar partes de Seamos Mejores Maestros (Ministerial Servants, Publishers)"></i>
                </label>
                <select class="form-select" id="mainMinistryMembers" name="ministry_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>

              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Discurso
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden presentar el Discurso en Seamos Mejores Maestros (Ministerial Servants, Publishers)"></i>
                </label>
                <select class="form-select" id="mainMinistryTalkMembers" name="ministry_talk_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>

              <div class="form-group" style="margin-bottom: 25px;">
                <label class="form-label">
                  Miembros para Estudio Bíblico de Congregación
                  <i class="bi bi-info-circle-fill" style="color: #6c757d; cursor: pointer; margin-left: 5px;"
                     title="Seleccione los miembros que pueden conducir el Estudio Bíblico de Congregación (Elders)"></i>
                </label>
                <select class="form-select" id="mainCongregationBibleStudyMembers" name="congregation_bible_study_members" multiple size="6" style="width: 100%;">
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>
          </div>
        </div>

        <div class="form-actions" style="margin-top: 20px; text-align: center;">
          <button type="button" class="button" id="saveMainMembersSettings" style="padding: 10px 20px; font-size: 16px;">Guardar Configuración de Miembros</button>
        </div>
      </div>

      <!-- Settings Tab -->
      <div id="settings-tab" class="tab-content">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3>Configuración de Reuniones</h3>
        </div>

        <!-- Settings Tab Navigation -->
        <div class="tabs" style="margin-bottom: 20px;">
          <div class="tab-button active" onclick="showSettingsTab('general-settings')">Configuración de Reuniones</div>
          <div class="tab-button" onclick="showSettingsTab('members-settings')">Miembros</div>
        </div>

        <!-- General Settings Tab Content -->
        <div id="general-settings" class="settings-tab-content active">
          <form id="settingsForm">
            <div class="form-section">
              <h4>Configuración General</h4>

              <div class="form-group">
                <label class="form-label">Día de la semana predeterminado</label>
                <select class="form-select" id="defaultDayOfWeek" name="default_day_of_week">
                  <option value="1">Lunes</option>
                  <option value="2">Martes</option>
                  <option value="3">Miércoles</option>
                  <option value="4">Jueves</option>
                  <option value="5" selected>Viernes</option>
                  <option value="6">Sábado</option>
                  <option value="0">Domingo</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">Hora predeterminada</label>
                <input type="time" class="form-input" id="defaultTime" name="default_time" value="19:30">
              </div>

              <div class="form-group">
                <label class="form-label">Lugar predeterminado</label>
                <input type="text" class="form-input" id="defaultLocation" name="default_location" value="Salon del Reino">
              </div>
            </div>

            <div class="form-section">
              <h4>Configuración de Zoom</h4>

              <div class="form-group">
                <label class="form-label">ID de reunión de Zoom predeterminado</label>
                <input type="text" class="form-input" id="defaultZoomMeetingId" name="default_zoom_meeting_id">
              </div>

              <div class="form-group">
                <label class="form-label">Contraseña de Zoom predeterminada</label>
                <input type="text" class="form-input" id="defaultZoomMeetingPassword" name="default_zoom_meeting_password">
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" class="button">Guardar Configuración</button>
            </div>
          </form>
        </div>

        <!-- Members Settings Tab Content -->
        <div id="members-settings" class="settings-tab-content">
          <div class="form-section">
            <h4>Configuración de Miembros</h4>

            <div class="form-group">
              <label class="form-label">Miembros para Oración</label>
              <select class="form-select" id="prayerMembers" name="prayer_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden hacer oraciones (Elders, Ministerial Servants)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Tesoros de la Biblia</label>
              <select class="form-select" id="treasuresMembers" name="treasures_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden presentar partes de Tesoros de la Biblia (Elders, Ministerial Servants)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Busquemos Perlas Escondidas</label>
              <select class="form-select" id="gemsMembers" name="gems_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden presentar la parte de Busquemos Perlas Escondidas (Ministerial Servants, Elders)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Lectura de la Biblia</label>
              <select class="form-select" id="bibleReadingMembers" name="bible_reading_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden hacer la lectura de la Biblia (Ministerial Servants, Publishers)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Seamos Mejores Maestros</label>
              <select class="form-select" id="ministryMembers" name="ministry_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden presentar partes de Seamos Mejores Maestros (Ministerial Servants, Publishers)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Discurso</label>
              <select class="form-select" id="ministryTalkMembers" name="ministry_talk_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden presentar el Discurso en Seamos Mejores Maestros (Ministerial Servants, Publishers)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Nuestra Vida Cristiana</label>
              <select class="form-select" id="christianLifeMembers" name="christian_life_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden presentar partes de Nuestra Vida Cristiana (Elders, Ministerial Servants)</small>
            </div>

            <div class="form-group">
              <label class="form-label">Miembros para Estudio Bíblico de Congregación</label>
              <select class="form-select" id="congregationBibleStudyMembers" name="congregation_bible_study_members" multiple size="5">
                <!-- Will be populated by JavaScript -->
              </select>
              <small>Seleccione los miembros que pueden conducir el Estudio Bíblico de Congregación (Elders)</small>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="button" id="saveMembersSettings">Guardar Configuración de Miembros</button>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Add Meeting Modal -->
  <div class="modal" id="addMeetingModal">
    <div class="modal-content">
      <button class="modal-close" onclick="closeAddMeetingModal()">&times;</button>
      <h2 class="modal-title">Agregar Reunión Entre Semana</h2>

      <form id="addMeetingForm">
        <div class="form-header">
          <h3>Reunión Vida y Ministerio Cristiano</h3>

          <div class="form-group">
            <label class="form-label" for="meetingDate">Fecha</label>
            <input type="date" class="form-input" id="meetingDate" name="date" required>
          </div>

          <!-- Time is always 7:30 PM, so we set it as a hidden field -->
          <input type="hidden" id="meetingTime" name="time" value="19:30">

          <!-- Location is always Kingdom Hall, so we set it as a hidden field -->
          <input type="hidden" id="meetingLocation" name="location" value="Kingdom Hall">
        </div>

        <!-- Meeting Preview Container - This will display the meeting like the frontend -->
        <div class="meeting-preview-container" id="meetingPreviewContainer">
          <div class="meeting-preview-content" id="meetingPreviewContent">
            <!-- Meeting content will be populated here in the same format as the frontend -->
          </div>
        </div>

        <!-- The form fields are now in the meeting preview container -->
        <!-- We keep the hidden fields for date, time, and location -->
        <!-- All other fields are now in the meeting preview with dropdowns -->

        <div class="form-actions">
          <button type="button" class="button button-secondary" onclick="closeAddMeetingModal()">Cancelar</button>
          <button type="submit" class="button">Agregar Reunión</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Edit Meeting Modal -->
  <div class="modal" id="editMeetingModal">
    <div class="modal-content view-meeting-modal">
      <div class="modal-header">
        <h3>Editar Reunión</h3>
        <span class="close" onclick="closeEditMeetingModal()">&times;</span>
      </div>
      <form id="editMeetingForm">
        <input type="hidden" id="editMeetingId" name="id">
        <div id="editMeetingContent" class="modal-body">
          <!-- Content will be populated dynamically to match the frontend format with dropdown selectors -->
        </div>
        <div class="modal-footer">
          <button type="button" class="button button-secondary" onclick="closeEditMeetingModal()">Cancelar</button>
          <button type="submit" class="button">Guardar Cambios</button>
        </div>
      </form>
    </div>
  </div>

  <!-- View Meeting Modal -->
  <div id="viewMeetingModal" class="modal">
    <div class="modal-content view-meeting-modal">
      <div class="modal-header">
        <h3>Ver Reunión</h3>
        <span class="close" onclick="closeViewMeetingModal()">&times;</span>
      </div>
      <div id="viewMeetingContent" class="modal-body">
        <!-- Content will be populated dynamically to match the frontend format -->
      </div>
      <div class="modal-footer">
        <button type="button" class="button button-secondary" onclick="closeViewMeetingModal()">Cerrar</button>
      </div>
    </div>
  </div>

  <!-- Add WOL Meeting Modal -->
  <div id="addWolMeetingModal" class="modal">
    <div class="modal-content">
      <button class="modal-close" onclick="closeAddWolMeetingModal()">&times;</button>
      <h2 class="modal-title">Agregar Reunión desde WOL</h2>

      <div class="form-group">
        <label class="form-label">Seleccione la semana:</label>
        <select class="form-select" id="wolWeekSelector">
          <option value="">Cargando semanas disponibles...</option>
        </select>
      </div>

      <div class="form-actions">
        <button type="button" class="button button-secondary" onclick="closeAddWolMeetingModal()">Cancelar</button>
        <button type="button" class="button" onclick="createWolMeeting()">Crear Reunión</button>
      </div>
    </div>
  </div>



  <style>
    .tabs {
      display: flex;
      margin-bottom: 15px;
      border-bottom: 1px solid #ddd;
    }

    .tab-button {
      padding: 10px 15px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 14px;
      opacity: 0.7;
    }

    .tab-button.active {
      opacity: 1;
      border-bottom: 2px solid #007bff;
      font-weight: bold;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    /* Settings tab content styles */
    .settings-tab-content {
      display: none;
    }

    .settings-tab-content.active {
      display: block;
    }
  </style>

  <!-- Loading and message elements -->
  <div id="loadingMessage" class="message loading-message" style="display: none;">Cargando...</div>
  <div id="successMessage" class="message success-message" style="display: none;">¡Operación exitosa!</div>
  <div id="errorMessage" class="message error-message" style="display: none;">Ha ocurrido un error.</div>

  <!-- Include API clients -->
  <script src="/admin/midweek-meeting-api.js?v=1.0.2"></script>

  <script>
    // Will be populated from API
    let meetings = [];
    let pastMeetings = [];
    let meetingParts = [];
    let members = [];
    let settings = {};

    // DOM elements
    let meetingTableBody;
    let historyTableBody;
    let addMeetingForm;
    let editMeetingForm;
    let settingsForm;

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      // Check authentication
      checkAuthentication();

      // Initialize tabs
      initTabs();

      // Initialize meeting schedule tab
      initMeetingScheduleTab();

      // Initialize past meetings tab
      initPastMeetingsTab();

      // Initialize members tab
      initMembersTab();

      // Initialize settings tab
      initSettingsTab();

      // Add event listeners for forms
      addMeetingForm = document.getElementById('addMeetingForm');
      editMeetingForm = document.getElementById('editMeetingForm');
      settingsForm = document.getElementById('settingsForm');

      addMeetingForm.addEventListener('submit', handleAddMeeting);
      editMeetingForm.addEventListener('submit', handleEditMeeting);
      settingsForm.addEventListener('submit', handleSaveSettings);

      // Populate chairmen dropdown
      populateChairmenDropdown();
    });

    // Check if user is authenticated and has admin role
    function checkAuthentication() {
      // Check if user is authenticated
      const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true';
      const userRole = localStorage.getItem('userRole');
      const memberData = localStorage.getItem('member');
      const token = localStorage.getItem('token');

      if (!isAuthenticated || !memberData || !token) {
        showError('No estás autenticado. Por favor, inicia sesión para acceder a esta página.');
        window.location.href = '/login.html?redirect=/admin/midweek-meeting.html';
        return false;
      }

      try {
        // Parse member data if needed
        const member = memberData ? JSON.parse(memberData) : null;

        // Check if user has appropriate role
        const allowedRoles = ['developer', 'overseer_coordinator', 'elder', 'ministerial_servant', 'Overseer', 'Coordinator', 'Elder', 'Ministerial Servant'];

        if (!allowedRoles.includes(userRole) && (!member || !allowedRoles.includes(member.role))) {
          console.log('User role not allowed:', userRole, member ? member.role : 'No member data');
          showError('No tienes permisos para acceder a esta página.');
          window.location.href = '/login.html?redirect=/admin/midweek-meeting.html';
          return false;
        }

        return true;
      } catch (error) {
        console.error('Error checking authentication:', error);
        showError('Error al verificar la autenticación.');
        window.location.href = '/login.html?redirect=/admin/midweek-meeting.html';
        return false;
      }
    }

    // Initialize tabs
    function initTabs() {
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove('active'));

          // Add active class to clicked tab
          this.classList.add('active');

          // Hide all tab content
          document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
          });

          // Show selected tab content
          const tabId = this.getAttribute('data-tab');
          document.getElementById(`${tabId}-tab`).classList.add('active');
        });
      });
    }

    // Initialize meeting schedule tab
    async function initMeetingScheduleTab() {
      meetingTableBody = document.getElementById('meetingTableBody');

      try {
        // Show loading message
        showLoading('Cargando reuniones...');

        // Fetch meetings from API
        meetings = await fetchMeetings();

        // Fetch members from API
        members = await fetchMembers();

        // Hide loading message
        hideLoading();

        // Render meetings
        renderMeetings();
      } catch (error) {
        console.error('Error initializing meeting schedule tab:', error);
        showError('Error al cargar las reuniones. Por favor, intente de nuevo.');
      }
    }

    // Fetch meetings from API
    async function fetchMeetings() {
      try {
        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        console.log('Fetching meetings from API...');

        // Try the WOL endpoint first
        const wolResponse = await fetch('/api/midweek/wol/meetings', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        let meetingsData = [];

        if (wolResponse.ok) {
          const wolData = await wolResponse.json();
          console.log('WOL API Response:', wolData);

          if (wolData.success && wolData.meetings) {
            meetingsData = wolData.meetings;
            console.log('Successfully fetched meetings from WOL endpoint:', meetingsData.length);
          }
        } else {
          console.log('WOL endpoint failed, falling back to regular endpoint');

          // Fallback to the regular API
          const response = await fetch('/api/midweek/meetings', {
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (!response.ok) {
            if (response.status === 401) {
              throw new Error('No tienes permiso para ver las reuniones. Por favor, inicia sesión nuevamente.');
            } else {
              throw new Error(`Error al cargar las reuniones: ${response.status}`);
            }
          }

          const data = await response.json();
          console.log('Regular API Response:', data);

          if (!data.success) {
            throw new Error(data.message || 'Error al cargar las reuniones');
          }

          meetingsData = data.meetings;
          console.log('Successfully fetched meetings from regular endpoint:', meetingsData.length);
        }

        // Sort all meetings by date (closest first)
        meetingsData.sort((a, b) => {
          const dateA = new Date(a.meeting_date);
          const dateB = new Date(b.meeting_date);
          return dateA - dateB;
        });

        console.log('Total meetings:', meetingsData.length);

        return meetingsData;
      } catch (error) {
        console.error('Error fetching meetings:', error);
        showError('Error al cargar las reuniones: ' + error.message);
        return [];
      }
    }

    // Initialize past meetings tab
    async function initPastMeetingsTab() {
      historyTableBody = document.getElementById('historyTableBody');

      try {
        // Show loading message when tab is activated
        document.querySelector('.tab[data-tab="history"]').addEventListener('click', function() {
          showLoading('Cargando reuniones pasadas...');

          try {
            // Filter for past meetings from the already loaded meetings array
            const today = new Date();
            today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison

            pastMeetings = meetings.filter(meeting => {
              const meetingDate = new Date(meeting.meeting_date);
              meetingDate.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison
              return meetingDate < today;
            });

            // Sort by date (newest first)
            pastMeetings.sort((a, b) => {
              const dateA = new Date(a.meeting_date);
              const dateB = new Date(b.meeting_date);
              return dateB - dateA;
            });

            hideLoading();
            renderPastMeetings();
          } catch (error) {
            console.error('Error loading past meetings:', error);
            hideLoading();
            showError('Error al cargar las reuniones pasadas.');
          }
        });
      } catch (error) {
        console.error('Error initializing past meetings tab:', error);
      }
    }

    // Initialize members tab
    async function initMembersTab() {
      try {
        // Show loading message when tab is activated
        document.querySelector('.tab[data-tab="members"]').addEventListener('click', async function() {
          showLoading('Cargando miembros...');

          try {
            // Fetch members if not already loaded
            if (!members || members.length === 0) {
              members = await fetchMembers();
            }

            // Get all member select elements
            const prayerMembersSelect = document.getElementById('mainPrayerMembers');
            const treasuresMembersSelect = document.getElementById('mainTreasuresMembers');
            const gemsMembersSelect = document.getElementById('mainGemsMembers');
            const bibleReadingMembersSelect = document.getElementById('mainBibleReadingMembers');
            const ministryMembersSelect = document.getElementById('mainMinistryMembers');
            const ministryTalkMembersSelect = document.getElementById('mainMinistryTalkMembers');
            const christianLifeMembersSelect = document.getElementById('mainChristianLifeMembers');
            const congregationBibleStudyMembersSelect = document.getElementById('mainCongregationBibleStudyMembers');

            // Clear existing options
            prayerMembersSelect.innerHTML = '';
            treasuresMembersSelect.innerHTML = '';
            gemsMembersSelect.innerHTML = '';
            bibleReadingMembersSelect.innerHTML = '';
            ministryMembersSelect.innerHTML = '';
            ministryTalkMembersSelect.innerHTML = '';
            christianLifeMembersSelect.innerHTML = '';
            congregationBibleStudyMembersSelect.innerHTML = '';

            // Filter members by role
            const elders = members.filter(m => m.role === 'elder' || m.role === 'Elder' || m.role === 'overseer_coordinator' || m.role === 'Overseer' || m.role === 'Coordinator');
            const ministerialServants = members.filter(m => m.role === 'ministerial_servant' || m.role === 'Ministerial Servant');
            const publishers = members.filter(m => m.role === 'publisher' || m.role === 'Publisher');

            // Populate prayer members (Elders, Ministerial Servants)
            [...elders, ...ministerialServants].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              prayerMembersSelect.appendChild(option);
            });

            // Populate treasures members (Elders, Ministerial Servants)
            [...elders, ...ministerialServants].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              treasuresMembersSelect.appendChild(option);
            });

            // Populate gems members (Ministerial Servants, Elders)
            [...ministerialServants, ...elders].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              gemsMembersSelect.appendChild(option);
            });

            // Populate Bible reading members (Ministerial Servants, Publishers)
            [...ministerialServants, ...publishers].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              bibleReadingMembersSelect.appendChild(option);
            });

            // Populate ministry members (Ministerial Servants, Publishers)
            [...ministerialServants, ...publishers].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              ministryMembersSelect.appendChild(option);
            });

            // Populate ministry talk members (Ministerial Servants, Publishers)
            [...ministerialServants, ...publishers].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              ministryTalkMembersSelect.appendChild(option);
            });

            // Populate Christian life members (Elders, Ministerial Servants)
            [...elders, ...ministerialServants].forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              christianLifeMembersSelect.appendChild(option);
            });

            // Populate congregation Bible study members (Elders)
            elders.forEach(member => {
              const option = document.createElement('option');
              option.value = member.id;
              option.textContent = member.name;
              congregationBibleStudyMembersSelect.appendChild(option);
            });

            // Add event listener to save button
            document.getElementById('saveMainMembersSettings').addEventListener('click', handleSaveMainMembersSettings);

            // Fetch and select current member settings
            await fetchMemberSettings();

            hideLoading();
          } catch (error) {
            console.error('Error loading members tab:', error);
            hideLoading();
            showError('Error al cargar los miembros. Por favor, intente de nuevo.');
          }
        });
      } catch (error) {
        console.error('Error initializing members tab:', error);
      }
    }

    // Fetch member settings
    async function fetchMemberSettings() {
      try {
        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        // Fetch member settings from API
        const response = await fetch('/api/midweek/members-settings?congregation_id=1', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          throw new Error(`Error al cargar la configuración de miembros: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Error al cargar la configuración de miembros');
        }

        const memberSettings = data.settings;

        // Select members in dropdowns
        if (memberSettings) {
          // Helper function to select options
          const selectOptions = (selectElement, values) => {
            if (!values || !Array.isArray(values)) return;

            for (let i = 0; i < selectElement.options.length; i++) {
              selectElement.options[i].selected = values.includes(selectElement.options[i].value);
            }
          };

          // Select members in each dropdown
          selectOptions(document.getElementById('mainPrayerMembers'), memberSettings.prayer_members);
          selectOptions(document.getElementById('mainTreasuresMembers'), memberSettings.treasures_members);
          selectOptions(document.getElementById('mainGemsMembers'), memberSettings.gems_members);
          selectOptions(document.getElementById('mainBibleReadingMembers'), memberSettings.bible_reading_members);
          selectOptions(document.getElementById('mainMinistryMembers'), memberSettings.ministry_members);
          selectOptions(document.getElementById('mainMinistryTalkMembers'), memberSettings.ministry_talk_members);
          selectOptions(document.getElementById('mainChristianLifeMembers'), memberSettings.christian_life_members);
          selectOptions(document.getElementById('mainCongregationBibleStudyMembers'), memberSettings.congregation_bible_study_members);
        }
      } catch (error) {
        console.error('Error fetching member settings:', error);
        // Don't show error to user, just log it
      }
    }

    // Handle save main members settings
    async function handleSaveMainMembersSettings() {
      try {
        showLoading('Guardando configuración de miembros...');

        // Get selected members from each dropdown
        const prayerMembers = Array.from(document.getElementById('mainPrayerMembers').selectedOptions).map(option => option.value);
        const treasuresMembers = Array.from(document.getElementById('mainTreasuresMembers').selectedOptions).map(option => option.value);
        const gemsMembers = Array.from(document.getElementById('mainGemsMembers').selectedOptions).map(option => option.value);
        const bibleReadingMembers = Array.from(document.getElementById('mainBibleReadingMembers').selectedOptions).map(option => option.value);
        const ministryMembers = Array.from(document.getElementById('mainMinistryMembers').selectedOptions).map(option => option.value);
        const ministryTalkMembers = Array.from(document.getElementById('mainMinistryTalkMembers').selectedOptions).map(option => option.value);
        const christianLifeMembers = Array.from(document.getElementById('mainChristianLifeMembers').selectedOptions).map(option => option.value);
        const congregationBibleStudyMembers = Array.from(document.getElementById('mainCongregationBibleStudyMembers').selectedOptions).map(option => option.value);

        // Create settings object
        const membersSettings = {
          congregation_id: 1, // Default congregation ID
          prayer_members: prayerMembers,
          treasures_members: treasuresMembers,
          gems_members: gemsMembers,
          bible_reading_members: bibleReadingMembers,
          ministry_members: ministryMembers,
          ministry_talk_members: ministryTalkMembers,
          christian_life_members: christianLifeMembers,
          congregation_bible_study_members: congregationBibleStudyMembers
        };

        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        // Save settings via API
        const response = await fetch('/api/midweek/members-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(membersSettings)
        });

        if (!response.ok) {
          throw new Error(`Error al guardar la configuración de miembros: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Error al guardar la configuración de miembros');
        }

        hideLoading();
        showSuccess('Configuración de miembros guardada correctamente');
      } catch (error) {
        console.error('Error saving members settings:', error);
        hideLoading();
        showError(`Error al guardar la configuración de miembros: ${error.message}`);
      }
    }

    // Render past meetings
    function renderPastMeetings() {
      historyTableBody.innerHTML = '';

      if (!pastMeetings || pastMeetings.length === 0) {
        historyTableBody.innerHTML = `
          <tr>
            <td colspan="5" style="text-align: center; padding: 24px;">
              No se encontraron reuniones pasadas.
            </td>
          </tr>
        `;
        return;
      }

      pastMeetings.forEach(meeting => {
        const row = document.createElement('tr');

        // Format date
        const date = new Date(meeting.meeting_date);
        const formattedDate = date.toLocaleDateString('es-ES', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        // Capitalize first letter
        const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

        // Format time
        const time = meeting.meeting_time || '19:30';
        const formattedTime = time.substring(0, 5);

        // Get chairman name
        let chairmanName = 'No asignado';
        if (meeting.chairman_id && members) {
          const chairman = members.find(m => m.id === parseInt(meeting.chairman_id));
          if (chairman) {
            chairmanName = chairman.name;
          }
        } else if (meeting.chairman_name) {
          chairmanName = meeting.chairman_name;
        }

        row.innerHTML = `
          <td>${capitalizedDate}</td>
          <td>${formattedTime}</td>
          <td>${meeting.meeting_location || 'Salón del Reino'}</td>
          <td>${chairmanName}</td>
          <td>
            <div class="action-buttons">
              <button class="button button-primary" onclick="openViewMeetingModal(${meeting.id})">
                <i class="material-icons">visibility</i>
                Ver
              </button>
            </div>
          </td>
        `;

        historyTableBody.appendChild(row);
      });
    }

    // Initialize settings tab
    async function initSettingsTab() {
      settingsForm = document.getElementById('settingsForm');

      try {
        // Show loading message when tab is activated
        document.querySelector('.tab[data-tab="settings"]').addEventListener('click', async function() {
          if (!settings.id) {
            showLoading('Cargando configuración...');

            try {
              // Use the regular API to fetch settings
              const response = await fetch('/api/midweek/settings');

              if (!response.ok) {
                throw new Error(`Failed to fetch settings: ${response.status}`);
              }

              const data = await response.json();

              if (!data.success) {
                throw new Error(data.message || 'Failed to fetch settings');
              }

              settings = data.settings;

              // Populate the form with settings
              if (settings) {
                document.getElementById('defaultDayOfWeek').value = settings.default_day_of_week || '5';
                document.getElementById('defaultTime').value = settings.default_time || '19:30';
                document.getElementById('defaultLocation').value = settings.default_location || 'Salon del Reino';
                document.getElementById('defaultZoomMeetingId').value = settings.default_zoom_meeting_id || '';
                document.getElementById('defaultZoomMeetingPassword').value = settings.default_zoom_meeting_password || '';
              }

              hideLoading();
            } catch (error) {
              console.error('Error loading settings:', error);
              hideLoading();
              showError('Error al cargar la configuración.');
            }
          }
        });
      } catch (error) {
        console.error('Error initializing settings tab:', error);
      }
    }

    // Handle save settings form submission
    async function handleSaveSettings(event) {
      event.preventDefault();

      try {
        showLoading('Guardando configuración...');

        // Get form data
        const formData = new FormData(settingsForm);
        const settingsData = {
          congregation_id: 1, // Default congregation ID
          default_day_of_week: formData.get('default_day_of_week'),
          default_time: formData.get('default_time'),
          default_location: formData.get('default_location'),
          default_zoom_meeting_id: formData.get('default_zoom_meeting_id'),
          default_zoom_meeting_password: formData.get('default_zoom_meeting_password')
        };

        // Save settings
        const response = await fetch('/api/midweek/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(settingsData)
        });

        if (!response.ok) {
          throw new Error(`Failed to update settings: ${response.status}`);
        }

        const responseData = await response.json();

        if (!responseData.success) {
          throw new Error(responseData.message || 'Failed to update settings');
        }

        hideLoading();
        showSuccess('Configuración guardada correctamente');

        // Update settings object
        const settingsResponse = await fetch('/api/midweek/settings');

        if (!settingsResponse.ok) {
          throw new Error(`Failed to fetch settings: ${settingsResponse.status}`);
        }

        const settingsResponseData = await settingsResponse.json();

        if (!settingsResponseData.success) {
          throw new Error(settingsResponseData.message || 'Failed to fetch settings');
        }

        settings = settingsResponseData.settings;
      } catch (error) {
        console.error('Error saving settings:', error);
        hideLoading();
        showError('Error al guardar la configuración.');
      }
    }



    // Render past meetings
    function renderPastMeetings() {
      historyTableBody.innerHTML = '';

      if (!pastMeetings || pastMeetings.length === 0) {
        historyTableBody.innerHTML = `
          <tr>
            <td colspan="5" style="text-align: center; padding: 24px;">
              No se encontraron reuniones pasadas.
            </td>
          </tr>
        `;
        return;
      }

      pastMeetings.forEach(meeting => {
        const row = document.createElement('tr');

        // Format date
        const date = new Date(meeting.meeting_date);
        const formattedDate = date.toLocaleDateString('es-ES', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        // Capitalize first letter
        const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

        // Format time
        const time = meeting.meeting_time || '19:30';
        const formattedTime = time.substring(0, 5);

        // Get chairman name
        let chairmanName = 'No asignado';
        if (meeting.chairman_id && members) {
          const chairman = members.find(m => m.id === parseInt(meeting.chairman_id));
          if (chairman) {
            chairmanName = chairman.name;
          }
        } else if (meeting.chairman_name) {
          chairmanName = meeting.chairman_name;
        }

        row.innerHTML = `
          <td>${capitalizedDate}</td>
          <td>${formattedTime}</td>
          <td>${meeting.meeting_location || 'Salón del Reino'}</td>
          <td>${chairmanName}</td>
          <td>
            <div class="action-buttons">
              <button class="button button-primary" onclick="openViewMeetingModal(${meeting.id})">
                <i class="material-icons">visibility</i>
                Ver
              </button>
              <button class="button button-secondary" onclick="openEditMeetingModal(${meeting.id})">
                <i class="material-icons">edit</i>
                Editar
              </button>
              <button class="button button-secondary" onclick="handleDeleteMeeting(${meeting.id})">
                <i class="material-icons">delete</i>
                Eliminar
              </button>
            </div>
          </td>
        `;

        historyTableBody.appendChild(row);
      });
    }

    // Initialize settings tab
    async function initSettingsTab() {
      settingsForm = document.getElementById('settingsForm');

      try {
        // Show loading message when tab is activated
        document.querySelector('.tab[data-tab="settings"]').addEventListener('click', async function() {
          if (!settings.id) {
            showLoading('Cargando configuración...');

            try {
              // Use the regular API to fetch settings
              const response = await fetch('/api/midweek/settings');

              if (!response.ok) {
                throw new Error(`Failed to fetch settings: ${response.status}`);
              }

              const data = await response.json();

              if (!data.success) {
                throw new Error(data.message || 'Failed to fetch settings');
              }

              settings = data.settings;

              // Populate the form with settings
              if (settings) {
                document.getElementById('defaultDayOfWeek').value = settings.default_day_of_week || '5';
                document.getElementById('defaultTime').value = settings.default_time || '19:30';
                document.getElementById('defaultLocation').value = settings.default_location || 'Salon del Reino';
                document.getElementById('defaultZoomMeetingId').value = settings.default_zoom_meeting_id || '';
                document.getElementById('defaultZoomMeetingPassword').value = settings.default_zoom_meeting_password || '';
              }

              hideLoading();
            } catch (error) {
              console.error('Error loading settings:', error);
              hideLoading();
              showError('Error al cargar la configuración.');
            }
          }
        });
      } catch (error) {
        console.error('Error initializing settings tab:', error);
      }
    }

    // Handle save settings form submission
    async function handleSaveSettings(event) {
      event.preventDefault();

      try {
        showLoading('Guardando configuración...');

        // Get form data
        const formData = new FormData(settingsForm);
        const settingsData = {
          congregation_id: 1, // Default congregation ID
          default_day_of_week: formData.get('default_day_of_week'),
          default_time: formData.get('default_time'),
          default_location: formData.get('default_location'),
          default_zoom_meeting_id: formData.get('default_zoom_meeting_id'),
          default_zoom_meeting_password: formData.get('default_zoom_meeting_password')
        };

        // Save settings
        const response = await fetch('/api/midweek/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(settingsData)
        });

        if (!response.ok) {
          throw new Error(`Failed to update settings: ${response.status}`);
        }

        const responseData = await response.json();

        if (!responseData.success) {
          throw new Error(responseData.message || 'Failed to update settings');
        }

        hideLoading();
        showSuccess('Configuración guardada correctamente');

        // Update settings object
        const settingsResponse = await fetch('/api/midweek/settings');

        if (!settingsResponse.ok) {
          throw new Error(`Failed to fetch settings: ${settingsResponse.status}`);
        }

        const settingsResponseData = await settingsResponse.json();

        if (!settingsResponseData.success) {
          throw new Error(settingsResponseData.message || 'Failed to fetch settings');
        }

        settings = settingsResponseData.settings;
      } catch (error) {
        console.error('Error saving settings:', error);
        hideLoading();
        showError('Error al guardar la configuración.');
      }
    }

    // Render meetings
    function renderMeetings() {
      meetingTableBody.innerHTML = '';

      if (!meetings || meetings.length === 0) {
        meetingTableBody.innerHTML = `
          <tr>
            <td colspan="5" style="text-align: center; padding: 24px;">
              No se encontraron reuniones. Agregue su primera reunión.
            </td>
          </tr>
        `;
        return;
      }

      console.log('Rendering meetings:', meetings);

      // Filter for upcoming meetings
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison

      // Sort by date (closest first) and filter for upcoming meetings
      const sortedMeetings = [...meetings]
        .filter(meeting => {
          const meetingDate = new Date(meeting.meeting_date);
          meetingDate.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison
          return meetingDate >= today;
        })
        .sort((a, b) => {
          const dateA = new Date(a.meeting_date);
          const dateB = new Date(b.meeting_date);
          return dateA - dateB; // Closest first
        });

      sortedMeetings.forEach(meeting => {
        const row = document.createElement('tr');

        // Format date
        const date = new Date(meeting.meeting_date);
        const formattedDate = date.toLocaleDateString('es-ES', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });

        // Capitalize first letter
        const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

        // Format time
        const time = meeting.meeting_time || '19:30';
        let formattedTime = time;

        // Handle different time formats
        if (time.includes(':')) {
          const timeParts = time.split(':');
          const hours = parseInt(timeParts[0]);
          const minutes = timeParts[1];
          formattedTime = `${hours}:${minutes}`;
        }

        // Get chairman name
        let chairmanName = 'No asignado';
        if (meeting.chairman_id && members && members.length > 0) {
          const chairman = members.find(m => m.id === parseInt(meeting.chairman_id));
          if (chairman) {
            chairmanName = chairman.name;
          }
        } else if (meeting.chairman_name) {
          chairmanName = meeting.chairman_name;
        }

        // Get location
        const location = meeting.meeting_location || 'Salón del Reino';

        row.innerHTML = `
          <td>${capitalizedDate}</td>
          <td>${formattedTime}</td>
          <td>${location}</td>
          <td>${chairmanName}</td>
          <td>
            <div class="action-buttons">
              <button class="button button-primary" onclick="openViewMeetingModal(${meeting.id})">
                <i class="material-icons">visibility</i>
                Ver
              </button>
              <button class="button button-secondary" onclick="openEditMeetingModal(${meeting.id})">
                <i class="material-icons">edit</i>
                Editar
              </button>
              <button class="button button-secondary" onclick="handleDeleteMeeting(${meeting.id})">
                <i class="material-icons">delete</i>
                Eliminar
              </button>
            </div>
          </td>
        `;

        meetingTableBody.appendChild(row);
      });
    }

    // JW.org integration functions
    function openFetchFromJWModal() {
      document.getElementById('fetchFromJWModal').style.display = 'flex';
      document.getElementById('jwOrgUrl').focus();

      // Load workbooks
      loadWorkbooks();
    }

    // WOL integration functions
    async function openAddWolMeetingModal() {
      document.getElementById('addWolMeetingModal').style.display = 'flex';

      // Load weeks for the current year
      await loadAvailableWeeks();
    }

    // Load available weeks from the API
    async function loadAvailableWeeks() {
      const weekSelector = document.getElementById('wolWeekSelector');
      const currentYear = new Date().getFullYear();

      try {
        // Show loading state
        weekSelector.innerHTML = '<option value="">Cargando semanas disponibles...</option>';

        // Generate weeks for the specified year directly in the frontend
        console.log(`Generating weeks for ${currentYear} according to JW.org calendar...`);

        const generatedWeeks = [];
        const maxWeek = 52; // Some years might have 53 weeks, but we'll stick with 52 for simplicity

        // Month names in Spanish
        const monthNames = [
          'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
          'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
        ];

        // Find the first Monday of the year
        // According to JW.org, the first week of the year is the week containing January 1
        // If January 1 is not a Monday, then the first week starts on the Monday before January 1
        const firstDayOfYear = new Date(currentYear, 0, 1);
        const firstDayOfWeek = firstDayOfYear.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Calculate days to subtract to get to the previous Monday
        // If it's already Monday (1), then subtract 0
        // If it's Tuesday (2), subtract 1, etc.
        // If it's Sunday (0), subtract 6
        const daysToSubtract = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

        // Calculate the first Monday of the year (might be in the previous year)
        const firstMonday = new Date(currentYear, 0, 1 - daysToSubtract);

        console.log(`First day of ${currentYear} is ${firstDayOfYear.toDateString()}, a ${['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][firstDayOfWeek]}`);
        console.log(`First Monday for week 1 of ${currentYear} is ${firstMonday.toDateString()}`);

        // Generate week data for each week
        for (let weekNumber = 1; weekNumber <= maxWeek; weekNumber++) {
          // Calculate start date (Monday) for this week
          const startDate = new Date(firstMonday);
          startDate.setDate(firstMonday.getDate() + (weekNumber - 1) * 7);

          // Calculate end date (Sunday) for this week
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6);

          // Calculate the Friday date for midweek meeting
          const fridayDate = new Date(startDate);
          fridayDate.setDate(startDate.getDate() + 4); // Monday + 4 days = Friday

          // Format dates for display
          const startDay = startDate.getDate();
          const startMonth = startDate.getMonth();
          const startYear = startDate.getFullYear();

          const endDay = endDate.getDate();
          const endMonth = endDate.getMonth();
          const endYear = endDate.getFullYear();

          const fridayDay = fridayDate.getDate();
          const fridayMonth = fridayDate.getMonth();
          const fridayYear = fridayDate.getFullYear();

          // Create title in the format "Semana del 30 de diciembre al 5 de enero"
          let title;
          if (startYear === endYear) {
            title = `Semana del ${startDay} de ${monthNames[startMonth]} al ${endDay} de ${monthNames[endMonth]} de ${startYear}`;
          } else {
            title = `Semana del ${startDay} de ${monthNames[startMonth]} de ${startYear} al ${endDay} de ${monthNames[endMonth]} de ${endYear}`;
          }

          // Format dates as ISO strings (YYYY-MM-DD)
          const formattedStartDate = `${startYear}-${String(startMonth + 1).padStart(2, '0')}-${String(startDay).padStart(2, '0')}`;
          const formattedEndDate = `${endYear}-${String(endMonth + 1).padStart(2, '0')}-${String(endDay).padStart(2, '0')}`;
          const formattedFridayDate = `${fridayYear}-${String(fridayMonth + 1).padStart(2, '0')}-${String(fridayDay).padStart(2, '0')}`;

          // Create a unique week ID in the format used by JW.org: r4/lp-s/YEAR/WEEK
          const jwOrgWeekId = `r4/lp-s/${currentYear}/${weekNumber}`;

          // Add the week to the list
          generatedWeeks.push({
            week_id: jwOrgWeekId,
            workbook_id: `${currentYear}`,
            title,
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            meeting_date: formattedFridayDate, // Default meeting date (Friday)
            week_number: weekNumber,
            year: currentYear
          });
        }

        console.log(`Generated ${generatedWeeks.length} weeks for ${currentYear}`);

        // Create a simple workbook object for the year
        const generatedWorkbooks = [{
          workbook_id: `${currentYear}`,
          title: `Reuniones ${currentYear}`,
          start_date: `${currentYear}-01-01`,
          end_date: `${currentYear}-12-31`
        }];

        // Create a mock response
        const response = {
          success: true,
          workbooks: generatedWorkbooks,
          weeks: generatedWeeks
        };
        console.log('API response:', response);

        // Check if we have weeks in the response
        if (!response || !response.weeks || response.weeks.length === 0) {
          console.log('No weeks found in the API response');
          weekSelector.innerHTML = '<option value="">No se encontraron semanas disponibles</option>';
          return;
        }

        const weeks = response.weeks;

        // Sort weeks by week number
        console.log('Sorting weeks by week number...');
        const sortedWeeks = [...weeks].sort((a, b) => {
          const weekNumA = parseInt(a.week_id.split('/')[1]);
          const weekNumB = parseInt(b.week_id.split('/')[1]);
          return weekNumA - weekNumB;
        });
        console.log('Sorted weeks:', sortedWeeks);

        // Populate the select with weeks
        console.log('Populating select with weeks...');
        weekSelector.innerHTML = '<option value="">Seleccione una semana</option>';
        sortedWeeks.forEach(week => {
          console.log('Adding week to select:', week);
          const option = document.createElement('option');
          option.value = week.week_id;
          option.textContent = week.title;
          weekSelector.appendChild(option);
        });
      } catch (error) {
        console.error('Error loading weeks:', error);
        weekSelector.innerHTML = '<option value="">Error al cargar semanas</option>';
      }
    }

    function closeAddWolMeetingModal() {
      document.getElementById('addWolMeetingModal').style.display = 'none';
    }

    async function loadWolWeeksForWorkbook() {
      const workbookValue = document.getElementById('wolWorkbookSelector').value;
      const weekSelector = document.getElementById('wolWeekSelector');

      if (!workbookValue) {
        weekSelector.innerHTML = '<option value="">Primero seleccione un mes</option>';
        weekSelector.disabled = true;
        return;
      }

      try {
        // Show loading state
        weekSelector.innerHTML = '<option value="">Cargando semanas...</option>';
        weekSelector.disabled = true;

        // Generate weeks for the specified year directly in the frontend
        const currentYear = new Date().getFullYear();
        console.log(`Generating weeks for ${currentYear} according to JW.org calendar...`);

        const generatedWeeks = [];
        const maxWeek = 52; // Some years might have 53 weeks, but we'll stick with 52 for simplicity

        // Month names in Spanish
        const monthNames = [
          'enero', 'febrero', 'marzo', 'abril', 'mayo', 'junio',
          'julio', 'agosto', 'septiembre', 'octubre', 'noviembre', 'diciembre'
        ];

        // Find the first Monday of the year
        // According to JW.org, the first week of the year is the week containing January 1
        // If January 1 is not a Monday, then the first week starts on the Monday before January 1
        const firstDayOfYear = new Date(currentYear, 0, 1);
        const firstDayOfWeek = firstDayOfYear.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Calculate days to subtract to get to the previous Monday
        // If it's already Monday (1), then subtract 0
        // If it's Tuesday (2), subtract 1, etc.
        // If it's Sunday (0), subtract 6
        const daysToSubtract = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

        // Calculate the first Monday of the year (might be in the previous year)
        const firstMonday = new Date(currentYear, 0, 1 - daysToSubtract);

        console.log(`First day of ${currentYear} is ${firstDayOfYear.toDateString()}, a ${['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][firstDayOfWeek]}`);
        console.log(`First Monday for week 1 of ${currentYear} is ${firstMonday.toDateString()}`);

        // Generate week data for each week
        for (let weekNumber = 1; weekNumber <= maxWeek; weekNumber++) {
          // Calculate start date (Monday) for this week
          const startDate = new Date(firstMonday);
          startDate.setDate(firstMonday.getDate() + (weekNumber - 1) * 7);

          // Calculate end date (Sunday) for this week
          const endDate = new Date(startDate);
          endDate.setDate(startDate.getDate() + 6);

          // Calculate the Friday date for midweek meeting
          const fridayDate = new Date(startDate);
          fridayDate.setDate(startDate.getDate() + 4); // Monday + 4 days = Friday

          // Format dates for display
          const startDay = startDate.getDate();
          const startMonth = startDate.getMonth();
          const startYear = startDate.getFullYear();

          const endDay = endDate.getDate();
          const endMonth = endDate.getMonth();
          const endYear = endDate.getFullYear();

          const fridayDay = fridayDate.getDate();
          const fridayMonth = fridayDate.getMonth();
          const fridayYear = fridayDate.getFullYear();

          // Create title in the format "Semana del 30 de diciembre al 5 de enero"
          let title;
          if (startYear === endYear) {
            title = `Semana del ${startDay} de ${monthNames[startMonth]} al ${endDay} de ${monthNames[endMonth]} de ${startYear}`;
          } else {
            title = `Semana del ${startDay} de ${monthNames[startMonth]} de ${startYear} al ${endDay} de ${monthNames[endMonth]} de ${endYear}`;
          }

          // Format dates as ISO strings (YYYY-MM-DD)
          const formattedStartDate = `${startYear}-${String(startMonth + 1).padStart(2, '0')}-${String(startDay).padStart(2, '0')}`;
          const formattedEndDate = `${endYear}-${String(endMonth + 1).padStart(2, '0')}-${String(endDay).padStart(2, '0')}`;
          const formattedFridayDate = `${fridayYear}-${String(fridayMonth + 1).padStart(2, '0')}-${String(fridayDay).padStart(2, '0')}`;

          // Create a unique week ID in the format used by JW.org: r4/lp-s/YEAR/WEEK
          const jwOrgWeekId = `r4/lp-s/${currentYear}/${weekNumber}`;

          // Add the week to the list
          generatedWeeks.push({
            week_id: jwOrgWeekId,
            workbook_id: `${currentYear}`,
            title,
            start_date: formattedStartDate,
            end_date: formattedEndDate,
            meeting_date: formattedFridayDate, // Default meeting date (Friday)
            week_number: weekNumber,
            year: currentYear
          });
        }

        console.log(`Generated ${generatedWeeks.length} weeks for ${currentYear}`);

        const weeks = generatedWeeks;

        if (!weeks || weeks.length === 0) {
          weekSelector.innerHTML = '<option value="">No se encontraron semanas</option>';
          return;
        }

        // Filter weeks for the selected workbook
        const filteredWeeks = weeks.filter(week => {
          return week.workbook_id === workbookValue;
        });

        if (filteredWeeks.length === 0) {
          weekSelector.innerHTML = '<option value="">No se encontraron semanas para este mes</option>';
          return;
        }

        // Populate the select with weeks
        weekSelector.innerHTML = '<option value="">Seleccione una semana</option>';
        filteredWeeks.forEach(week => {
          const option = document.createElement('option');
          option.value = week.week_id;
          option.textContent = week.title;
          weekSelector.appendChild(option);
        });

        weekSelector.disabled = false;
      } catch (error) {
        console.error('Error loading weeks:', error);
        weekSelector.innerHTML = '<option value="">Error al cargar semanas</option>';
      }
    }

    async function createWolMeeting() {
      const weekId = document.getElementById('wolWeekSelector').value;

      if (!weekId) {
        showError('Por favor, seleccione una semana');
        return;
      }

      try {
        // Show loading message
        showLoading('Creando reunión...');

        // Parse the week ID to extract year and week number
        // The week ID format is r4/lp-s/YEAR/WEEK
        const parts = weekId.split('/');
        const year = parts[2];
        const weekNumber = parts[3];

        console.log(`Parsed week ID: year=${year}, weekNumber=${weekNumber}`);

        console.log('Creating meeting with week ID:', weekId);

        // Find the first Monday of the year
        const firstDayOfYear = new Date(parseInt(year), 0, 1);
        const firstDayOfWeek = firstDayOfYear.getDay(); // 0 = Sunday, 1 = Monday, etc.

        // Calculate days to subtract to get to the previous Monday
        const daysToSubtract = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;

        // Calculate the first Monday of the year (might be in the previous year)
        const firstMonday = new Date(parseInt(year), 0, 1 - daysToSubtract);

        console.log(`First day of ${year} is ${firstDayOfYear.toDateString()}, a ${['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][firstDayOfWeek]}`);
        console.log(`First Monday for week 1 of ${year} is ${firstMonday.toDateString()}`);

        // Calculate the Monday of the selected week
        const selectedWeekMonday = new Date(firstMonday);
        selectedWeekMonday.setDate(firstMonday.getDate() + (parseInt(weekNumber) - 1) * 7);

        // Calculate the Friday of the selected week (Monday + 4 days)
        const selectedWeekFriday = new Date(selectedWeekMonday);
        selectedWeekFriday.setDate(selectedWeekMonday.getDate() + 4);

        console.log(`Selected week ${weekNumber} Monday: ${selectedWeekMonday.toDateString()}`);
        console.log(`Selected week ${weekNumber} Friday: ${selectedWeekFriday.toDateString()}`);

        // Format the date as YYYY-MM-DD
        const meetingDate = selectedWeekFriday.toISOString().split('T')[0];

        console.log('Calculated meeting date (Friday):', meetingDate);

        // Get default settings from the settings object
        const defaultTime = settings.default_time || '19:30';
        const defaultLocation = settings.default_location || 'Salón del Reino';

        // Create meeting data with the correct week_id format
        // The server expects either 'r4/lp-s/YEAR/WEEK' or 'YEAR/WEEK'
        const meetingData = {
          week_id: `r4/lp-s/${year}/${weekNumber}`,  // Use the full format expected by the server
          year: parseInt(year),
          week_number: parseInt(weekNumber),
          meeting_date: meetingDate,
          meeting_time: defaultTime,
          meeting_location: defaultLocation
        };

        console.log('Meeting data:', JSON.stringify(meetingData));

        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        // Use the actual API to create the meeting
        const response = await fetch('/api/midweek/wol/meetings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(meetingData)
        });

        console.log('Create meeting response status:', response.status);

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error('No tienes permiso para crear reuniones. Por favor, inicia sesión nuevamente.');
          } else if (response.status === 403) {
            throw new Error('No tienes los permisos necesarios para crear reuniones.');
          } else {
            throw new Error(`Error al crear la reunión: ${response.status}`);
          }
        }

        const responseData = await response.json();
        console.log('Create meeting response data:', responseData);

        // Hide loading message
        hideLoading();

        if (responseData.success) {
          showSuccess('Reunión creada correctamente');
          closeAddWolMeetingModal();

          // Refresh the meetings list from the server
          console.log('Refreshing meetings list after creation...');
          meetings = await fetchMeetings();

          // Render the updated meetings
          console.log('Rendering updated meetings:', meetings);
          renderMeetings();
        } else {
          showError(responseData.message || 'Error al crear la reunión');
        }
      } catch (error) {
        console.error('Error creating meeting:', error);
        hideLoading();
        showError('Error al crear la reunión: ' + error.message);
      }
    }

    function closeFetchFromJWModal() {
      document.getElementById('fetchFromJWModal').style.display = 'none';
    }

    function showTab(tabId) {
      // Hide all tab contents
      const tabContents = document.querySelectorAll('.tab-content');
      tabContents.forEach(content => {
        content.classList.remove('active');
      });

      // Deactivate all tab buttons
      const tabButtons = document.querySelectorAll('.tab-button');
      tabButtons.forEach(button => {
        button.classList.remove('active');
      });

      // Show the selected tab content
      document.getElementById(tabId).classList.add('active');

      // Activate the selected tab button
      document.querySelector(`.tab-button[onclick="showTab('${tabId}')"]`).classList.add('active');
    }

    // Function to handle settings tabs
    function showSettingsTab(tabId) {
      // Hide all settings tab contents
      const tabContents = document.querySelectorAll('.settings-tab-content');
      tabContents.forEach(content => {
        content.classList.remove('active');
      });

      // Deactivate all settings tab buttons
      const tabButtons = document.querySelectorAll('.tabs .tab-button');
      tabButtons.forEach(button => {
        button.classList.remove('active');
      });

      // Show the selected settings tab content
      document.getElementById(tabId).classList.add('active');

      // Activate the selected settings tab button
      document.querySelector(`.tab-button[onclick="showSettingsTab('${tabId}')"]`).classList.add('active');

      // If showing members tab, populate the member dropdowns
      if (tabId === 'members-settings') {
        populateMembersSettings();
      }
    }

    // Function to populate members settings
    async function populateMembersSettings() {
      try {
        // Show loading message
        showLoading('Cargando miembros...');

        // Fetch members if not already loaded
        if (!members || members.length === 0) {
          members = await fetchMembers();
        }

        // Get all member select elements
        const prayerMembersSelect = document.getElementById('prayerMembers');
        const treasuresMembersSelect = document.getElementById('treasuresMembers');
        const gemsMembersSelect = document.getElementById('gemsMembers');
        const bibleReadingMembersSelect = document.getElementById('bibleReadingMembers');
        const ministryMembersSelect = document.getElementById('ministryMembers');
        const ministryTalkMembersSelect = document.getElementById('ministryTalkMembers');
        const christianLifeMembersSelect = document.getElementById('christianLifeMembers');
        const congregationBibleStudyMembersSelect = document.getElementById('congregationBibleStudyMembers');

        // Clear existing options
        prayerMembersSelect.innerHTML = '';
        treasuresMembersSelect.innerHTML = '';
        gemsMembersSelect.innerHTML = '';
        bibleReadingMembersSelect.innerHTML = '';
        ministryMembersSelect.innerHTML = '';
        ministryTalkMembersSelect.innerHTML = '';
        christianLifeMembersSelect.innerHTML = '';
        congregationBibleStudyMembersSelect.innerHTML = '';

        // Filter members by role
        const elders = members.filter(m => m.role === 'elder' || m.role === 'Elder' || m.role === 'overseer_coordinator' || m.role === 'Overseer' || m.role === 'Coordinator');
        const ministerialServants = members.filter(m => m.role === 'ministerial_servant' || m.role === 'Ministerial Servant');
        const publishers = members.filter(m => m.role === 'publisher' || m.role === 'Publisher');

        // Populate prayer members (Elders, Ministerial Servants)
        [...elders, ...ministerialServants].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          prayerMembersSelect.appendChild(option);
        });

        // Populate treasures members (Elders, Ministerial Servants)
        [...elders, ...ministerialServants].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          treasuresMembersSelect.appendChild(option);
        });

        // Populate gems members (Ministerial Servants, Elders)
        [...ministerialServants, ...elders].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          gemsMembersSelect.appendChild(option);
        });

        // Populate Bible reading members (Ministerial Servants, Publishers)
        [...ministerialServants, ...publishers].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          bibleReadingMembersSelect.appendChild(option);
        });

        // Populate ministry members (Ministerial Servants, Publishers)
        [...ministerialServants, ...publishers].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          ministryMembersSelect.appendChild(option);
        });

        // Populate ministry talk members (Ministerial Servants, Publishers)
        [...ministerialServants, ...publishers].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          ministryTalkMembersSelect.appendChild(option);
        });

        // Populate Christian life members (Elders, Ministerial Servants)
        [...elders, ...ministerialServants].forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          christianLifeMembersSelect.appendChild(option);
        });

        // Populate congregation Bible study members (Elders)
        elders.forEach(member => {
          const option = document.createElement('option');
          option.value = member.id;
          option.textContent = member.name;
          congregationBibleStudyMembersSelect.appendChild(option);
        });

        // Add event listener to save button
        document.getElementById('saveMembersSettings').addEventListener('click', handleSaveMembersSettings);

        // Hide loading message
        hideLoading();
      } catch (error) {
        console.error('Error populating members settings:', error);
        hideLoading();
        showError('Error al cargar los miembros. Por favor, intente de nuevo.');
      }
    }

    // Function to handle saving members settings
    async function handleSaveMembersSettings() {
      try {
        showLoading('Guardando configuración de miembros...');

        // Get selected members from each dropdown
        const prayerMembers = Array.from(document.getElementById('prayerMembers').selectedOptions).map(option => option.value);
        const treasuresMembers = Array.from(document.getElementById('treasuresMembers').selectedOptions).map(option => option.value);
        const bibleReadingMembers = Array.from(document.getElementById('bibleReadingMembers').selectedOptions).map(option => option.value);
        const ministryMembers = Array.from(document.getElementById('ministryMembers').selectedOptions).map(option => option.value);
        const ministryTalkMembers = Array.from(document.getElementById('ministryTalkMembers').selectedOptions).map(option => option.value);
        const christianLifeMembers = Array.from(document.getElementById('christianLifeMembers').selectedOptions).map(option => option.value);
        const congregationBibleStudyMembers = Array.from(document.getElementById('congregationBibleStudyMembers').selectedOptions).map(option => option.value);

        // Create settings object
        const membersSettings = {
          prayer_members: prayerMembers,
          treasures_members: treasuresMembers,
          bible_reading_members: bibleReadingMembers,
          ministry_members: ministryMembers,
          ministry_talk_members: ministryTalkMembers,
          christian_life_members: christianLifeMembers,
          congregation_bible_study_members: congregationBibleStudyMembers
        };

        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        // Save settings via API
        const response = await fetch('/api/midweek/members-settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(membersSettings)
        });

        if (!response.ok) {
          throw new Error(`Error al guardar la configuración de miembros: ${response.status}`);
        }

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Error al guardar la configuración de miembros');
        }

        hideLoading();
        showSuccess('Configuración de miembros guardada correctamente');
      } catch (error) {
        console.error('Error saving members settings:', error);
        hideLoading();
        showError(`Error al guardar la configuración de miembros: ${error.message}`);
      }
    }

    // Load workbooks from JW.org
    async function loadWorkbooks() {
      try {
        const workbookSelect = document.getElementById('workbookSelect');
        workbookSelect.innerHTML = '<option value="">Cargando cuadernos...</option>';

        // Fetch workbooks from JW.org
        const workbooks = await fetchWorkbooksFromJW();

        if (!workbooks || workbooks.length === 0) {
          workbookSelect.innerHTML = '<option value="">No se encontraron cuadernos</option>';
          return;
        }

        // Populate the select with workbooks
        workbookSelect.innerHTML = '<option value="">Seleccione un cuaderno</option>';
        workbooks.forEach(workbook => {
          const option = document.createElement('option');
          option.value = workbook.fullUrl;
          option.textContent = workbook.title;
          workbookSelect.appendChild(option);
        });
      } catch (error) {
        console.error('Error loading workbooks:', error);
        document.getElementById('workbookSelect').innerHTML = '<option value="">Error al cargar cuadernos</option>';
      }
    }

    // Handle workbook selection
    async function workbookSelected() {
      const workbookUrl = document.getElementById('workbookSelect').value;
      const meetingsContainer = document.getElementById('meetingsContainer');
      const meetingSelect = document.getElementById('meetingSelect');
      const fetchButton = document.getElementById('fetchMeetingButton');

      // Reset
      fetchButton.disabled = true;

      if (!workbookUrl) {
        meetingsContainer.style.display = 'none';
        return;
      }

      try {
        // Show loading state
        meetingSelect.innerHTML = '<option value="">Cargando reuniones...</option>';
        meetingsContainer.style.display = 'block';

        // Fetch weekly meetings for the selected workbook
        const meetings = await fetchWeeklyMeetingsForWorkbook(workbookUrl);

        if (!meetings || meetings.length === 0) {
          meetingSelect.innerHTML = '<option value="">No se encontraron reuniones</option>';
          return;
        }

        // Populate the select with meetings
        meetingSelect.innerHTML = '<option value="">Seleccione una semana</option>';
        meetings.forEach(meeting => {
          if (meeting.fullUrl) {
            const option = document.createElement('option');
            option.value = meeting.fullUrl;
            option.textContent = meeting.title;
            meetingSelect.appendChild(option);
          }
        });

        // Enable the fetch button
        meetingSelect.onchange = function() {
          fetchButton.disabled = !meetingSelect.value;
        };
      } catch (error) {
        console.error('Error loading meetings:', error);
        meetingSelect.innerHTML = '<option value="">Error al cargar reuniones</option>';
      }
    }

    // Fetch selected meeting
    async function fetchSelectedMeeting() {
      const meetingUrl = document.getElementById('meetingSelect').value;

      if (!meetingUrl) {
        showError('Por favor, seleccione una semana');
        return;
      }

      try {
        // Show loading message
        showLoading('Obteniendo datos de la reunión...');

        // Fetch meeting data from JW.org
        const meetingData = await fetchMeetingDataFromJW(meetingUrl);

        if (!meetingData) {
          throw new Error('No se pudo obtener datos de la reunión');
        }

        // Hide loading message
        hideLoading();

        // Close the modal
        closeFetchFromJWModal();

        // Open the add meeting modal with the fetched data
        openAddMeetingModalWithData(meetingData);
      } catch (error) {
        hideLoading();
        showError(`Error al obtener datos de la reunión: ${error.message}`);
      }
    }

    async function fetchDataFromJW() {
      const url = document.getElementById('jwOrgUrl').value.trim();

      if (!url) {
        showError('Por favor, ingrese una URL válida de JW.org');
        return;
      }

      if (!url.includes('jw.org')) {
        showError('La URL debe ser de jw.org');
        return;
      }

      try {
        // Show loading message
        showLoading('Obteniendo datos de JW.org...');

        // Fetch meeting data from JW.org
        const meetingData = await fetchMeetingDataFromJW(url);

        if (!meetingData) {
          throw new Error('No se pudo obtener datos de la reunión');
        }

        // Hide loading message
        hideLoading();

        // Close the modal
        closeFetchFromJWModal();

        // Open the add meeting modal with the fetched data
        openAddMeetingModalWithData(meetingData);
      } catch (error) {
        hideLoading();
        showError(`Error al obtener datos de JW.org: ${error.message}`);
      }
    }

    // Open add meeting modal with pre-populated data from JW.org
    function openAddMeetingModalWithData(meetingData) {
      // Reset the form first
      addMeetingForm.reset();

      // Set default date to next Wednesday
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      const daysUntilWednesday = (3 - dayOfWeek + 7) % 7; // Calculate days until next Wednesday (3)

      // If today is Wednesday, set to next Wednesday
      const daysToAdd = daysUntilWednesday === 0 ? 7 : daysUntilWednesday;

      const nextWednesday = new Date(today);
      nextWednesday.setDate(today.getDate() + daysToAdd);

      // Format date as YYYY-MM-DD
      const year = nextWednesday.getFullYear();
      const month = String(nextWednesday.getMonth() + 1).padStart(2, '0');
      const day = String(nextWednesday.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;

      document.getElementById('meetingDate').value = formattedDate;

      // Populate the form with the fetched data
      if (meetingData.theme) {
        document.getElementById('meetingTheme').value = meetingData.theme;
      }

      // Populate songs if available
      if (meetingData.sections && meetingData.sections.songs) {
        const openingSong = meetingData.sections.songs.find(song => song.displayOrder === 0);
        if (openingSong) {
          document.getElementById('openingSongNumber').value = openingSong.songNumber;
          document.getElementById('openingSongTitle').value = openingSong.title;
        }

        const middleSong = meetingData.sections.songs.find(song => song.displayOrder === 1);
        if (middleSong) {
          document.getElementById('middleSongNumber').value = middleSong.songNumber;
          document.getElementById('middleSongTitle').value = middleSong.title;
        }

        const closingSong = meetingData.sections.songs.find(song => song.displayOrder === 2);
        if (closingSong) {
          document.getElementById('closingSongNumber').value = closingSong.songNumber;
          document.getElementById('closingSongTitle').value = closingSong.title;
        }
      }

      // Populate treasures section if available
      if (meetingData.sections && meetingData.sections.treasures) {
        const treasuresParts = meetingData.sections.treasures;
        if (treasuresParts.length > 0) {
          document.getElementById('treasuresTalkTitle').value = treasuresParts[0].title;
        }
      }

      // Populate ministry section if available
      if (meetingData.sections && meetingData.sections.ministry) {
        const ministryParts = meetingData.sections.ministry;
        if (ministryParts.length > 0) {
          document.getElementById('firstMinistryPartTitle').value = ministryParts[0].title;
        }
        if (ministryParts.length > 1) {
          document.getElementById('secondMinistryPartTitle').value = ministryParts[1].title;
        }
        if (ministryParts.length > 2) {
          document.getElementById('ministryTalkTitle').value = ministryParts[2].title;
        }
      }

      // Populate Christian Life section if available
      if (meetingData.sections && meetingData.sections.christian_life) {
        const christianLifeParts = meetingData.sections.christian_life;
        if (christianLifeParts.length > 0) {
          document.getElementById('christianLifePart1Title').value = christianLifeParts[0].title;
        }
      }

      // Show the modal
      document.getElementById('addMeetingModal').style.display = 'flex';
    }

    // Populate member dropdowns
    function populateChairmenDropdown() {
      if (!members || members.length === 0) {
        console.warn('No members available to populate dropdowns');
        return;
      }

      // Filter members by role
      const elders = members.filter(member =>
        ['elder', 'overseer_coordinator'].includes(member.role)
      );

      const ministerialServants = members.filter(member =>
        ['ministerial_servant'].includes(member.role)
      );

      const allBrothers = members.filter(member =>
        ['elder', 'ministerial_servant', 'overseer_coordinator', 'publisher'].includes(member.role) &&
        member.gender === 'male'
      );

      const allMembers = members;

      // Populate chairman dropdown (elders and ministerial servants)
      populateDropdown('meetingChairman', [...elders, ...ministerialServants]);
      populateDropdown('editChairman', [...elders, ...ministerialServants]);

      // Populate prayer dropdowns
      populateDropdown('openingPrayer', allBrothers);
      populateDropdown('closingPrayer', allBrothers);

      // Populate treasures section dropdowns
      populateDropdown('treasuresTalkSpeaker', elders);
      populateDropdown('gemsPresenter', elders);
      populateDropdown('bibleReader', allBrothers);

      // Populate ministry section dropdowns
      populateDropdown('firstMinistryStudent', allMembers);
      populateDropdown('firstMinistryAssistant', allMembers);
      populateDropdown('secondMinistryStudent', allMembers);
      populateDropdown('secondMinistryAssistant', allMembers);
      populateDropdown('ministryTalkSpeaker', allBrothers);

      // Populate Christian Life section dropdowns
      populateDropdown('christianLifePartSpeaker', allBrothers);
      populateDropdown('congregationBibleStudy', elders);

      // Populate task dropdowns
      populateDropdown('audioVideo', allBrothers);
      populateDropdown('platform', allBrothers);
      populateDropdown('microphone1', allBrothers);
      populateDropdown('microphone2', allBrothers);

      // Populate edit form dropdowns
      populateDropdown('editTreasuresSpeaker', elders);
      populateDropdown('editGemsPresenter', elders);
      populateDropdown('editBibleReader', allBrothers);
      populateDropdown('editFirstMinistryStudent', allMembers);
      populateDropdown('editFirstMinistryAssistant', allMembers);
      populateDropdown('editSecondMinistryStudent', allMembers);
      populateDropdown('editSecondMinistryAssistant', allMembers);
      populateDropdown('editMinistrySpeaker', allBrothers);
      populateDropdown('editChristianLifePart1Speaker', allBrothers);
      populateDropdown('editCongregationBibleStudy', elders);
      populateDropdown('editClosingPrayer', allBrothers);
      populateDropdown('editAudioVideo', allBrothers);
      populateDropdown('editPlatform', allBrothers);
      populateDropdown('editMicrophone1', allBrothers);
      populateDropdown('editMicrophone2', allBrothers);
    }

    // Helper function to populate a dropdown
    function populateDropdown(elementId, membersList) {
      const dropdown = document.getElementById(elementId);

      if (!dropdown) {
        return;
      }

      // Clear existing options except the first one
      while (dropdown.options.length > 1) {
        dropdown.remove(1);
      }

      // Add options
      membersList.forEach(member => {
        const option = document.createElement('option');
        option.value = member.id;
        option.textContent = member.name;
        dropdown.appendChild(option);
      });
    }

    // Open add meeting modal
    function openAddMeetingModal() {
      addMeetingForm.reset();

      // Set default date to next Wednesday
      const today = new Date();
      const dayOfWeek = today.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
      const daysUntilWednesday = (3 - dayOfWeek + 7) % 7; // Calculate days until next Wednesday (3)

      // If today is Wednesday, set to next Wednesday
      const daysToAdd = daysUntilWednesday === 0 ? 7 : daysUntilWednesday;

      const nextWednesday = new Date(today);
      nextWednesday.setDate(today.getDate() + daysToAdd);

      // Format date as YYYY-MM-DD
      const year = nextWednesday.getFullYear();
      const month = String(nextWednesday.getMonth() + 1).padStart(2, '0');
      const day = String(nextWednesday.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;

      document.getElementById('meetingDate').value = formattedDate;

      // Show modal
      document.getElementById('addMeetingModal').style.display = 'flex';
    }

    // Close add meeting modal
    function closeAddMeetingModal() {
      document.getElementById('addMeetingModal').style.display = 'none';
    }

    // Open edit meeting modal
    async function openEditMeetingModal(meetingId) {
      console.log('Opening edit meeting modal for meeting ID:', meetingId);
      try {
        // Show loading message
        showLoading('Cargando datos de la reunión...');

        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        // Use the fetchMeeting function from midweek-meeting-api.js which already handles both endpoints
        console.log(`Fetching meeting data for meeting ${meetingId}`);
        const meeting = await fetchMeeting(meetingId);

        if (!meeting) {
          throw new Error('No se pudo obtener los datos de la reunión');
        }

        console.log('Successfully fetched meeting data for edit:', meeting);

        // Log important fields for debugging
        console.log('Meeting fields check for edit:');
        console.log('- meeting_date:', meeting.meeting_date);
        console.log('- chairman_id:', meeting.chairman_id);
        console.log('- prayer_beginning_id:', meeting.prayer_beginning_id);
        console.log('- opening_prayer_id:', meeting.opening_prayer_id);
        console.log('- prayer_end_id:', meeting.prayer_end_id);
        console.log('- closing_prayer_id:', meeting.closing_prayer_id);
        console.log('- songs:', meeting.songs);
        console.log('- opening_song_number:', meeting.opening_song_number);
        console.log('- parts:', meeting.parts);

        // Hide loading message
        hideLoading();

        if (meeting) {
          // Get the edit meeting content container
          const editMeetingContent = document.getElementById('editMeetingContent');

          // Set the meeting ID
          document.getElementById('editMeetingId').value = meeting.id;

          // Format date
          const date = new Date(meeting.meeting_date || meeting.date);
          const formattedDate = date.toLocaleDateString('es-ES', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });

          // Capitalize first letter
          const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

          // Format time
          const time = meeting.meeting_time || meeting.time || '19:30';
          const formattedTime = time.substring(0, 5);

          // Format date for input
          const inputDate = date.toISOString().split('T')[0];

          // Create HTML for the meeting edit form with dropdown selectors
          let html = `
            <div class="meeting-view">
              <div class="content">
                <div class="meeting-card">
                  <div class="meeting-header">
                    <div class="meeting-title">Vida y Ministerio Cristianos</div>
                    <div class="form-group">
                      <label class="form-label" for="editMeetingDate">Fecha</label>
                      <input type="date" class="form-input" id="editMeetingDate" name="date" value="${inputDate}" required>
                    </div>
                  </div>

                  <div class="meeting-info">
                    <i class="bi bi-clock"></i>
                    <span>
                      <input type="time" class="form-input" id="editMeetingTime" name="time" value="${time}" style="width: 100px;">
                    </span>
                  </div>

                  <div class="meeting-info">
                    <i class="bi bi-geo-alt"></i>
                    <span>
                      <select class="form-select" id="editMeetingLocation" name="meeting_location">
                        <option value="kingdom_hall" ${meeting.meeting_location === 'kingdom_hall' ? 'selected' : ''}>Salón del Reino</option>
                        <option value="zoom" ${meeting.meeting_location === 'zoom' ? 'selected' : ''}>Zoom</option>
                      </select>
                    </span>
                  </div>

                  <div class="meeting-info">
                    <i class="bi bi-person"></i>
                    <span>Presidente:
                      <select class="form-select" id="editChairman" name="chairman_id">
                        <option value="">Seleccionar presidente</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                    </span>
                  </div>

                  <div class="meeting-section-title">TESOROS DE LA BIBLIA</div>
                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Canción</div>
                      <div class="assignment-person">
                        <div class="song-display" id="openingSongDisplay" style="cursor: pointer; padding: 8px; border-radius: 4px; background-color: #f5f5f5; margin-bottom: 8px; display: block">
                          Canción <span id="openingSongNumberDisplay"></span><span id="openingSongTitleDisplay"></span>
                          <span class="edit-hint" style="font-size: 0.8em; color: #666;">(Haga clic para editar)</span>
                        </div>
                        <div class="song-edit" id="openingSongEdit" style="display: none; gap: 10px; align-items: center;">
                          <div style="flex: 0 0 80px;">
                            <label>Número:</label>
                            <input type="number" class="form-input" id="editOpeningSongNumber" name="opening_song_number" value="${meeting.opening_song_number || ''}" min="1" max="161" style="width: 60px;">
                          </div>
                          <div style="flex: 1;">
                            <label>Título:</label>
                            <input type="text" class="form-input" id="editOpeningSongTitle" name="opening_song_title" value="${meeting.opening_song_title || ''}">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Oración inicial</div>
                      <div class="assignment-person">
                        <select class="form-select" id="editOpeningPrayer" name="prayer_beginning_id">
                          <option value="">Seleccionar hermano</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                        <input type="hidden" id="editOpeningPrayerLegacy" name="opening_prayer_id">
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">${meeting.treasures_talk_title || 'Tesoros de la Biblia'}</div>
                      <div class="assignment-person">
                        <select class="form-select" id="editTreasuresSpeaker" name="treasures_speaker_id">
                          <option value="">Seleccionar orador</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Busquemos perlas escondidas</div>
                      <div class="assignment-person">
                        <select class="form-select" id="editGemsPresenter" name="gems_presenter_id">
                          <option value="">Seleccionar presentador</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">${meeting.bible_reading || 'Lectura de la Biblia'}</div>
                      <div class="assignment-person">
                        <select class="form-select" id="editBibleReader" name="bible_reading_id">
                          <option value="">Seleccionar lector</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="meeting-section-title">SEAMOS MEJORES MAESTROS</div>
                  <!-- Container for dynamically generated ministry parts -->
                  <div id="editMinistryPartsContainer">
                    <!-- Will be populated dynamically by JavaScript -->
                  </div>

                  <div class="meeting-section-title">NUESTRA VIDA CRISTIANA</div>
                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Canción</div>
                      <div class="assignment-person">
                        <div class="song-display" id="middleSongDisplay" style="cursor: pointer; padding: 8px; border-radius: 4px; background-color: #f5f5f5; margin-bottom: 8px; display: block">
                          Canción <span id="middleSongNumberDisplay"></span><span id="middleSongTitleDisplay"></span>
                        </div>
                        <div class="song-edit" id="middleSongEdit" style="display: none; gap: 10px; align-items: center;">
                          <div style="flex: 0 0 80px;">
                            <label>Número:</label>
                            <input type="number" class="form-input" id="editMiddleSongNumber" name="middle_song_number" value="${meeting.middle_song_number || ''}" min="1" max="161" style="width: 60px;">
                          </div>
                          <div style="flex: 1;">
                            <label>Título:</label>
                            <input type="text" class="form-input" id="editMiddleSongTitle" name="middle_song_title" value="${meeting.middle_song_title || ''}">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Container for dynamically generated Christian Life parts -->
                  <div id="editChristianLifePartsContainer">
                    <!-- Will be populated dynamically by JavaScript -->
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Canción</div>
                      <div class="assignment-person">
                        <div class="song-display" id="closingSongDisplay" style="cursor: pointer; padding: 8px; border-radius: 4px; background-color: #f5f5f5; margin-bottom: 8px; display: block">
                          Canción <span id="closingSongNumberDisplay"></span><span id="closingSongTitleDisplay"></span>
                        </div>
                        <div class="song-edit" id="closingSongEdit" style="display: none; gap: 10px; align-items: center;">
                          <div style="flex: 0 0 80px;">
                            <label>Número:</label>
                            <input type="number" class="form-input" id="editClosingSongNumber" name="closing_song_number" value="${meeting.closing_song_number || ''}" min="1" max="161" style="width: 60px;">
                          </div>
                          <div style="flex: 1;">
                            <label>Título:</label>
                            <input type="text" class="form-input" id="editClosingSongTitle" name="closing_song_title" value="${meeting.closing_song_title || ''}">
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Oración final</div>
                      <div class="assignment-person">
                        <select class="form-select" id="editClosingPrayer" name="prayer_end_id">
                          <option value="">Seleccionar hermano</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                        <input type="hidden" id="editClosingPrayerLegacy" name="closing_prayer_id">
                      </div>
                    </div>
                  </div>

                  <div class="tasks-title">TAREAS</div>
                  <div class="tasks-section">
                    <div class="task-item">
                      <div class="task-name">Operador de audio</div>
                      <div class="task-assignee">
                        <select class="form-select" id="editAudioVideo" name="audio_video_id">
                          <option value="">Seleccionar hermano</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                    <div class="task-item">
                      <div class="task-name">Plataforma</div>
                      <div class="task-assignee">
                        <select class="form-select" id="editPlatform" name="platform_id">
                          <option value="">Seleccionar hermano</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                    <div class="task-item">
                      <div class="task-name">Micrófonos</div>
                      <div class="task-assignee" style="display: flex; gap: 10px;">
                        <select class="form-select" id="editMicrophone1" name="microphone1_id" style="flex: 1;">
                          <option value="">Hermano 1</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                        <select class="form-select" id="editMicrophone2" name="microphone2_id" style="flex: 1;">
                          <option value="">Hermano 2</option>
                          <!-- Will be populated by JavaScript -->
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="cleaning-title">LIMPIEZA Y MANTENIMIENTO DEL SALÓN</div>
                  <div class="cleaning-section">
                    <div class="cleaning-item">
                      <div class="cleaning-area">Limpieza del Salon</div>
                      <div class="cleaning-group">
                        <select class="form-select" id="editCleaningGroup" name="cleaning_group">
                          <option value="">Seleccionar grupo</option>
                          <option value="1" ${meeting.cleaning_group === '1' ? 'selected' : ''}>Grupo 1</option>
                          <option value="2" ${meeting.cleaning_group === '2' ? 'selected' : ''}>Grupo 2</option>
                          <option value="3" ${meeting.cleaning_group === '3' ? 'selected' : ''}>Grupo 3</option>
                          <option value="4" ${meeting.cleaning_group === '4' ? 'selected' : ''}>Grupo 4</option>
                          <option value="5" ${meeting.cleaning_group === '5' ? 'selected' : ''}>Grupo 5</option>
                          <option value="6" ${meeting.cleaning_group === '6' ? 'selected' : ''}>Grupo 6</option>
                          <option value="7" ${meeting.cleaning_group === '7' ? 'selected' : ''}>Grupo 7</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Add Zoom section if applicable
          if (meeting.meeting_location === 'zoom' || meeting.zoom_id || meeting.zoom_password || meeting.zoom_link) {
            html += `
              <div class="zoom-title">ZOOM</div>
              <div class="zoom-section">
                <div class="zoom-info">
                  <strong>ID de reunión:</strong>
                  <input type="text" class="form-input" id="editZoomId" name="zoom_id" value="${meeting.zoom_id || ''}">
                </div>
                <div class="zoom-info">
                  <strong>Código de acceso:</strong>
                  <input type="text" class="form-input" id="editZoomPassword" name="zoom_password" value="${meeting.zoom_password || ''}">
                </div>
                <div class="zoom-info">
                  <strong>Enlace:</strong>
                  <input type="url" class="form-input" id="editZoomLink" name="zoom_link" value="${meeting.zoom_link || ''}">
                </div>
              </div>
            `;
          }

          // Set the HTML content
          editMeetingContent.innerHTML = html;

          // Use a more reliable approach with a proper delay to ensure DOM elements are ready
          setTimeout(() => {
            try {
              console.log('Populating song fields after delay');
              // Populate song fields first
              populateSongFields(meeting);

              // Generate dynamic parts for ministry and Christian life sections
              generateEditMinistryParts(meeting);
              generateEditChristianLifeParts(meeting);

              // Then populate all member dropdowns
              populateEditMemberDropdowns(meeting);

              // Add event listeners for song display/edit toggling
              setupSongToggleListeners();

              console.log('All fields populated successfully');
            } catch (error) {
              console.error('Error populating form fields:', error);
            }
          }, 200); // 200ms delay should be sufficient

          // Set selected values for all dropdowns based on meeting data
          console.log('Setting selected values for dropdowns based on meeting data:', meeting);

          // Helper function to set dropdown value
          function setDropdownValue(id, value) {
            const dropdown = document.getElementById(id);
            if (dropdown && value) {
              console.log(`Setting ${id} to value ${value}`);
              dropdown.value = value;

              // Trigger a change event to ensure any event listeners are notified
              const event = new Event('change', { bubbles: true });
              dropdown.dispatchEvent(event);
            }
          }

          // Wait a short time to ensure dropdowns are fully populated
          setTimeout(() => {
            try {
              // Set values for all dropdowns
              setDropdownValue('editChairman', meeting.chairman_id);
              setDropdownValue('editOpeningPrayer', meeting.prayer_beginning_id);
              setDropdownValue('editTreasuresSpeaker', meeting.treasures_speaker_id);
              setDropdownValue('editGemsPresenter', meeting.gems_presenter_id);
              setDropdownValue('editBibleReader', meeting.bible_reading_id);
              setDropdownValue('editFirstMinistryStudent', meeting.first_ministry_student_id);
              setDropdownValue('editFirstMinistryAssistant', meeting.first_ministry_assistant_id);
              setDropdownValue('editSecondMinistryStudent', meeting.second_ministry_student_id);
              setDropdownValue('editSecondMinistryAssistant', meeting.second_ministry_assistant_id);
              setDropdownValue('editMinistrySpeaker', meeting.ministry_speaker_id);
              setDropdownValue('editChristianLifePartSpeaker', meeting.christian_life_part_speaker_id);
              setDropdownValue('editCongregationBibleStudy', meeting.congregation_bible_study_id);
              setDropdownValue('editClosingPrayer', meeting.prayer_end_id);

              // Set values for task and cleaning fields
              console.log('Setting task and cleaning fields:');
              console.log('audio_video_id:', meeting.audio_video_id);
              console.log('platform_id:', meeting.platform_id);
              console.log('microphone1_id:', meeting.microphone1_id);
              console.log('microphone2_id:', meeting.microphone2_id);
              console.log('cleaning_group:', meeting.cleaning_group);

              setDropdownValue('editAudioVideo', meeting.audio_video_id);
              setDropdownValue('editPlatform', meeting.platform_id);
              setDropdownValue('editMicrophone1', meeting.microphone1_id);
              setDropdownValue('editMicrophone2', meeting.microphone2_id);

              // Set cleaning group dropdown
              const cleaningGroupDropdown = document.getElementById('editCleaningGroup');
              if (cleaningGroupDropdown && meeting.cleaning_group) {
                console.log(`Setting editCleaningGroup to value ${meeting.cleaning_group}`);
                cleaningGroupDropdown.value = meeting.cleaning_group;

                // Trigger a change event to ensure any event listeners are notified
                const event = new Event('change', { bubbles: true });
                cleaningGroupDropdown.dispatchEvent(event);
              }

              console.log('All dropdown values set');

              // Initialize song displays one final time to ensure everything is in sync
              updateSongDisplays();
            } catch (error) {
              console.error('Error setting dropdown values:', error);
            }
          }, 500);

          // Show the modal
          document.getElementById('editMeetingModal').style.display = 'flex';
        }

        // Initialize song displays
        function initializeSongDisplays() {
          // Use the updateSongDisplays function to ensure consistent display
          console.log('Initializing song displays');
          updateSongDisplays();
        }

        // Setup song toggle listeners
        function setupSongToggleListeners() {
          // Opening song
          const openingSongDisplay = document.getElementById('openingSongDisplay');
          const openingSongEdit = document.getElementById('openingSongEdit');
          const editOpeningSongNumber = document.getElementById('editOpeningSongNumber');
          const editOpeningSongTitle = document.getElementById('editOpeningSongTitle');

          if (openingSongDisplay) {
            openingSongDisplay.addEventListener('click', function() {
              openingSongDisplay.style.display = 'none';
              openingSongEdit.style.display = 'flex';
            });
          }

          if (editOpeningSongNumber && editOpeningSongTitle) {
            // Update display when inputs change
            const updateOpeningSongDisplay = function() {
              const number = editOpeningSongNumber.value;
              const title = editOpeningSongTitle.value;
              if (number) {
                // Update the span elements
                const numberDisplay = document.getElementById('openingSongNumberDisplay');
                const titleDisplay = document.getElementById('openingSongTitleDisplay');
                if (numberDisplay) numberDisplay.textContent = number;
                if (titleDisplay) titleDisplay.textContent = title ? ' - ' + title : '';

                openingSongDisplay.style.display = 'block';
                openingSongEdit.style.display = 'none';
              }
            };

            editOpeningSongNumber.addEventListener('change', updateOpeningSongDisplay);
            editOpeningSongTitle.addEventListener('change', updateOpeningSongDisplay);
            editOpeningSongNumber.addEventListener('blur', updateOpeningSongDisplay);
            editOpeningSongTitle.addEventListener('blur', updateOpeningSongDisplay);
          }

          // Middle song
          const middleSongDisplay = document.getElementById('middleSongDisplay');
          const middleSongEdit = document.getElementById('middleSongEdit');
          const editMiddleSongNumber = document.getElementById('editMiddleSongNumber');
          const editMiddleSongTitle = document.getElementById('editMiddleSongTitle');

          if (middleSongDisplay) {
            middleSongDisplay.addEventListener('click', function() {
              middleSongDisplay.style.display = 'none';
              middleSongEdit.style.display = 'flex';
            });
          }

          if (editMiddleSongNumber && editMiddleSongTitle) {
            // Update display when inputs change
            const updateMiddleSongDisplay = function() {
              const number = editMiddleSongNumber.value;
              const title = editMiddleSongTitle.value;
              if (number) {
                // Update the span elements
                const numberDisplay = document.getElementById('middleSongNumberDisplay');
                const titleDisplay = document.getElementById('middleSongTitleDisplay');
                if (numberDisplay) numberDisplay.textContent = number;
                if (titleDisplay) titleDisplay.textContent = title ? ' - ' + title : '';

                middleSongDisplay.style.display = 'block';
                middleSongEdit.style.display = 'none';
              }
            };

            editMiddleSongNumber.addEventListener('change', updateMiddleSongDisplay);
            editMiddleSongTitle.addEventListener('change', updateMiddleSongDisplay);
            editMiddleSongNumber.addEventListener('blur', updateMiddleSongDisplay);
            editMiddleSongTitle.addEventListener('blur', updateMiddleSongDisplay);
          }

          // Closing song
          const closingSongDisplay = document.getElementById('closingSongDisplay');
          const closingSongEdit = document.getElementById('closingSongEdit');
          const editClosingSongNumber = document.getElementById('editClosingSongNumber');
          const editClosingSongTitle = document.getElementById('editClosingSongTitle');

          if (closingSongDisplay) {
            closingSongDisplay.addEventListener('click', function() {
              closingSongDisplay.style.display = 'none';
              closingSongEdit.style.display = 'flex';
            });
          }

          if (editClosingSongNumber && editClosingSongTitle) {
            // Update display when inputs change
            const updateClosingSongDisplay = function() {
              const number = editClosingSongNumber.value;
              const title = editClosingSongTitle.value;
              if (number) {
                // Update the span elements
                const numberDisplay = document.getElementById('closingSongNumberDisplay');
                const titleDisplay = document.getElementById('closingSongTitleDisplay');
                if (numberDisplay) numberDisplay.textContent = number;
                if (titleDisplay) titleDisplay.textContent = title ? ' - ' + title : '';

                closingSongDisplay.style.display = 'block';
                closingSongEdit.style.display = 'none';
              }
            };

            editClosingSongNumber.addEventListener('change', updateClosingSongDisplay);
            editClosingSongTitle.addEventListener('change', updateClosingSongDisplay);
            editClosingSongNumber.addEventListener('blur', updateClosingSongDisplay);
            editClosingSongTitle.addEventListener('blur', updateClosingSongDisplay);
          }

          // Initialize song displays with current values
          updateSongDisplays();
        }
      } catch (error) {
        console.error('Error opening edit meeting modal:', error);
        hideLoading();
        showError(`Error al cargar los datos de la reunión: ${error.message}`);
      }
    }

    // Close edit meeting modal
    function closeEditMeetingModal() {
      document.getElementById('editMeetingModal').style.display = 'none';
    }

    // Open view meeting modal
    async function openViewMeetingModal(meetingId) {
      console.log('Opening view meeting modal for meeting ID:', meetingId);
      try {
        // Show loading message
        showLoading('Cargando datos de la reunión...');

        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        // Use the fetchMeeting function from midweek-meeting-api.js which already handles both endpoints
        console.log(`Fetching meeting data for meeting ${meetingId}`);
        const meeting = await fetchMeeting(meetingId);

        if (!meeting) {
          throw new Error('No se pudo obtener los datos de la reunión');
        }

        console.log('Successfully fetched meeting data:', meeting);

        // Log important fields for debugging
        console.log('Meeting fields check:');
        console.log('- meeting_date:', meeting.meeting_date);
        console.log('- chairman_id:', meeting.chairman_id);
        console.log('- prayer_beginning_id:', meeting.prayer_beginning_id);
        console.log('- opening_prayer_id:', meeting.opening_prayer_id);
        console.log('- prayer_end_id:', meeting.prayer_end_id);
        console.log('- closing_prayer_id:', meeting.closing_prayer_id);
        console.log('- songs:', meeting.songs);
        console.log('- opening_song_number:', meeting.opening_song_number);
        console.log('- parts:', meeting.parts);

        // Hide loading message
        hideLoading();

        if (meeting) {
          // Get the view meeting content container
          const viewMeetingContent = document.getElementById('viewMeetingContent');

          // Format date
          const date = new Date(meeting.meeting_date || meeting.date);
          const formattedDate = date.toLocaleDateString('es-ES', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          });

          // Capitalize first letter
          const capitalizedDate = formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);

          // Format time
          const time = meeting.meeting_time || meeting.time || '19:30';
          const formattedTime = time.substring(0, 5);

          // Get member names
          const getMemberName = (memberId) => {
            if (!memberId) return '';
            const member = members.find(m => m.id === parseInt(memberId));
            return member ? member.name : 'No asignado';
          };

          // Function to generate parts HTML for a section
          function generatePartsHTML(sectionParts, startIndex = 1) {
            if (!sectionParts || !Array.isArray(sectionParts) || sectionParts.length === 0) {
              return '';
            }

            let html = '';
            let index = startIndex;

            for (const part of sectionParts) {
              // Skip parts that are actually songs (they'll be handled separately)
              if (part.title && part.title.toLowerCase().includes('canción')) {
                continue;
              }

              // Skip section headers - they are displayed separately
              if (part.title === 'Tesoros de la Biblia' ||
                  part.title === 'Seamos Mejores Maestros' ||
                  part.title === 'Nuestra Vida Cristiana') {
                continue;
              }

              // Use JW.org part number if available, otherwise use our index
              const partNumber = part.jw_number ? part.jw_number : index;

              // Clean up the title to remove any numbering that might be included
              let cleanTitle = part.title || '';
              // Remove any leading numbers like "1. " from the title if we're using jw_number
              if (part.jw_number && cleanTitle.match(/^\d+\.\s+/)) {
                cleanTitle = cleanTitle.replace(/^\d+\.\s+/, '');
              }

              html += `
                <div class="assignment-item">
                  <div class="assignment-details">
                    <div class="assignment-title">${partNumber}. ${cleanTitle}</div>
                    <div class="assignment-person">${getMemberName(part.assignee_id)}</div>
                  </div>
                </div>
              `;
              index++;
            }

            return html;
          }

          // Get parts from meeting data
          const treasuresParts = meeting.parts && meeting.parts.treasures ? meeting.parts.treasures : [];
          const ministryParts = meeting.parts && meeting.parts.ministry ? meeting.parts.ministry : [];
          const christianLifeParts = meeting.parts && meeting.parts.christianLife ? meeting.parts.christianLife : [];

          console.log('Meeting parts data:');
          console.log('Meeting ID:', meeting.id);
          console.log('Treasures parts:', JSON.stringify(treasuresParts));
          console.log('Ministry parts:', JSON.stringify(ministryParts));
          console.log('Christian Life parts:', JSON.stringify(christianLifeParts));

          // Log detailed parts data for debugging
          console.log('Detailed parts data:');
          if (treasuresParts.length > 0) {
            console.log('Sample treasures part:', treasuresParts[0]);
            console.log('Treasures part assignee_id:', treasuresParts[0].assignee_id);
            console.log('Treasures part structure:', Object.keys(treasuresParts[0]));
          }
          if (ministryParts.length > 0) {
            console.log('Sample ministry part:', ministryParts[0]);
            console.log('Ministry part assignee_id:', ministryParts[0].assignee_id);
            console.log('Ministry part structure:', Object.keys(ministryParts[0]));
          }
          if (christianLifeParts.length > 0) {
            console.log('Sample Christian Life part:', christianLifeParts[0]);
            console.log('Christian Life part assignee_id:', christianLifeParts[0].assignee_id);
            console.log('Christian Life part structure:', Object.keys(christianLifeParts[0]));
          }

          // Log task and cleaning data for debugging
          console.log('Task and cleaning data:');
          console.log('audio_video_id:', meeting.audio_video_id);
          console.log('platform_id:', meeting.platform_id);
          console.log('microphone1_id:', meeting.microphone1_id);
          console.log('microphone2_id:', meeting.microphone2_id);
          console.log('cleaning_group:', meeting.cleaning_group);

          // Calculate the correct starting index for each section
          // We need to count the actual parts (not section headers) in the previous sections
          const treasuresActualParts = treasuresParts.filter(p => !p.is_section_header && !p.title.toLowerCase().includes('canción')).length;

          // Get songs from meeting data
          const songs = meeting.songs || [];
          // Find songs by display_order or section
          const openingSong = songs.find(s => s.display_order === 0 || s.section === 'opening');
          const middleSong = songs.find(s => s.display_order === 1 || s.section === 'middle');
          const closingSong = songs.find(s => s.display_order === 2 || s.section === 'closing');

          // Create HTML for the meeting view to match frontend format
          let html = `
            <div class="meeting-view">
              <div class="content">
                <div class="meeting-card">
                  <div class="meeting-header">
                    <div class="meeting-title">${meeting.theme || 'Vida y Ministerio Cristianos'}</div>
                    <div class="meeting-date">${capitalizedDate}</div>
                  </div>

                  <div class="meeting-info">
                    <i class="bi bi-clock"></i>
                    <span>${formattedTime}</span>
                  </div>

                  <div class="meeting-info">
                    <i class="bi bi-geo-alt"></i>
                    <span>${meeting.meeting_location || 'Salón del Reino'}</span>
                  </div>

                  <div class="meeting-info">
                    <i class="bi bi-person"></i>
                    <span>Presidente: ${getMemberName(meeting.chairman_id)}</span>
                  </div>

                  <div class="meeting-section-title">TESOROS DE LA BIBLIA</div>
                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Canción</div>
                      <div class="assignment-person">
                        ${meeting.opening_song_number ? `Canción ${meeting.opening_song_number} - ${meeting.opening_song_title || ''}` :
                          (meeting.opening_song_formatted ||
                           (openingSong ?
                             (openingSong.full_text ||
                              (openingSong.number ?
                                `Canción ${openingSong.number} - ${openingSong.title}` :
                                openingSong.title)
                             ) :
                             'No asignada')
                          )
                        }
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Presidente</div>
                      <div class="assignment-person">${getMemberName(meeting.chairman_id)}</div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Oración inicial</div>
                      <div class="assignment-person">${getMemberName(meeting.prayer_beginning_id || meeting.opening_prayer_id)}</div>
                    </div>
                  </div>

                  ${generatePartsHTML(treasuresParts, 1)}

                  <div class="meeting-section-title">SEAMOS MEJORES MAESTROS</div>
                  ${generatePartsHTML(ministryParts, treasuresActualParts + 1)}

                  <div class="meeting-section-title">NUESTRA VIDA CRISTIANA</div>
                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Canción</div>
                      <div class="assignment-person">
                        ${meeting.middle_song_number ? `Canción ${meeting.middle_song_number} - ${meeting.middle_song_title || ''}` :
                          (meeting.middle_song_formatted ||
                           (middleSong ?
                             (middleSong.full_text ||
                              (middleSong.number ?
                                `Canción ${middleSong.number} - ${middleSong.title}` :
                                middleSong.title)
                             ) :
                             'No asignada')
                          )
                        }
                      </div>
                    </div>
                  </div>

                  ${generatePartsHTML(christianLifeParts, treasuresActualParts + ministryParts.filter(p => !p.is_section_header && !p.title.toLowerCase().includes('canción')).length + 1)}

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Canción</div>
                      <div class="assignment-person">
                        ${meeting.closing_song_number ? `Canción ${meeting.closing_song_number} - ${meeting.closing_song_title || ''}` :
                          (meeting.closing_song_formatted ||
                           (closingSong ?
                             (closingSong.full_text ||
                              (closingSong.number ?
                                `Canción ${closingSong.number} - ${closingSong.title}` :
                                closingSong.title)
                             ) :
                             'No asignada')
                          )
                        }
                      </div>
                    </div>
                  </div>

                  <div class="assignment-item">
                    <div class="assignment-details">
                      <div class="assignment-title">Oración final</div>
                      <div class="assignment-person">${getMemberName(meeting.prayer_end_id || meeting.closing_prayer_id)}</div>
                    </div>
                  </div>

                  <div class="tasks-title">TAREAS</div>
                  <div class="tasks-section">
                    <div class="task-item">
                      <div class="task-name">Operador de audio</div>
                      <div class="task-assignee">${getMemberName(meeting.audio_video_id) || 'No asignado'}</div>
                    </div>
                    <div class="task-item">
                      <div class="task-name">Plataforma</div>
                      <div class="task-assignee">${getMemberName(meeting.platform_id) || 'No asignado'}</div>
                    </div>
                    <div class="task-item">
                      <div class="task-name">Micrófonos</div>
                      <div class="task-assignee">
                        ${meeting.microphone1_id ? getMemberName(meeting.microphone1_id) : 'No asignado'}
                        ${meeting.microphone2_id ? ', ' + getMemberName(meeting.microphone2_id) : ''}
                      </div>
                    </div>
                  </div>

                  <div class="cleaning-title">LIMPIEZA Y MANTENIMIENTO DEL SALÓN</div>
                  <div class="cleaning-section">
                    <div class="cleaning-item">
                      <div class="cleaning-area">Limpieza del Salon</div>
                      <div class="cleaning-group">
                        ${meeting.cleaning_group ? `Grupo ${meeting.cleaning_group}` : 'No asignado'}
                      </div>
                    </div>
                  </div>

                  ${meeting.zoom_id ? `
                  <div class="zoom-title">ZOOM</div>
                  <div class="zoom-section">
                    <div class="zoom-info">
                      <strong>ID de reunión:</strong> ${meeting.zoom_id}
                    </div>
                    <div class="zoom-info">
                      <strong>Código de acceso:</strong> ${meeting.zoom_password || ''}
                    </div>
                    <a href="https://us02web.zoom.us/j/${meeting.zoom_id}" class="zoom-link">
                      https://us02web.zoom.us/j/${meeting.zoom_id} ${meeting.zoom_password ? `(Passcode: ${meeting.zoom_password})` : ''}
                    </a>
                  </div>
                  ` : ''}
                </div>
              </div>
            </div>
          `;

          // Set the HTML content
          viewMeetingContent.innerHTML = html;

          // Show the modal
          document.getElementById('viewMeetingModal').style.display = 'flex';
        }
      } catch (error) {
        console.error('Error opening view meeting modal:', error);
        hideLoading();
        showError(`Error al cargar los datos de la reunión: ${error.message}`);
      }
    }

    // Close view meeting modal
    function closeViewMeetingModal() {
      document.getElementById('viewMeetingModal').style.display = 'none';
    }

    // Handle add meeting form submission
    async function handleAddMeeting(event) {
      event.preventDefault();

      // Get form data from the preview container
      const formData = new FormData(addMeetingForm);

      try {
        // Get song numbers and titles from the preview container
        // Use safer selectors with more specific paths
        const songElements = document.querySelectorAll('.assignment-item');
        let openingSongText = '';
        let middleSongText = '';
        let closingSongText = '';

        // Find song elements by their position and content
        for (const element of songElements) {
          const titleElement = element.querySelector('.assignment-title');
          if (titleElement && titleElement.textContent.trim() === 'Canción') {
            const personElement = element.querySelector('.assignment-person');
            if (personElement) {
              const prevElement = element.previousElementSibling;
              if (prevElement && prevElement.classList.contains('meeting-section-title')) {
                const sectionTitle = prevElement.textContent.trim();
                if (sectionTitle === 'TESOROS DE LA BIBLIA') {
                  openingSongText = personElement.textContent.trim();
                } else if (sectionTitle === 'NUESTRA VIDA CRISTIANA') {
                  middleSongText = personElement.textContent.trim();
                }
              } else if (!closingSongText && personElement.textContent.includes('Danos fuerzas')) {
                closingSongText = personElement.textContent.trim();
              }
            }
          }
        }

        console.log('Songs found:', { openingSongText, middleSongText, closingSongText });

        // Extract song numbers and titles
        const openingSongMatch = openingSongText ? openingSongText.match(/([0-9]+)\s*-\s*(.+)/) : null;
        const middleSongMatch = middleSongText ? middleSongText.match(/([0-9]+)\s*-\s*(.+)/) : null;
        const closingSongMatch = closingSongText ? closingSongText.match(/([0-9]+)\s*-\s*(.+)/) : null;

        // Get part titles from the preview container using more reliable selectors
        const treasuresTalkElement = document.querySelector('#treasuresTalkSpeaker');
        const bibleReadingElement = document.querySelector('#bibleReader');
        const firstMinistryElement = document.querySelector('#firstMinistryStudent');
        const secondMinistryElement = document.querySelector('#secondMinistryStudent');
        const ministryTalkElement = document.querySelector('#ministryTalkSpeaker');
        const christianLifeElement = document.querySelector('#christianLifePartSpeaker');

        // Get the titles from the closest parent's title element
        const treasuresTalkTitle = treasuresTalkElement ?
          treasuresTalkElement.closest('.assignment-item').querySelector('.assignment-title').textContent.trim() : '';
        const bibleReadingTitle = bibleReadingElement ?
          bibleReadingElement.closest('.assignment-item').querySelector('.assignment-title').textContent.trim() : '';
        const firstMinistryPartTitle = firstMinistryElement ?
          firstMinistryElement.closest('.assignment-item').querySelector('.assignment-title').textContent.trim() : '';
        const secondMinistryPartTitle = secondMinistryElement ?
          secondMinistryElement.closest('.assignment-item').querySelector('.assignment-title').textContent.trim() : '';
        const ministryTalkTitle = ministryTalkElement ?
          ministryTalkElement.closest('.assignment-item').querySelector('.assignment-title').textContent.trim() : '';
        const christianLifePartTitle = christianLifeElement ?
          christianLifeElement.closest('.assignment-item').querySelector('.assignment-title').textContent.trim() : '';

        // Create new meeting object
        const newMeeting = {
        meeting_date: formData.get('date'),
        meeting_time: formData.get('time'),
        meeting_location: formData.get('location'),
        chairman_id: document.getElementById('meetingChairman').value || null,
        opening_prayer_id: document.getElementById('openingPrayer').value || null,
        closing_prayer_id: document.getElementById('closingPrayer').value || null,
        bible_reading: bibleReadingTitle || '',
        bible_reader_id: document.getElementById('bibleReader').value || null,

        // Songs
        opening_song_number: openingSongMatch ? openingSongMatch[1] : '',
        opening_song_title: openingSongMatch ? openingSongMatch[2] : '',
        middle_song_number: middleSongMatch ? middleSongMatch[1] : '',
        middle_song_title: middleSongMatch ? middleSongMatch[2] : '',
        closing_song_number: closingSongMatch ? closingSongMatch[1] : '',
        closing_song_title: closingSongMatch ? closingSongMatch[2] : '',

        // Treasures section
        treasures_talk_title: treasuresTalkTitle || '',
        treasures_talk_speaker_id: document.getElementById('treasuresTalkSpeaker').value || null,
        gems_presenter_id: document.getElementById('gemsPresenter').value || null,

        // Ministry section
        first_ministry_part_title: firstMinistryPartTitle || '',
        first_ministry_student_id: document.getElementById('firstMinistryStudent').value || null,
        first_ministry_assistant_id: document.getElementById('firstMinistryAssistant').value || null,
        second_ministry_part_title: secondMinistryPartTitle || '',
        second_ministry_student_id: document.getElementById('secondMinistryStudent').value || null,
        second_ministry_assistant_id: document.getElementById('secondMinistryAssistant').value || null,
        ministry_talk_title: ministryTalkTitle || '',
        ministry_talk_speaker_id: document.getElementById('ministryTalkSpeaker').value || null,

        // Christian Life section
        christian_life_part_title: christianLifePartTitle || '',
        christian_life_part_speaker_id: document.getElementById('christianLifePartSpeaker').value || null,
        congregation_bible_study_id: document.getElementById('congregationBibleStudy').value || null,

        // Tasks
        audio_video_id: document.getElementById('audioVideo').value || null,
        platform_id: document.getElementById('platform').value || null,
        microphone1_id: document.getElementById('microphone1').value || null,
        microphone2_id: document.getElementById('microphone2').value || null,
        cleaning_group: document.getElementById('cleaningGroup').value || ''
      };

        // Add zoom info
        newMeeting.zoom_id = document.getElementById('meetingZoomId').value || '';
        newMeeting.zoom_password = document.getElementById('meetingZoomPassword').value || '';
        newMeeting.zoom_link = document.getElementById('meetingZoomLink').value || '';

        try {
          // Show loading message
          showLoading('Guardando reunión...');

          // Create meeting via API
          const result = await createMeeting(newMeeting);

          // Hide loading message
          hideLoading();

          if (result) {
            // Close modal
            closeAddMeetingModal();

            // Refresh meetings list
            meetings = await fetchMeetings();
            renderMeetings();

            // Show success message
            showSuccess('Reunión creada exitosamente');
          }
        } catch (error) {
          console.error('Error creating meeting:', error);
          hideLoading();
          showError(`Error al crear la reunión: ${error.message}`);
        }
      } catch (error) {
        console.error('Error preparing meeting data:', error);
        hideLoading();
        showError(`Error al preparar los datos de la reunión: ${error.message}`);
      }
    }

    // Handle edit meeting form submission
    async function handleEditMeeting(event) {
      event.preventDefault();
      console.log('Handling edit meeting form submission');

      const formData = new FormData(editMeetingForm);
      const meetingId = parseInt(formData.get('id'));
      console.log('Meeting ID:', meetingId);

      // Get all form fields from the edit meeting form
      // This will include all the fields from our new modal
      const formEntries = Array.from(formData.entries());
      console.log('Form entries:', formEntries);

      // Log the form elements for debugging
      console.log('Form elements:');
      const formElements = editMeetingForm.elements;
      for (let i = 0; i < formElements.length; i++) {
        const element = formElements[i];
        if (element.name) {
          console.log(`Element name: ${element.name}, value: ${element.value}, type: ${element.type}`);
        }
      }

      // Check if the chairman_id field exists in the form
      const chairmanField = document.getElementById('editChairman');
      console.log('Chairman field:', chairmanField);
      if (chairmanField) {
        console.log('Chairman field name:', chairmanField.name);
        console.log('Chairman field value:', chairmanField.value);
      }

      // Check task and cleaning fields
      const audioVideoField = document.getElementById('editAudioVideo');
      const platformField = document.getElementById('editPlatform');
      const microphone1Field = document.getElementById('editMicrophone1');
      const microphone2Field = document.getElementById('editMicrophone2');
      const cleaningGroupField = document.getElementById('editCleaningGroup');

      console.log('Audio/Video field:', audioVideoField ? audioVideoField.value : 'not found');
      console.log('Platform field:', platformField ? platformField.value : 'not found');
      console.log('Microphone1 field:', microphone1Field ? microphone1Field.value : 'not found');
      console.log('Microphone2 field:', microphone2Field ? microphone2Field.value : 'not found');
      console.log('Cleaning Group field:', cleaningGroupField ? cleaningGroupField.value : 'not found');

      // Create updated meeting object with correct field mappings
      // Only include fields that exist in the database
      const updatedMeeting = {
        // Basic meeting info
        meeting_date: formData.get('date'),
        meeting_time: formData.get('time'),
        meeting_location: formData.get('meeting_location'),

        // Opening section
        chairman_id: document.getElementById('editChairman').value || null,

        // Prayer assignments - Use both field name formats for compatibility
        prayer_beginning_id: document.getElementById('editOpeningPrayer').value || null,
        opening_prayer_id: document.getElementById('editOpeningPrayer').value || null,
        prayer_end_id: document.getElementById('editClosingPrayer').value || null,
        closing_prayer_id: document.getElementById('editClosingPrayer').value || null,

        // Songs
        opening_song_number: document.getElementById('editOpeningSongNumber').value || '',
        opening_song_title: document.getElementById('editOpeningSongTitle').value || '',
        middle_song_number: document.getElementById('editMiddleSongNumber').value || '',
        middle_song_title: document.getElementById('editMiddleSongTitle').value || '',
        closing_song_number: document.getElementById('editClosingSongNumber').value || '',
        closing_song_title: document.getElementById('editClosingSongTitle').value || '',

        // Treasures section
        treasures_speaker_id: document.getElementById('editTreasuresSpeaker').value || null,
        gems_presenter_id: document.getElementById('editGemsPresenter').value || null,

        // Bible reading - Use only the canonical field names from the database schema
        bible_reading_id: document.getElementById('editBibleReader').value || null,
        bible_reading: formData.get('bible_reading') || '',

        // Ministry section - collect data from dynamically generated parts
        // For backward compatibility, we still use the original field names
        first_ministry_student_id: document.getElementById('editFirstMinistryStudent')?.value ||
                                  document.getElementById('editMinistryStudent_0')?.value || null,
        first_ministry_assistant_id: document.getElementById('editFirstMinistryAssistant')?.value ||
                                    document.getElementById('editMinistryAssistant_0')?.value || null,
        second_ministry_student_id: document.getElementById('editSecondMinistryStudent')?.value ||
                                   document.getElementById('editMinistryStudent_1')?.value || null,
        second_ministry_assistant_id: document.getElementById('editSecondMinistryAssistant')?.value ||
                                     document.getElementById('editMinistryAssistant_1')?.value || null,
        ministry_speaker_id: document.getElementById('editMinistrySpeaker').value || null,

        // Store additional ministry parts if present (for 4-part structure)
        ministry_parts: collectMinistryParts(),

        // Christian Life section - collect data from dynamically generated parts
        // For backward compatibility, we still use the original field names
        christian_life_part_speaker_id: document.getElementById('editChristianLifePartSpeaker').value || null,
        congregation_bible_study_id: document.getElementById('editCongregationBibleStudy').value || null,

        // Store additional Christian Life parts if present (for 3-part structure)
        christian_life_parts: collectChristianLifeParts(),

        // Tasks section - These fields now exist in the database
        audio_video_id: document.getElementById('editAudioVideo').value || null,
        platform_id: document.getElementById('editPlatform').value || null,
        microphone1_id: document.getElementById('editMicrophone1').value || null,
        microphone2_id: document.getElementById('editMicrophone2').value || null,
        cleaning_group: document.getElementById('editCleaningGroup').value || ''
      };

      // Add zoom info
      updatedMeeting.zoom_id = formData.get('zoom_id') || '';
      updatedMeeting.zoom_password = formData.get('zoom_password') || '';
      updatedMeeting.zoom_link = formData.get('zoom_link') || '';

      // Log the form data for debugging
      console.log('Form data:');
      for (const [key, value] of formData.entries()) {
        console.log(`${key}: ${value}`);
      }

      // Log the updated meeting object for debugging
      console.log('Updated meeting object:', updatedMeeting);

      // Log all dropdown values for debugging
      console.log('Dropdown values:');
      console.log('Chairman:', document.getElementById('editChairman').value);
      console.log('Opening Prayer:', document.getElementById('editOpeningPrayer').value);
      console.log('Treasures Speaker:', document.getElementById('editTreasuresSpeaker').value);
      console.log('Gems Presenter:', document.getElementById('editGemsPresenter').value);
      console.log('Bible Reader:', document.getElementById('editBibleReader').value);
      console.log('Closing Prayer:', document.getElementById('editClosingPrayer').value);

      console.log('First Ministry Student:', document.getElementById('editFirstMinistryStudent').value);
      console.log('First Ministry Assistant:', document.getElementById('editFirstMinistryAssistant').value);
      console.log('Second Ministry Student:', document.getElementById('editSecondMinistryStudent').value);
      console.log('Second Ministry Assistant:', document.getElementById('editSecondMinistryAssistant').value);
      console.log('Ministry Speaker:', document.getElementById('editMinistrySpeaker').value);
      console.log('Christian Life Part Speaker:', document.getElementById('editChristianLifePartSpeaker').value);
      console.log('Congregation Bible Study:', document.getElementById('editCongregationBibleStudy').value);

      // Log task and cleaning fields specifically
      console.log('Task and Cleaning Fields:');
      console.log('Audio/Video:', document.getElementById('editAudioVideo').value);
      console.log('Platform:', document.getElementById('editPlatform').value);
      console.log('Microphone1:', document.getElementById('editMicrophone1').value);
      console.log('Microphone2:', document.getElementById('editMicrophone2').value);
      console.log('Cleaning Group:', document.getElementById('editCleaningGroup').value);

      // Double-check that these values are included in the updatedMeeting object
      console.log('Task and Cleaning Fields in updatedMeeting object:');
      console.log('audio_video_id in updatedMeeting:', updatedMeeting.audio_video_id);
      console.log('platform_id in updatedMeeting:', updatedMeeting.platform_id);
      console.log('microphone1_id in updatedMeeting:', updatedMeeting.microphone1_id);
      console.log('microphone2_id in updatedMeeting:', updatedMeeting.microphone2_id);
      console.log('cleaning_group in updatedMeeting:', updatedMeeting.cleaning_group);

      try {
        // Show loading message
        showLoading('Actualizando reunión...');

        // Log the data being sent to the API
        console.log('Sending data to update meeting API:', meetingId, updatedMeeting);

        // Update meeting via API
        const result = await updateMeeting(meetingId, updatedMeeting);
        console.log('Update meeting result:', result);

        // Hide loading message
        hideLoading();

        // Close modal regardless of result to ensure it always closes
        closeEditMeetingModal();

        if (result) {
          // Refresh meetings list
          console.log('Refreshing meetings list...');
          meetings = await fetchMeetings();
          renderMeetings();

          // Show success message
          showSuccess('Reunión actualizada exitosamente');
        } else {
          // Show error message
          showError('Error al actualizar la reunión. Por favor, intente de nuevo.');
        }
      } catch (error) {
        console.error('Error updating meeting:', error);
        hideLoading();
        showError(`Error al actualizar la reunión: ${error.message}`);
      }
    }

    // Delete meeting
    async function handleDeleteMeeting(meetingId) {
      if (confirm('¿Está seguro de que desea eliminar esta reunión?')) {
        try {
          // Show loading message
          showLoading('Eliminando reunión...');

          console.log(`Attempting to delete meeting with ID: ${meetingId}`);

          // Get the authentication token from localStorage
          const token = localStorage.getItem('token');

          if (!token) {
            throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
          }

          console.log('Trying WOL endpoint first...');

          // Try the WOL endpoint first
          const wolResponse = await fetch(`/api/midweek/wol/meetings/${meetingId}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          console.log('WOL DELETE Response status:', wolResponse.status, wolResponse.statusText);

          let success = false;

          if (wolResponse.ok) {
            const wolData = await wolResponse.json();
            console.log('WOL DELETE Response data:', wolData);

            if (wolData.success) {
              success = true;
              console.log('Successfully deleted meeting using WOL endpoint');
            }
          } else {
            console.log('WOL endpoint failed with status:', wolResponse.status);
            console.log('Falling back to regular endpoint...');

            // If WOL endpoint fails, try the regular endpoint
            const response = await fetch(`/api/midweek/meetings/${meetingId}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            console.log('Regular DELETE Response status:', response.status, response.statusText);

            if (response.ok) {
              const data = await response.json();
              console.log('Regular DELETE Response data:', data);

              if (data.success) {
                success = true;
                console.log('Successfully deleted meeting using regular endpoint');
              }
            }
          }

          // Hide loading message
          hideLoading();

          if (success) {
            console.log('Meeting deleted successfully, refreshing meetings list...');

            // Remove the meeting from the local array to immediately update the UI
            meetings = meetings.filter(m => m.id !== meetingId);
            console.log('Filtered meetings array:', meetings.length);

            // Re-render the meetings list with the updated array
            renderMeetings();

            // Also fetch fresh data from the server to ensure we're in sync
            console.log('Fetching fresh data from server...');
            meetings = await fetchMeetings();
            console.log('Fetched meetings:', meetings.length);
            renderMeetings();

            // Show success message
            showSuccess('Reunión eliminada exitosamente');
          } else {
            showError('No se pudo eliminar la reunión. Por favor, intente de nuevo.');
          }
        } catch (error) {
          console.error('Error deleting meeting:', error);
          hideLoading();
          showError(`Error al eliminar la reunión: ${error.message}`);
        }
      }
    }

    // Open select week modal
    function openSelectWeekModal() {
      const modal = document.getElementById('selectWeekModal');
      modal.style.display = 'flex';

      // Load available weeks
      loadAvailableWeeks();
    }

    // Close select week modal
    function closeSelectWeekModal() {
      const modal = document.getElementById('selectWeekModal');
      modal.style.display = 'none';
    }

    // Load weeks for selected workbook
    function loadWeeksForWorkbook() {
      const workbookSelector = document.getElementById('workbookSelector');
      const weekSelector = document.getElementById('weekSelector');
      const selectedWorkbook = workbookSelector.value;

      if (!selectedWorkbook) {
        weekSelector.innerHTML = '<option value="">Primero seleccione un mes</option>';
        weekSelector.disabled = true;
        return;
      }

      // Enable week selector
      weekSelector.disabled = false;

      // Show loading state
      weekSelector.innerHTML = '<option value="">Cargando semanas disponibles...</option>';

      // Define weeks for each workbook
      const workbookWeeks = {
        'marzo-abril-2025-mwb': [
          { id: 'marzo-abril-2025-semana1', date: '3-9 marzo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-3-9-marzo/' },
          { id: 'marzo-abril-2025-semana2', date: '10-16 marzo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-10-16-marzo/' },
          { id: 'marzo-abril-2025-semana3', date: '17-23 marzo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-17-23-marzo/' },
          { id: 'marzo-abril-2025-semana4', date: '24-30 marzo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-24-30-marzo/' },
          { id: 'marzo-abril-2025-semana5', date: '31 marzo-6 abril 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-31-marzo-6-abril/' },
          { id: 'marzo-abril-2025-semana6', date: '7-13 abril 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-7-13-abril/' },
          { id: 'marzo-abril-2025-semana7', date: '14-20 abril 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-14-20-abril/' },
          { id: 'marzo-abril-2025-semana8', date: '21-27 abril 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/marzo-abril-2025-mwb/reunion-vida-ministerio-21-27-abril/' }
        ],
        'mayo-junio-2025-mwb': [
          { id: 'mayo-junio-2025-semana1', date: '28 abril-4 mayo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-28-abril-4-mayo/' },
          { id: 'mayo-junio-2025-semana2', date: '5-11 mayo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-5-11-mayo/' },
          { id: 'mayo-junio-2025-semana3', date: '12-18 mayo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-12-18-mayo/' },
          { id: 'mayo-junio-2025-semana4', date: '19-25 mayo 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-19-25-mayo/' },
          { id: 'mayo-junio-2025-semana5', date: '26 mayo-1 junio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-26-mayo-1-junio/' },
          { id: 'mayo-junio-2025-semana6', date: '2-8 junio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-2-8-junio/' },
          { id: 'mayo-junio-2025-semana7', date: '9-15 junio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-9-15-junio/' },
          { id: 'mayo-junio-2025-semana8', date: '16-22 junio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-16-22-junio/' },
          { id: 'mayo-junio-2025-semana9', date: '23-29 junio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/mayo-junio-2025-mwb/reunion-vida-ministerio-23-29-junio/' }
        ],
        'julio-agosto-2025-mwb': [
          { id: 'julio-agosto-2025-semana1', date: '30 junio-6 julio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-30-junio-6-julio/' },
          { id: 'julio-agosto-2025-semana2', date: '7-13 julio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-7-13-julio/' },
          { id: 'julio-agosto-2025-semana3', date: '14-20 julio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-14-20-julio/' },
          { id: 'julio-agosto-2025-semana4', date: '21-27 julio 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-21-27-julio/' },
          { id: 'julio-agosto-2025-semana5', date: '28 julio-3 agosto 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-28-julio-3-agosto/' },
          { id: 'julio-agosto-2025-semana6', date: '4-10 agosto 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-4-10-agosto/' },
          { id: 'julio-agosto-2025-semana7', date: '11-17 agosto 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-11-17-agosto/' },
          { id: 'julio-agosto-2025-semana8', date: '18-24 agosto 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-18-24-agosto/' },
          { id: 'julio-agosto-2025-semana9', date: '25-31 agosto 2025', url: 'https://www.jw.org/es/biblioteca/guia-actividades-reunion-testigos-jehova/julio-agosto-2025-mwb/reunion-vida-ministerio-25-31-agosto/' }
        ]
      };

      // Get weeks for selected workbook
      const weeks = workbookWeeks[selectedWorkbook] || [];

      if (weeks.length === 0) {
        weekSelector.innerHTML = '<option value="">No hay semanas disponibles</option>';
        return;
      }

      // Populate week selector
      weekSelector.innerHTML = '<option value="">Seleccione una semana</option>';

      weeks.forEach(week => {
        const option = document.createElement('option');
        option.value = JSON.stringify({ id: week.id, url: week.url });
        option.textContent = `Semana del ${week.date}`;
        weekSelector.appendChild(option);
      });
    }

    // Fetch selected week data from jw.org
    async function fetchSelectedWeekData() {
      const weekSelectorValue = document.getElementById('weekSelector').value;

      if (!weekSelectorValue) {
        showError('Por favor seleccione una semana');
        return;
      }

      try {
        // Parse the selected week data
        const selectedWeek = JSON.parse(weekSelectorValue);
        const url = selectedWeek.url;

        // Show loading message
        showLoading('Obteniendo datos de la reunión desde JW.org...');

        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        console.log(`Fetching meeting data from JW.org URL: ${url}`);

        // Fetch meeting data from JW.org using the API
        const response = await fetch('/api/midweek/fetch-from-jw', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({ url })
        });

        console.log('API Response status:', response.status, response.statusText);

        if (!response.ok) {
          throw new Error(`Error al obtener datos de JW.org: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API Response data:', data);

        if (!data.success) {
          throw new Error(data.message || 'Error al obtener datos de JW.org');
        }

        // Extract meeting data from the response
        const meetingData = data.meetingData;

        if (!meetingData) {
          throw new Error('No se encontraron datos de la reunión');
        }

        console.log('Meeting data from JW.org:', meetingData);

        // Transform the meeting data to the format expected by renderMeetingPreview
        const transformedData = {
          treasures: {
            talk: meetingData.treasures_talk_title || 'Tesoros de la Biblia',
            bibleReading: meetingData.bible_reading || 'Lectura de la Biblia'
          },
          ministry: {
            firstPart: meetingData.first_ministry_part_title || 'Primera conversación',
            secondPart: meetingData.second_ministry_part_title || 'Revisita',
            talk: meetingData.ministry_talk_title || 'Curso bíblico'
          },
          christianLiving: {
            part1: meetingData.christian_life_part_title || 'Necesidades de la congregación'
          },
          songs: {
            opening: `${meetingData.opening_song_number || '7'} - ${meetingData.opening_song_title || 'Jehová es mi fuerza y mi salvación'}`,
            middle: `${meetingData.middle_song_number || '46'} - ${meetingData.middle_song_title || 'Gracias, Jehová'}`,
            closing: `${meetingData.closing_song_number || '73'} - ${meetingData.closing_song_title || 'Danos fuerzas y valor'}`
          }
        };

        // Close select week modal
        closeSelectWeekModal();

        // Render the meeting in the frontend style with dropdown selectors
        renderMeetingPreview(transformedData);

        // Set the meeting date
        populateAddMeetingForm(transformedData);

        // Hide loading
        hideLoading();

        // Show success message
        showSuccess('Datos obtenidos correctamente desde JW.org');
      } catch (error) {
        hideLoading();
        console.error('Error fetching selected week data:', error);
        showError(`Error al obtener datos de la semana: ${error.message}`);

        // Fallback to mock data if there's an error
        console.log('Falling back to mock data due to error');

        const mockMeetingData = {
          treasures: {
            talk: 'Tesoros de la Biblia: "Jehová es mi fuerza y mi salvación"',
            bibleReading: 'Lectura de la Biblia: Salmos 27:1-14'
          },
          ministry: {
            firstPart: 'Primera conversación: Mateo 24:14',
            secondPart: 'Revisita: Salmos 83:18',
            talk: 'Curso bíblico: Juan 17:3'
          },
          christianLiving: {
            part1: 'Necesidades de la congregación'
          },
          songs: {
            opening: '7 - Jehová es mi fuerza y mi salvación',
            middle: '46 - Gracias, Jehová',
            closing: '73 - Danos fuerzas y valor'
          }
        };

        // Close select week modal
        closeSelectWeekModal();

        // Render the meeting in the frontend style with dropdown selectors
        renderMeetingPreview(mockMeetingData);

        // Set the meeting date
        populateAddMeetingForm(mockMeetingData);
      }
    }

    // Populate add meeting form with meeting data
    function populateAddMeetingForm(meetingData) {
      // Set meeting date to next Wednesday
      const today = new Date();
      const daysUntilWednesday = (3 - today.getDay() + 7) % 7;
      const nextWednesday = new Date(today);
      nextWednesday.setDate(today.getDate() + daysUntilWednesday);

      document.getElementById('meetingDate').valueAsDate = nextWednesday;

      // We don't need to populate the form fields anymore since we're using the frontend-style display
      // The meeting data is already displayed in the meeting preview container
      // The member dropdowns are populated by the populateAllMemberDropdowns function
    }

    // Render the meeting preview in the frontend style with dropdown selectors
    function renderMeetingPreview(meetingData) {
      const previewContainer = document.getElementById('meetingPreviewContent');
      if (!previewContainer) return;

      // Get the meeting date
      const meetingDate = document.getElementById('meetingDate').value;
      const formattedDate = meetingDate ? new Date(meetingDate).toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) : 'Fecha por definir';

      // Extract data from the meeting data
      const { treasures, ministry, christianLiving, songs } = meetingData;

      // Create the HTML for the meeting preview
      let html = `
        <div class="meeting-header">
          <div class="meeting-title">Vida y Ministerio Cristianos</div>
          <div class="meeting-date">${formattedDate}</div>
        </div>

        <div class="meeting-section-title">TESOROS DE LA BIBLIA</div>
        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Canción</div>
            <div class="assignment-person">
              <span class="song-number">${songs.opening.split(' - ')[0]}</span> - ${songs.opening.split(' - ')[1] || 'Jehová es mi fuerza y mi salvación'}
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Presidente</div>
            <div class="assignment-person">
              <select class="form-select" id="meetingChairman" name="chairman" required>
                <option value="">Seleccionar presidente</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Oración inicial</div>
            <div class="assignment-person">
              <select class="form-select" id="openingPrayer" name="opening_prayer">
                <option value="">Seleccionar hermano</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">${treasures.talk}</div>
            <div class="assignment-person">
              <select class="form-select" id="treasuresTalkSpeaker" name="treasures_talk_speaker">
                <option value="">Seleccionar orador</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Busquemos perlas escondidas</div>
            <div class="assignment-person">
              <select class="form-select" id="gemsPresenter" name="gems_presenter">
                <option value="">Seleccionar presentador</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">${treasures.bibleReading}</div>
            <div class="assignment-person">
              <select class="form-select" id="bibleReader" name="bible_reader">
                <option value="">Seleccionar lector</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="meeting-section-title">SEAMOS MEJORES MAESTROS</div>
        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">${ministry.firstPart}</div>
            <div class="assignment-person" style="display: flex; gap: 10px;">
              <select class="form-select" id="firstMinistryStudent" name="first_ministry_student" style="flex: 1;">
                <option value="">Estudiante</option>
                <!-- Will be populated by JavaScript -->
              </select>
              <select class="form-select" id="firstMinistryAssistant" name="first_ministry_assistant" style="flex: 1;">
                <option value="">Ayudante</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">${ministry.secondPart}</div>
            <div class="assignment-person" style="display: flex; gap: 10px;">
              <select class="form-select" id="secondMinistryStudent" name="second_ministry_student" style="flex: 1;">
                <option value="">Estudiante</option>
                <!-- Will be populated by JavaScript -->
              </select>
              <select class="form-select" id="secondMinistryAssistant" name="second_ministry_assistant" style="flex: 1;">
                <option value="">Ayudante</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">${ministry.talk}</div>
            <div class="assignment-person">
              <select class="form-select" id="ministryTalkSpeaker" name="ministry_talk_speaker">
                <option value="">Seleccionar orador</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="meeting-section-title">NUESTRA VIDA CRISTIANA</div>
        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Canción</div>
            <div class="assignment-person">
              <span class="song-number">${songs.middle.split(' - ')[0]}</span> - ${songs.middle.split(' - ')[1] || 'Gracias, Jehová'}
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">${christianLiving.part1}</div>
            <div class="assignment-person">
              <select class="form-select" id="christianLifePartSpeaker" name="christian_life_part_speaker">
                <option value="">Seleccionar presentador</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Estudio bíblico de la congregación</div>
            <div class="assignment-person">
              <select class="form-select" id="congregationBibleStudy" name="congregation_bible_study">
                <option value="">Seleccionar conductor</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Canción</div>
            <div class="assignment-person">
              <span class="song-number">${songs.closing.split(' - ')[0]}</span> - ${songs.closing.split(' - ')[1] || 'Danos fuerzas y valor'}
            </div>
          </div>
        </div>

        <div class="assignment-item">
          <div class="assignment-details">
            <div class="assignment-title">Oración final</div>
            <div class="assignment-person">
              <select class="form-select" id="closingPrayer" name="closing_prayer">
                <option value="">Seleccionar hermano</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="tasks-title">TAREAS</div>
        <div class="tasks-section">
          <div class="task-item">
            <div class="task-name">Operador de audio</div>
            <div class="task-assignee">
              <select class="form-select" id="audioVideo" name="audio_video">
                <option value="">Seleccionar hermano</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
          <div class="task-item">
            <div class="task-name">Plataforma</div>
            <div class="task-assignee">
              <select class="form-select" id="platform" name="platform">
                <option value="">Seleccionar hermano</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
          <div class="task-item">
            <div class="task-name">Micrófonos</div>
            <div class="task-assignee" style="display: flex; gap: 10px;">
              <select class="form-select" id="microphone1" name="microphone1" style="flex: 1;">
                <option value="">Hermano 1</option>
                <!-- Will be populated by JavaScript -->
              </select>
              <select class="form-select" id="microphone2" name="microphone2" style="flex: 1;">
                <option value="">Hermano 2</option>
                <!-- Will be populated by JavaScript -->
              </select>
            </div>
          </div>
        </div>

        <div class="cleaning-title">LIMPIEZA Y MANTENIMIENTO DEL SALÓN</div>
        <div class="cleaning-section">
          <div class="cleaning-item">
            <div class="cleaning-area">Limpieza del Salon</div>
            <div class="cleaning-group">
              <select class="form-select" id="cleaningGroup" name="cleaning_group">
                <option value="">Seleccionar grupo</option>
                <option value="1">Grupo 1</option>
                <option value="2">Grupo 2</option>
                <option value="3">Grupo 3</option>
                <option value="4">Grupo 4</option>
                <option value="5">Grupo 5</option>
                <option value="6">Grupo 6</option>
                <option value="7">Grupo 7</option>
              </select>
            </div>
          </div>
        </div>

        <div class="zoom-title">ZOOM</div>
        <div class="zoom-section">
          <div class="zoom-info">
            <strong>ID de reunión:</strong>
            <input type="text" class="form-input" id="meetingZoomId" name="zoom_id" placeholder="ID de reunión">
          </div>
          <div class="zoom-info">
            <strong>Código de acceso:</strong>
            <input type="text" class="form-input" id="meetingZoomPassword" name="zoom_password" placeholder="Código de acceso">
          </div>
          <div class="zoom-info">
            <strong>Enlace:</strong>
            <input type="url" class="form-input" id="meetingZoomLink" name="zoom_link" placeholder="Enlace de Zoom">
          </div>
        </div>
      `;

      // Set the HTML content
      previewContainer.innerHTML = html;

      // Show the add meeting modal
      openAddMeetingModal();

      // Populate the member dropdowns
      populateAllMemberDropdowns();
    }

    // Fetch members from API
    async function fetchMembers() {
      try {
        // Get the authentication token from localStorage
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No estás autenticado. Por favor, inicia sesión nuevamente.');
        }

        const response = await fetch('/api/members', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          if (response.status === 401) {
            throw new Error('No tienes permiso para ver los miembros. Por favor, inicia sesión nuevamente.');
          } else {
            throw new Error(`Error al cargar los miembros: ${response.status}`);
          }
        }

        const data = await response.json();
        console.log('Fetched members:', data); // Debug log

        // Check if the response has a members property (new API format)
        const members = data.members || data;

        // Add gender property based on name if not present
        const membersWithGender = members.map(member => {
          if (!member.gender) {
            // Simple heuristic: names ending with 'a' are likely female in Spanish
            const isFemale = member.name.endsWith('a') ||
                           member.name.includes('María') ||
                           member.name.includes('Lourdes') ||
                           member.name.includes('Danay');
            return { ...member, gender: isFemale ? 'female' : 'male' };
          }
          return member;
        });

        console.log('Members with gender:', membersWithGender);

        // If no members are returned, create some mock members for testing
        if (!membersWithGender || membersWithGender.length === 0) {
          console.warn('No members returned from API, using mock data');
          return [
            { id: 1, name: 'Juan Pérez', role: 'elder', gender: 'male' },
            { id: 2, name: 'Carlos Rodríguez', role: 'ministerial_servant', gender: 'male' },
            { id: 3, name: 'Miguel González', role: 'publisher', gender: 'male' },
            { id: 4, name: 'Ana Martínez', role: 'publisher', gender: 'female' },
            { id: 5, name: 'Pedro Sánchez', role: 'elder', gender: 'male' },
            { id: 6, name: 'Luis Morales', role: 'ministerial_servant', gender: 'male' },
            { id: 7, name: 'Roberto Jiménez', role: 'publisher', gender: 'male' },
            { id: 8, name: 'María López', role: 'publisher', gender: 'female' }
          ];
        }

        return membersWithGender;
      } catch (error) {
        console.error('Error fetching members:', error);
        console.warn('Using mock members due to error');
        showError('Error al cargar los miembros: ' + error.message);

        // Return mock members in case of error
        return [
          { id: 1, name: 'Juan Pérez', role: 'elder', gender: 'male' },
          { id: 2, name: 'Carlos Rodríguez', role: 'ministerial_servant', gender: 'male' },
          { id: 3, name: 'Miguel González', role: 'publisher', gender: 'male' },
          { id: 4, name: 'Ana Martínez', role: 'publisher', gender: 'female' },
          { id: 5, name: 'Pedro Sánchez', role: 'elder', gender: 'male' },
          { id: 6, name: 'Luis Morales', role: 'ministerial_servant', gender: 'male' },
          { id: 7, name: 'Roberto Jiménez', role: 'publisher', gender: 'male' },
          { id: 8, name: 'María López', role: 'publisher', gender: 'female' }
        ];
      }
    }

    // Populate all member dropdowns
    async function populateAllMemberDropdowns() {
      try {
        // Fetch members from API
        const members = await fetchMembers();
        console.log('Members for dropdowns:', members); // Debug log

        // Check if members array is valid
        if (!members || !Array.isArray(members) || members.length === 0) {
          console.warn('No members available or invalid members array');
          // Use mock data if no members are available
          const mockMembers = [
            { id: 1, name: 'Juan Pérez', role: 'elder', gender: 'male' },
            { id: 2, name: 'Carlos Rodríguez', role: 'ministerial_servant', gender: 'male' },
            { id: 3, name: 'Miguel González', role: 'publisher', gender: 'male' },
            { id: 4, name: 'Ana Martínez', role: 'publisher', gender: 'female' },
            { id: 5, name: 'Pedro Sánchez', role: 'elder', gender: 'male' },
            { id: 6, name: 'Luis Morales', role: 'ministerial_servant', gender: 'male' },
            { id: 7, name: 'Roberto Jiménez', role: 'publisher', gender: 'male' },
            { id: 8, name: 'María López', role: 'publisher', gender: 'female' }
          ];

          // Populate all dropdowns with mock data
          populateDropdown('meetingChairman', mockMembers.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
          populateDropdown('openingPrayer', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('treasuresTalkSpeaker', mockMembers.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
          populateDropdown('gemsPresenter', mockMembers.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
          populateDropdown('bibleReader', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('firstMinistryStudent', mockMembers);
          populateDropdown('firstMinistryAssistant', mockMembers);
          populateDropdown('secondMinistryStudent', mockMembers);
          populateDropdown('secondMinistryAssistant', mockMembers);
          populateDropdown('ministryTalkSpeaker', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('christianLifePartSpeaker', mockMembers.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
          populateDropdown('congregationBibleStudy', mockMembers.filter(m => m.role === 'elder'));
          populateDropdown('closingPrayer', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('audioVideo', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('platform', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('microphone1', mockMembers.filter(m => m.gender === 'male'));
          populateDropdown('microphone2', mockMembers.filter(m => m.gender === 'male'));
          return;
        }

        // Populate all dropdowns
        populateDropdown('meetingChairman', members.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
        populateDropdown('openingPrayer', members.filter(m => m.gender === 'male'));
        populateDropdown('treasuresTalkSpeaker', members.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
        populateDropdown('gemsPresenter', members.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
        populateDropdown('bibleReader', members.filter(m => m.gender === 'male'));
        populateDropdown('firstMinistryStudent', members);
        populateDropdown('firstMinistryAssistant', members);
        populateDropdown('secondMinistryStudent', members);
        populateDropdown('secondMinistryAssistant', members);
        populateDropdown('ministryTalkSpeaker', members.filter(m => m.gender === 'male'));
        populateDropdown('christianLifePartSpeaker', members.filter(m => m.role === 'elder' || m.role === 'ministerial_servant'));
        populateDropdown('congregationBibleStudy', members.filter(m => m.role === 'elder'));
        populateDropdown('closingPrayer', members.filter(m => m.gender === 'male'));
        populateDropdown('audioVideo', members.filter(m => m.gender === 'male'));
        populateDropdown('platform', members.filter(m => m.gender === 'male'));
        populateDropdown('microphone1', members.filter(m => m.gender === 'male'));
        populateDropdown('microphone2', members.filter(m => m.gender === 'male'));
      } catch (error) {
        console.error('Error populating dropdowns:', error);
        showError('Error al cargar los miembros. Por favor, intente de nuevo.');
      }
    }

    // Helper function to populate a dropdown
    function populateDropdown(id, items) {
      const dropdown = document.getElementById(id);
      if (!dropdown) {
        console.warn(`Dropdown with id ${id} not found`);
        return;
      }

      console.log(`Populating dropdown ${id} with ${items.length} items`);

      // Keep the first option (placeholder)
      const firstOption = dropdown.options[0];
      dropdown.innerHTML = '';
      dropdown.appendChild(firstOption);

      // Add items to dropdown
      items.forEach(item => {
        const option = document.createElement('option');
        option.value = item.id;
        option.textContent = item.name;
        dropdown.appendChild(option);
      });

      console.log(`Dropdown ${id} now has ${dropdown.options.length} options`);
    }

    // Populate member dropdowns in the edit meeting form
    async function populateEditMemberDropdowns(meeting) {
      try {
        // Fetch members if not already available
        if (!members || members.length === 0) {
          members = await fetchMembers();
        }

        console.log('Members for edit dropdowns:', members);

        // Fetch member settings to get the preselected members for each part
        let memberSettings = {};
        try {
          // Get the authentication token from localStorage
          const token = localStorage.getItem('token');

          if (token) {
            // Fetch member settings from API
            const response = await fetch('/api/midweek/members-settings?congregation_id=1', {
              headers: {
                'Authorization': `Bearer ${token}`
              }
            });

            if (response.ok) {
              const data = await response.json();
              if (data.success) {
                memberSettings = data.settings || {};
                console.log('Fetched member settings for dropdowns:', memberSettings);
              }
            }
          }
        } catch (error) {
          console.error('Error fetching member settings:', error);
          // Continue with default settings if there's an error
        }

        // Map dropdown IDs to their corresponding settings keys
        const settingsMap = {
          'editOpeningPrayer': 'prayer_members',
          'editClosingPrayer': 'prayer_members',
          'editTreasuresSpeaker': 'treasures_members',
          'editGemsPresenter': 'gems_members', // Use the specific gems_members setting
          'editBibleReader': 'bible_reading_members',
          'editFirstMinistryStudent': 'ministry_members',
          'editFirstMinistryAssistant': 'ministry_members',
          'editSecondMinistryStudent': 'ministry_members',
          'editSecondMinistryAssistant': 'ministry_members',
          'editMinistrySpeaker': 'ministry_talk_members', // Use the specific ministry_talk_members setting
          'editChristianLifePartSpeaker': 'christian_life_members',
          'editCongregationBibleStudy': 'congregation_bible_study_members'
        };

        // Define role-based dropdown selectors
        const elderSelectors = [
          'editChairman',
          'editTreasuresSpeaker'
          // Removed 'editCongregationBibleStudy' to allow preselected publishers
        ];

        // Define dropdowns that can be filled by both elders and ministerial servants
        const elderAndMinisterialServantSelectors = [
          'editOpeningPrayer',
          'editClosingPrayer',
          'editGemsPresenter', // Both elders and ministerial servants can present Perlas Escondidas
          'editChristianLifePartSpeaker' // Both elders and ministerial servants can do Christian Life parts
        ];

        const ministerialServantSelectors = [
          'editBibleReader',
          'editMinistrySpeaker',
          'editAudioVideo',
          'editPlatform',
          'editMicrophone1',
          'editMicrophone2'
        ];

        const publisherSelectors = [
          'editFirstMinistryStudent',
          'editFirstMinistryAssistant',
          'editSecondMinistryStudent',
          'editSecondMinistryAssistant'
        ];

        // Helper function to populate a dropdown with filtered members
        const populateDropdownWithSettings = (id, allMembers, settingsKey) => {
          const dropdown = document.getElementById(id);
          if (!dropdown) return;

          // Keep the first option (placeholder)
          const firstOption = dropdown.options[0];
          dropdown.innerHTML = '';
          dropdown.appendChild(firstOption);

          // Get the member IDs from settings
          const memberIds = memberSettings[settingsKey] || [];

          // Filter members based on settings if available
          let filteredMembers = allMembers;
          if (memberIds && memberIds.length > 0) {
            filteredMembers = allMembers.filter(m => memberIds.includes(m.id.toString()));
          }

          // Add filtered members to dropdown
          filteredMembers.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = member.name;
            dropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          // Handle both naming conventions (prayer_beginning_id and opening_prayer_id)
          let fieldName = id.replace('edit', '').toLowerCase() + '_id';
          let fieldValue = meeting[fieldName];

          // Special handling for prayer fields that have two possible names
          if (id === 'editOpeningPrayer') {
            fieldValue = meeting.prayer_beginning_id || meeting.opening_prayer_id;

            // Also set the legacy hidden field
            const legacyField = document.getElementById('editOpeningPrayerLegacy');
            if (legacyField && fieldValue) {
              legacyField.value = fieldValue;
            }
          } else if (id === 'editClosingPrayer') {
            fieldValue = meeting.prayer_end_id || meeting.closing_prayer_id;

            // Also set the legacy hidden field
            const legacyField = document.getElementById('editClosingPrayerLegacy');
            if (legacyField && fieldValue) {
              legacyField.value = fieldValue;
            }
          }

          if (fieldValue) {
            console.log(`Setting ${id} to value ${fieldValue} from field ${fieldName}`);
            dropdown.value = fieldValue;

            // Trigger a change event to ensure any event listeners are notified
            const event = new Event('change', { bubbles: true });
            dropdown.dispatchEvent(event);
          }
        };

        // Populate chairman dropdown (always elders and ministerial servants)
        const chairmanDropdown = document.getElementById('editChairman');
        if (chairmanDropdown) {
          // Keep the first option (placeholder)
          const firstOption = chairmanDropdown.options[0];
          chairmanDropdown.innerHTML = '';
          chairmanDropdown.appendChild(firstOption);

          // Filter for elders and coordinator
          const chairmen = members.filter(m =>
            m.role === 'elder' ||
            m.role === 'coordinator' ||
            m.role === 'overseer_coordinator' ||
            m.role === 'ministerial_servant'
          );

          // Add chairman options
          chairmen.forEach(chairman => {
            const option = document.createElement('option');
            option.value = chairman.id;
            option.textContent = chairman.name;
            chairmanDropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          if (meeting.chairman_id) {
            chairmanDropdown.value = meeting.chairman_id;
          }
        }

        // Populate task dropdowns (always all brothers)
        const taskDropdowns = ['editAudioVideo', 'editPlatform', 'editMicrophone1', 'editMicrophone2'];
        taskDropdowns.forEach(id => {
          const dropdown = document.getElementById(id);
          if (!dropdown) return;

          // Keep the first option (placeholder)
          const firstOption = dropdown.options[0];
          dropdown.innerHTML = '';
          dropdown.appendChild(firstOption);

          // Filter for brothers (male members) - typically elders, ministerial servants, and coordinators
          // Note: In JW congregations, these roles are typically assigned to brothers
          const brothers = members.filter(m =>
            m.role === 'elder' ||
            m.role === 'ministerial_servant' ||
            m.role === 'overseer_coordinator' ||
            m.role === 'developer'
          );

          // Add brother options
          brothers.forEach(brother => {
            const option = document.createElement('option');
            option.value = brother.id;
            option.textContent = brother.name;
            dropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          const fieldName = id.replace('edit', '').toLowerCase() + '_id';
          const fieldValue = meeting[fieldName];

          if (fieldValue) {
            console.log(`Setting ${id} to value ${fieldValue} from field ${fieldName}`);
            dropdown.value = fieldValue;

            // Trigger a change event to ensure any event listeners are notified
            const event = new Event('change', { bubbles: true });
            dropdown.dispatchEvent(event);
          }
        });

        // Populate all other dropdowns based on settings
        Object.entries(settingsMap).forEach(([id, settingsKey]) => {
          // Skip task dropdowns that were already populated
          if (taskDropdowns.includes(id)) return;

          // Get the appropriate member list based on the dropdown type
          let memberList = members;

          if (elderSelectors.includes(id)) {
            // Only elders and coordinator
            memberList = members.filter(m =>
              m.role === 'elder' ||
              m.role === 'coordinator' ||
              m.role === 'overseer_coordinator'
            );
          } else if (elderAndMinisterialServantSelectors.includes(id)) {
            // Both elders and ministerial servants
            memberList = members.filter(m =>
              m.role === 'elder' ||
              m.role === 'coordinator' ||
              m.role === 'overseer_coordinator' ||
              m.role === 'ministerial_servant'
            );
          } else if (ministerialServantSelectors.includes(id)) {
            // Ministerial servants and elders
            memberList = members.filter(m =>
              m.role === 'ministerial_servant' ||
              m.role === 'elder' ||
              m.role === 'coordinator' ||
              m.role === 'overseer_coordinator'
            );
          }

          // Special case for Bible Reader - include ministerial servants, elders, and preselected publishers
          if (id === 'editBibleReader') {
            // Get ministerial servants and elders
            const msAndElders = members.filter(m =>
              m.role === 'ministerial_servant' ||
              m.role === 'elder' ||
              m.role === 'coordinator' ||
              m.role === 'overseer_coordinator'
            );

            // Get preselected members from settings
            const preselectedMemberIds = memberSettings['bible_reading_members'] || [];
            const preselectedPublishers = members.filter(m =>
              m.role === 'publisher' &&
              preselectedMemberIds.includes(m.id.toString())
            );

            // Combine ministerial servants, elders, and preselected publishers
            const combinedList = [...msAndElders, ...preselectedPublishers];

            // Remove duplicates (in case a member appears in both lists)
            const uniqueMembers = Array.from(new Map(combinedList.map(m => [m.id, m])).values());

            console.log(`Populating Bible reader dropdown with ${uniqueMembers.length} members (${msAndElders.length} MS/elders and ${preselectedPublishers.length} preselected publishers)`);

            populateDropdownWithSettings(id, uniqueMembers, settingsKey);
          }
          // Special case for congregation Bible study - include both elders and preselected publishers
          else if (id === 'editCongregationBibleStudy') {
            // Get elders
            const elders = members.filter(m =>
              m.role === 'elder' ||
              m.role === 'coordinator' ||
              m.role === 'overseer_coordinator'
            );

            // Get preselected members from settings
            const preselectedMemberIds = memberSettings['congregation_bible_study_members'] || [];
            const preselectedPublishers = members.filter(m =>
              (m.role === 'publisher' || m.role === 'ministerial_servant') &&
              preselectedMemberIds.includes(m.id.toString())
            );

            // Combine elders and preselected publishers
            const combinedList = [...elders, ...preselectedPublishers];

            // Remove duplicates (in case a member appears in both lists)
            const uniqueMembers = Array.from(new Map(combinedList.map(m => [m.id, m])).values());

            console.log(`Populating congregation Bible study dropdown with ${uniqueMembers.length} members (${elders.length} elders and ${preselectedPublishers.length} preselected publishers)`);

            populateDropdownWithSettings(id, uniqueMembers, settingsKey);
          } else {
            // Populate the dropdown with filtered members
            populateDropdownWithSettings(id, memberList, settingsKey);
          }
        });

        // Populate dynamically generated parts
        populateDynamicPartDropdowns(meeting, memberSettings);
      } catch (error) {
        console.error('Error populating edit dropdowns:', error);
        showError('Error al cargar los miembros para edición. Por favor, intente de nuevo.');
      }
    }

    // Function to populate dynamically generated part dropdowns
    function populateDynamicPartDropdowns(meeting, memberSettings) {
      console.log('Populating dynamic part dropdowns');

      try {
        // Get the member IDs from settings for ministry parts
        const ministryMemberIds = memberSettings?.ministry_members || [];
        console.log('Ministry member IDs from settings:', ministryMemberIds);

        // Filter members based on settings if available
        let ministryMembers = members;
        if (ministryMemberIds && ministryMemberIds.length > 0) {
          ministryMembers = members.filter(m => ministryMemberIds.includes(m.id.toString()));
          console.log(`Filtered ${ministryMembers.length} members for ministry parts`);
        }

        // Populate original fixed student dropdowns
        const fixedStudentDropdowns = [
          document.getElementById('editFirstMinistryStudent'),
          document.getElementById('editSecondMinistryStudent')
        ];

        fixedStudentDropdowns.forEach((dropdown, index) => {
          if (!dropdown) return;

          console.log(`Populating fixed student dropdown: ${dropdown.id}`);

          // Keep the first option (placeholder)
          const firstOption = dropdown.options[0];
          dropdown.innerHTML = '';
          dropdown.appendChild(firstOption);

          // Add filtered members to dropdown
          ministryMembers.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = member.name;
            dropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          let studentId = null;
          if (index === 0) {
            studentId = meeting.first_ministry_student_id;
          } else if (index === 1) {
            studentId = meeting.second_ministry_student_id;
          }

          if (studentId) {
            console.log(`Setting ${dropdown.id} to value ${studentId}`);
            dropdown.value = studentId;

            // Trigger a change event to ensure any event listeners are notified
            const event = new Event('change', { bubbles: true });
            dropdown.dispatchEvent(event);
          }
        });

        // Populate original fixed assistant dropdowns
        const fixedAssistantDropdowns = [
          document.getElementById('editFirstMinistryAssistant'),
          document.getElementById('editSecondMinistryAssistant')
        ];

        fixedAssistantDropdowns.forEach((dropdown, index) => {
          if (!dropdown) return;

          console.log(`Populating fixed assistant dropdown: ${dropdown.id}`);

          // Keep the first option (placeholder)
          const firstOption = dropdown.options[0];
          dropdown.innerHTML = '';
          dropdown.appendChild(firstOption);

          // Add filtered members to dropdown
          ministryMembers.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = member.name;
            dropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          let assistantId = null;
          if (index === 0) {
            assistantId = meeting.first_ministry_assistant_id;
          } else if (index === 1) {
            assistantId = meeting.second_ministry_assistant_id;
          }

          if (assistantId) {
            console.log(`Setting ${dropdown.id} to value ${assistantId}`);
            dropdown.value = assistantId;

            // Trigger a change event to ensure any event listeners are notified
            const event = new Event('change', { bubbles: true });
            dropdown.dispatchEvent(event);
          }
        });

        // Get all dynamically generated student dropdowns
        const dynamicStudentDropdowns = document.querySelectorAll('[id^="editMinistryStudent_"]');
        console.log(`Found ${dynamicStudentDropdowns.length} dynamic student dropdowns`);

        // Populate dynamic student dropdowns
        dynamicStudentDropdowns.forEach(dropdown => {
          console.log(`Populating dynamic student dropdown: ${dropdown.id}`);

          // Keep the first option (placeholder)
          const firstOption = dropdown.options[0];
          dropdown.innerHTML = '';
          dropdown.appendChild(firstOption);

          // Add filtered members to dropdown
          ministryMembers.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = member.name;
            dropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          const partIndex = dropdown.id.split('_')[1];
          let studentId = null;

          // Check if we have parts data
          if (meeting.parts && meeting.parts.ministry && meeting.parts.ministry.length > 0) {
            // Find the corresponding part in the meeting data
            const ministryParts = meeting.parts.ministry.filter(part =>
              part.title !== 'Seamos Mejores Maestros' &&
              !part.title.toLowerCase().includes('canción')
            );

            if (ministryParts[partIndex]) {
              studentId = ministryParts[partIndex].student_id;
            }
          } else {
            // Use legacy fields for backward compatibility
            if (partIndex === '0') {
              studentId = meeting.first_ministry_student_id;
            } else if (partIndex === '1') {
              studentId = meeting.second_ministry_student_id;
            }
          }

          if (studentId) {
            console.log(`Setting ${dropdown.id} to value ${studentId}`);
            dropdown.value = studentId;

            // Trigger a change event to ensure any event listeners are notified
            const event = new Event('change', { bubbles: true });
            dropdown.dispatchEvent(event);
          }
        });

        // Get all dynamically generated assistant dropdowns
        const dynamicAssistantDropdowns = document.querySelectorAll('[id^="editMinistryAssistant_"]');
        console.log(`Found ${dynamicAssistantDropdowns.length} dynamic assistant dropdowns`);

        // Populate dynamic assistant dropdowns
        dynamicAssistantDropdowns.forEach(dropdown => {
          console.log(`Populating dynamic assistant dropdown: ${dropdown.id}`);

          // Keep the first option (placeholder)
          const firstOption = dropdown.options[0];
          dropdown.innerHTML = '';
          dropdown.appendChild(firstOption);

          // Add filtered members to dropdown
          ministryMembers.forEach(member => {
            const option = document.createElement('option');
            option.value = member.id;
            option.textContent = member.name;
            dropdown.appendChild(option);
          });

          // Set selected value if available in the meeting data
          const partIndex = dropdown.id.split('_')[1];
          let assistantId = null;

          // Check if we have parts data
          if (meeting.parts && meeting.parts.ministry && meeting.parts.ministry.length > 0) {
            // Find the corresponding part in the meeting data
            const ministryParts = meeting.parts.ministry.filter(part =>
              part.title !== 'Seamos Mejores Maestros' &&
              !part.title.toLowerCase().includes('canción')
            );

            if (ministryParts[partIndex]) {
              assistantId = ministryParts[partIndex].assistant_id;
            }
          } else {
            // Use legacy fields for backward compatibility
            if (partIndex === '0') {
              assistantId = meeting.first_ministry_assistant_id;
            } else if (partIndex === '1') {
              assistantId = meeting.second_ministry_assistant_id;
            }
          }

          if (assistantId) {
            console.log(`Setting ${dropdown.id} to value ${assistantId}`);
            dropdown.value = assistantId;

            // Trigger a change event to ensure any event listeners are notified
            const event = new Event('change', { bubbles: true });
            dropdown.dispatchEvent(event);
          }
        });

        // Sync values between original and dynamic dropdowns
        syncDropdownValues('editFirstMinistryStudent', 'editMinistryStudent_0');
        syncDropdownValues('editFirstMinistryAssistant', 'editMinistryAssistant_0');
        syncDropdownValues('editSecondMinistryStudent', 'editMinistryStudent_1');
        syncDropdownValues('editSecondMinistryAssistant', 'editMinistryAssistant_1');

        console.log('Finished populating dynamic part dropdowns');
      } catch (error) {
        console.error('Error populating dynamic part dropdowns:', error);
      }
    }

    // Helper function to sync values between two dropdowns
    function syncDropdownValues(sourceId, targetId) {
      const sourceDropdown = document.getElementById(sourceId);
      const targetDropdown = document.getElementById(targetId);

      if (!sourceDropdown || !targetDropdown) return;

      // Set initial value from source to target
      targetDropdown.value = sourceDropdown.value;

      // Add event listener to keep them in sync
      sourceDropdown.addEventListener('change', () => {
        targetDropdown.value = sourceDropdown.value;
      });

      targetDropdown.addEventListener('change', () => {
        sourceDropdown.value = targetDropdown.value;
      });
    }

    // Function to populate song fields in the edit modal
    function populateSongFields(meeting) {
      console.log('Populating song fields with meeting data:', meeting);

      try {
        // Opening song
        const editOpeningSongNumber = document.getElementById('editOpeningSongNumber');
        const editOpeningSongTitle = document.getElementById('editOpeningSongTitle');
        const openingSongNumberDisplay = document.getElementById('openingSongNumberDisplay');
        const openingSongTitleDisplay = document.getElementById('openingSongTitleDisplay');
        const openingSongDisplay = document.getElementById('openingSongDisplay');
        const openingSongInputs = document.getElementById('openingSongInputs');

        if (editOpeningSongNumber && editOpeningSongTitle) {
          // Set input values
          editOpeningSongNumber.value = meeting.opening_song_number || '';
          editOpeningSongTitle.value = meeting.opening_song_title || '';
          console.log('Set opening song:', editOpeningSongNumber.value, editOpeningSongTitle.value);

          // Update display directly
          if (openingSongNumberDisplay && openingSongTitleDisplay) {
            // Format the display with number and title
            const number = meeting.opening_song_number || '';
            const title = meeting.opening_song_title || '';

            openingSongNumberDisplay.textContent = number;
            openingSongTitleDisplay.textContent = title ? ' - ' + title : '';

            // Show the formatted song in the display
            if (openingSongDisplay) {
              if (number || title) {
                // If we have either number or title, show them
                openingSongDisplay.style.display = 'inline';
                // Make sure inputs are hidden initially
                if (openingSongInputs) openingSongInputs.style.display = 'none';
              } else {
                // If both are empty, show a placeholder
                openingSongDisplay.textContent = 'Canción (Haga clic para editar)';
                openingSongDisplay.style.display = 'inline';
                // Make sure inputs are hidden initially
                if (openingSongInputs) openingSongInputs.style.display = 'none';
              }
            }
          }
        }

        // Middle song
        const editMiddleSongNumber = document.getElementById('editMiddleSongNumber');
        const editMiddleSongTitle = document.getElementById('editMiddleSongTitle');
        const middleSongNumberDisplay = document.getElementById('middleSongNumberDisplay');
        const middleSongTitleDisplay = document.getElementById('middleSongTitleDisplay');
        const middleSongDisplay = document.getElementById('middleSongDisplay');
        const middleSongInputs = document.getElementById('middleSongInputs');

        if (editMiddleSongNumber && editMiddleSongTitle) {
          // Set input values
          editMiddleSongNumber.value = meeting.middle_song_number || '';
          editMiddleSongTitle.value = meeting.middle_song_title || '';
          console.log('Set middle song:', editMiddleSongNumber.value, editMiddleSongTitle.value);

          // Update display directly
          if (middleSongNumberDisplay && middleSongTitleDisplay) {
            // Format the display with number and title
            const number = meeting.middle_song_number || '';
            const title = meeting.middle_song_title || '';

            middleSongNumberDisplay.textContent = number;
            middleSongTitleDisplay.textContent = title ? ' - ' + title : '';

            // Show the formatted song in the display
            if (middleSongDisplay) {
              if (number || title) {
                // If we have either number or title, show them
                middleSongDisplay.style.display = 'inline';
                // Make sure inputs are hidden initially
                if (middleSongInputs) middleSongInputs.style.display = 'none';
              } else {
                // If both are empty, show a placeholder
                middleSongDisplay.textContent = 'Canción (Haga clic para editar)';
                middleSongDisplay.style.display = 'inline';
                // Make sure inputs are hidden initially
                if (middleSongInputs) middleSongInputs.style.display = 'none';
              }
            }
          }
        }

        // Closing song
        const editClosingSongNumber = document.getElementById('editClosingSongNumber');
        const editClosingSongTitle = document.getElementById('editClosingSongTitle');
        const closingSongNumberDisplay = document.getElementById('closingSongNumberDisplay');
        const closingSongTitleDisplay = document.getElementById('closingSongTitleDisplay');
        const closingSongDisplay = document.getElementById('closingSongDisplay');
        const closingSongInputs = document.getElementById('closingSongInputs');

        if (editClosingSongNumber && editClosingSongTitle) {
          // Set input values
          editClosingSongNumber.value = meeting.closing_song_number || '';
          editClosingSongTitle.value = meeting.closing_song_title || '';
          console.log('Set closing song:', editClosingSongNumber.value, editClosingSongTitle.value);

          // Update display directly
          if (closingSongNumberDisplay && closingSongTitleDisplay) {
            // Format the display with number and title
            const number = meeting.closing_song_number || '';
            const title = meeting.closing_song_title || '';

            closingSongNumberDisplay.textContent = number;
            closingSongTitleDisplay.textContent = title ? ' - ' + title : '';

            // Show the formatted song in the display
            if (closingSongDisplay) {
              if (number || title) {
                // If we have either number or title, show them
                closingSongDisplay.style.display = 'inline';
                // Make sure inputs are hidden initially
                if (closingSongInputs) closingSongInputs.style.display = 'none';
              } else {
                // If both are empty, show a placeholder
                closingSongDisplay.textContent = 'Canción (Haga clic para editar)';
                closingSongDisplay.style.display = 'inline';
                // Make sure inputs are hidden initially
                if (closingSongInputs) closingSongInputs.style.display = 'none';
              }
            }
          }
        }

        // Also update song displays through the standard function
        updateSongDisplays();

      } catch (error) {
        console.error('Error populating song fields:', error);
      }
    }

    // Function to generate ministry parts for the edit modal
    function generateEditMinistryParts(meeting) {
      console.log('Generating ministry parts for edit modal');
      const container = document.getElementById('editMinistryPartsContainer');
      if (!container) {
        console.error('Ministry parts container not found');
        return;
      }

      // Clear the container
      container.innerHTML = '';

      // Check if we have parts data from the meeting
      if (meeting.parts && meeting.parts.ministry && meeting.parts.ministry.length > 0) {
        // Filter out section headers and songs
        const ministryParts = meeting.parts.ministry.filter(part =>
          part.title !== 'Seamos Mejores Maestros' &&
          !part.title.toLowerCase().includes('canción')
        );

        console.log('Filtered ministry parts:', ministryParts);

        // Generate HTML for each ministry part
        ministryParts.forEach((part, index) => {
          let html = '';

          // Check if this is a student part (needs student and assistant)
          const isStudentPart = index < ministryParts.length - 1; // All except the last part

          if (isStudentPart) {
            // For the first two parts, use both the original IDs and the dynamic IDs
            if (index === 0) {
              html = `
                <div class="assignment-item" data-part-index="${index}">
                  <div class="assignment-details">
                    <div class="assignment-title">${part.title || 'Primera conversación'}</div>
                    <div class="assignment-person" style="display: flex; gap: 10px;">
                      <select class="form-select" id="editFirstMinistryStudent" name="first_ministry_student_id" style="flex: 1;">
                        <option value="">Estudiante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editMinistryStudent_${index}" name="ministry_student_${index}_id" style="display: none;">
                        <option value="">Estudiante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editFirstMinistryAssistant" name="first_ministry_assistant_id" style="flex: 1;">
                        <option value="">Ayudante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editMinistryAssistant_${index}" name="ministry_assistant_${index}_id" style="display: none;">
                        <option value="">Ayudante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                    </div>
                  </div>
                </div>
              `;
            } else if (index === 1) {
              html = `
                <div class="assignment-item" data-part-index="${index}">
                  <div class="assignment-details">
                    <div class="assignment-title">${part.title || 'Revisita'}</div>
                    <div class="assignment-person" style="display: flex; gap: 10px;">
                      <select class="form-select" id="editSecondMinistryStudent" name="second_ministry_student_id" style="flex: 1;">
                        <option value="">Estudiante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editMinistryStudent_${index}" name="ministry_student_${index}_id" style="display: none;">
                        <option value="">Estudiante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editSecondMinistryAssistant" name="second_ministry_assistant_id" style="flex: 1;">
                        <option value="">Ayudante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editMinistryAssistant_${index}" name="ministry_assistant_${index}_id" style="display: none;">
                        <option value="">Ayudante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                    </div>
                  </div>
                </div>
              `;
            } else {
              // For additional parts (beyond the original fixed structure), use only dynamic IDs
              html = `
                <div class="assignment-item" data-part-index="${index}">
                  <div class="assignment-details">
                    <div class="assignment-title">${part.title || 'Parte adicional'}</div>
                    <div class="assignment-person" style="display: flex; gap: 10px;">
                      <select class="form-select" id="editMinistryStudent_${index}" name="ministry_student_${index}_id" style="flex: 1;">
                        <option value="">Estudiante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                      <select class="form-select" id="editMinistryAssistant_${index}" name="ministry_assistant_${index}_id" style="flex: 1;">
                        <option value="">Ayudante</option>
                        <!-- Will be populated by JavaScript -->
                      </select>
                    </div>
                  </div>
                </div>
              `;
            }
          } else {
            // Last part is always the talk/discourse
            html = `
              <div class="assignment-item" data-part-index="${index}">
                <div class="assignment-details">
                  <div class="assignment-title">${part.title || 'Discurso'}</div>
                  <div class="assignment-person">
                    <select class="form-select" id="editMinistrySpeaker" name="ministry_speaker_id">
                      <option value="">Seleccionar orador</option>
                      <!-- Will be populated by JavaScript -->
                    </select>
                  </div>
                </div>
              </div>
            `;
          }

          container.innerHTML += html;
        });
      } else {
        // If no parts data, create default structure (3 parts)
        // First part - Primera conversación
        container.innerHTML += `
          <div class="assignment-item" data-part-index="0">
            <div class="assignment-details">
              <div class="assignment-title">${meeting.first_ministry_part_title || 'Primera conversación'}</div>
              <div class="assignment-person" style="display: flex; gap: 10px;">
                <select class="form-select" id="editFirstMinistryStudent" name="first_ministry_student_id" style="flex: 1;">
                  <option value="">Estudiante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
                <select class="form-select" id="editMinistryStudent_0" name="ministry_student_0_id" style="display: none;">
                  <option value="">Estudiante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
                <select class="form-select" id="editFirstMinistryAssistant" name="first_ministry_assistant_id" style="flex: 1;">
                  <option value="">Ayudante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
                <select class="form-select" id="editMinistryAssistant_0" name="ministry_assistant_0_id" style="display: none;">
                  <option value="">Ayudante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>
          </div>
        `;

        // Second part - Revisita
        container.innerHTML += `
          <div class="assignment-item" data-part-index="1">
            <div class="assignment-details">
              <div class="assignment-title">${meeting.second_ministry_part_title || 'Revisita'}</div>
              <div class="assignment-person" style="display: flex; gap: 10px;">
                <select class="form-select" id="editSecondMinistryStudent" name="second_ministry_student_id" style="flex: 1;">
                  <option value="">Estudiante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
                <select class="form-select" id="editMinistryStudent_1" name="ministry_student_1_id" style="display: none;">
                  <option value="">Estudiante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
                <select class="form-select" id="editSecondMinistryAssistant" name="second_ministry_assistant_id" style="flex: 1;">
                  <option value="">Ayudante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
                <select class="form-select" id="editMinistryAssistant_1" name="ministry_assistant_1_id" style="display: none;">
                  <option value="">Ayudante</option>
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>
          </div>
        `;

        // Third part - Curso bíblico/Discurso
        container.innerHTML += `
          <div class="assignment-item" data-part-index="2">
            <div class="assignment-details">
              <div class="assignment-title">${meeting.ministry_talk_title || 'Curso bíblico'}</div>
              <div class="assignment-person">
                <select class="form-select" id="editMinistrySpeaker" name="ministry_speaker_id">
                  <option value="">Seleccionar orador</option>
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>
          </div>
        `;
      }

      console.log('Generated ministry parts HTML');
    }

    // Function to generate Christian Life parts for the edit modal
    function generateEditChristianLifeParts(meeting) {
      console.log('Generating Christian Life parts for edit modal');
      const container = document.getElementById('editChristianLifePartsContainer');
      if (!container) {
        console.error('Christian Life parts container not found');
        return;
      }

      // Clear the container
      container.innerHTML = '';

      // Check if we have parts data from the meeting
      if (meeting.parts && meeting.parts.christianLife && meeting.parts.christianLife.length > 0) {
        // Filter out section headers and songs
        const christianLifeParts = meeting.parts.christianLife.filter(part =>
          part.title !== 'Nuestra Vida Cristiana' &&
          !part.title.toLowerCase().includes('canción')
        );

        console.log('Filtered Christian Life parts:', christianLifeParts);

        // Generate HTML for each Christian Life part
        christianLifeParts.forEach((part, index) => {
          let html = '';

          // Check if this is the Bible study part (always the last part)
          const isBibleStudy = index === christianLifeParts.length - 1 ||
                              part.title.toLowerCase().includes('estudio bíblico');

          if (isBibleStudy) {
            html = `
              <div class="assignment-item" data-part-index="${index}">
                <div class="assignment-details">
                  <div class="assignment-title">Estudio bíblico de la congregación</div>
                  <div class="assignment-person">
                    <select class="form-select" id="editCongregationBibleStudy" name="congregation_bible_study_id">
                      <option value="">Seleccionar conductor</option>
                      <!-- Will be populated by JavaScript -->
                    </select>
                  </div>
                </div>
              </div>
            `;
          } else {
            html = `
              <div class="assignment-item" data-part-index="${index}">
                <div class="assignment-details">
                  <div class="assignment-title">${part.title || 'Necesidades de la congregación'}</div>
                  <div class="assignment-person">
                    <select class="form-select" id="editChristianLifePartSpeaker" name="christian_life_part_speaker_id">
                      <option value="">Seleccionar presentador</option>
                      <!-- Will be populated by JavaScript -->
                    </select>
                  </div>
                </div>
              </div>
            `;
          }

          container.innerHTML += html;
        });
      } else {
        // If no parts data, create default structure (2 parts)
        // First part - Necesidades de la congregación
        container.innerHTML += `
          <div class="assignment-item" data-part-index="0">
            <div class="assignment-details">
              <div class="assignment-title">${meeting.christian_life_part_title || 'Necesidades de la congregación'}</div>
              <div class="assignment-person">
                <select class="form-select" id="editChristianLifePartSpeaker" name="christian_life_part_speaker_id">
                  <option value="">Seleccionar presentador</option>
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>
          </div>
        `;

        // Second part - Estudio bíblico
        container.innerHTML += `
          <div class="assignment-item" data-part-index="1">
            <div class="assignment-details">
              <div class="assignment-title">Estudio bíblico de la congregación</div>
              <div class="assignment-person">
                <select class="form-select" id="editCongregationBibleStudy" name="congregation_bible_study_id">
                  <option value="">Seleccionar conductor</option>
                  <!-- Will be populated by JavaScript -->
                </select>
              </div>
            </div>
          </div>
        `;
      }

      console.log('Generated Christian Life parts HTML');
    }

    // Function to collect data from dynamically generated ministry parts
    function collectMinistryParts() {
      console.log('Collecting ministry parts data');
      const container = document.getElementById('editMinistryPartsContainer');
      if (!container) {
        console.error('Ministry parts container not found');
        return [];
      }

      // Get all part items
      const partItems = container.querySelectorAll('.assignment-item');
      console.log(`Found ${partItems.length} ministry part items`);

      // Create an array to store the parts
      const parts = [];

      // For each part item, extract the data
      partItems.forEach((item, index) => {
        const titleElement = item.querySelector('.assignment-title');
        const title = titleElement ? titleElement.textContent : '';

        // Create a part object
        const part = {
          title: title,
          index: index,
          section: 'ministry'
        };

        // Check if this is a student part (has student and assistant dropdowns)
        const studentDropdown = item.querySelector('[id^="editMinistryStudent_"]') ||
                               item.querySelector('#editFirstMinistryStudent') ||
                               item.querySelector('#editSecondMinistryStudent');

        const assistantDropdown = item.querySelector('[id^="editMinistryAssistant_"]') ||
                                 item.querySelector('#editFirstMinistryAssistant') ||
                                 item.querySelector('#editSecondMinistryAssistant');

        const speakerDropdown = item.querySelector('#editMinistrySpeaker');

        if (studentDropdown) {
          part.student_id = studentDropdown.value || null;
        }

        if (assistantDropdown) {
          part.assistant_id = assistantDropdown.value || null;
        }

        if (speakerDropdown) {
          part.assignee_id = speakerDropdown.value || null;
        }

        // Add the part to the array
        parts.push(part);
      });

      console.log('Collected ministry parts:', parts);
      return parts;
    }

    // Function to collect data from dynamically generated Christian Life parts
    function collectChristianLifeParts() {
      console.log('Collecting Christian Life parts data');
      const container = document.getElementById('editChristianLifePartsContainer');
      if (!container) {
        console.error('Christian Life parts container not found');
        return [];
      }

      // Get all part items
      const partItems = container.querySelectorAll('.assignment-item');
      console.log(`Found ${partItems.length} Christian Life part items`);

      // Create an array to store the parts
      const parts = [];

      // For each part item, extract the data
      partItems.forEach((item, index) => {
        const titleElement = item.querySelector('.assignment-title');
        const title = titleElement ? titleElement.textContent : '';

        // Create a part object
        const part = {
          title: title,
          index: index,
          section: 'christianLife'
        };

        // Check if this is the Bible study part
        const isBibleStudy = title.toLowerCase().includes('estudio bíblico');

        if (isBibleStudy) {
          const dropdown = item.querySelector('#editCongregationBibleStudy');
          if (dropdown) {
            part.assignee_id = dropdown.value || null;
          }
        } else {
          const dropdown = item.querySelector('#editChristianLifePartSpeaker');
          if (dropdown) {
            part.assignee_id = dropdown.value || null;
          }
        }

        // Add the part to the array
        parts.push(part);
      });

      console.log('Collected Christian Life parts:', parts);
      return parts;
    }

    // Function to update song displays based on input values
    function updateSongDisplays() {
      console.log('Updating song displays');

      // Opening song
      const openingSongNumberDisplay = document.getElementById('openingSongNumberDisplay');
      const openingSongTitleDisplay = document.getElementById('openingSongTitleDisplay');
      const editOpeningSongNumber = document.getElementById('editOpeningSongNumber');
      const editOpeningSongTitle = document.getElementById('editOpeningSongTitle');
      const openingSongDisplay = document.getElementById('openingSongDisplay');

      if (openingSongNumberDisplay && openingSongTitleDisplay && editOpeningSongNumber && editOpeningSongTitle) {
        const number = editOpeningSongNumber.value;
        const title = editOpeningSongTitle.value;
        console.log('Opening song values:', number, title);

        // Always show at least "Canción" even if number is empty
        openingSongNumberDisplay.textContent = number || '';
        openingSongTitleDisplay.textContent = title ? ' - ' + title : '';

        // If both number and title are empty, show a placeholder
        if (!number && !title && openingSongDisplay) {
          openingSongDisplay.textContent = 'Canción (Haga clic para editar)';
        }

        console.log('Updated opening song display:', number, title);
      }

      // Middle song
      const middleSongNumberDisplay = document.getElementById('middleSongNumberDisplay');
      const middleSongTitleDisplay = document.getElementById('middleSongTitleDisplay');
      const editMiddleSongNumber = document.getElementById('editMiddleSongNumber');
      const editMiddleSongTitle = document.getElementById('editMiddleSongTitle');
      const middleSongDisplay = document.getElementById('middleSongDisplay');

      if (middleSongNumberDisplay && middleSongTitleDisplay && editMiddleSongNumber && editMiddleSongTitle) {
        const number = editMiddleSongNumber.value;
        const title = editMiddleSongTitle.value;
        console.log('Middle song values:', number, title);

        // Always show at least "Canción" even if number is empty
        middleSongNumberDisplay.textContent = number || '';
        middleSongTitleDisplay.textContent = title ? ' - ' + title : '';

        // If both number and title are empty, show a placeholder
        if (!number && !title && middleSongDisplay) {
          middleSongDisplay.textContent = 'Canción (Haga clic para editar)';
        }

        console.log('Updated middle song display:', number, title);
      }

      // Closing song
      const closingSongNumberDisplay = document.getElementById('closingSongNumberDisplay');
      const closingSongTitleDisplay = document.getElementById('closingSongTitleDisplay');
      const editClosingSongNumber = document.getElementById('editClosingSongNumber');
      const editClosingSongTitle = document.getElementById('editClosingSongTitle');
      const closingSongDisplay = document.getElementById('closingSongDisplay');

      if (closingSongNumberDisplay && closingSongTitleDisplay && editClosingSongNumber && editClosingSongTitle) {
        const number = editClosingSongNumber.value;
        const title = editClosingSongTitle.value;
        console.log('Closing song values:', number, title);

        // Always show at least "Canción" even if number is empty
        closingSongNumberDisplay.textContent = number || '';
        closingSongTitleDisplay.textContent = title ? ' - ' + title : '';

        // If both number and title are empty, show a placeholder
        if (!number && !title && closingSongDisplay) {
          closingSongDisplay.textContent = 'Canción (Haga clic para editar)';
        }

        console.log('Updated closing song display:', number, title);
      }
    }

    // Load meetings from API
    async function loadMeetings() {
      try {
        // Show loading message
        showLoading('Cargando reuniones...');

        console.log('Loading meetings from API...');

        // Fetch meetings from the API
        meetings = await fetchMeetings();
        console.log('Fetched meetings:', meetings.length);

        // Also fetch members for displaying chairman names
        members = await fetchMembers();
        console.log('Fetched members:', members.length);

        // Hide loading message
        hideLoading();

        // Render meetings
        renderMeetings();
      } catch (error) {
        console.error('Error loading meetings:', error);
        hideLoading();
        showError('Error al cargar las reuniones: ' + error.message);
      }
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
      const addMeetingModal = document.getElementById('addMeetingModal');
      const editMeetingModal = document.getElementById('editMeetingModal');
      const selectWeekModal = document.getElementById('selectWeekModal');

      if (event.target === addMeetingModal) {
        closeAddMeetingModal();
      } else if (event.target === editMeetingModal) {
        closeEditMeetingModal();
      } else if (event.target === selectWeekModal) {
        closeSelectWeekModal();
      }
    });
  </script>
</body>
</html>