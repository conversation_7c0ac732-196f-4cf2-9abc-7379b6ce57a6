# Coral Oeste App - Database Hooks and Triggers Analysis

## Overview

This document analyzes the database hooks, triggers, and automated processes in the Coral Oeste App database system.

## Current Database Automation

### 1. Timestamp Automation

#### Auto-Generated Timestamps
All tables include automatic timestamp management:

```sql
-- Standard timestamp columns in all tables
created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
```

**Tables with Timestamp Automation (43 tables):**
- congregations, members, roles, permissions
- midweek_meetings, midweek_meeting_parts
- tasks, task_assignments, events
- letters, songs, and all other core tables

### 2. Auto-Increment Primary Keys

All tables use auto-increment primary keys for unique identification:

```sql
-- Standard primary key pattern
id INT AUTO_INCREMENT PRIMARY KEY
```

### 3. Default Value Automation

#### Boolean Defaults
```sql
-- Common boolean defaults
is_active BOOLEAN DEFAULT TRUE
is_visible BOOLEAN DEFAULT TRUE
```

#### Status Defaults
```sql
-- Meeting location defaults
meeting_location ENUM('salon', 'zoom') DEFAULT 'salon'
```

## Application-Level Hooks

### 1. Authentication Hooks

#### Login Process Hooks
```javascript
// Pre-login validation
- Validate congregation_id format
- Check congregation exists
- Verify member credentials

// Post-login hooks
- Update last_login timestamp
- Generate JWT token
- Log authentication event
```

#### Session Management
```javascript
// Token validation hooks
- Verify JWT signature
- Check token expiration
- Validate user permissions
- Refresh token if needed
```

### 2. Meeting Management Hooks

#### Meeting Creation Hooks
```javascript
// Pre-creation validation
- Validate meeting date
- Check for conflicts
- Verify chairman availability

// Post-creation hooks
- Create default meeting parts
- Assign default songs
- Send notifications
```

#### Part Assignment Hooks
```javascript
// Assignment validation
- Check member role compatibility
- Verify availability
- Validate part requirements

// Assignment completion
- Update assignment status
- Log assignment changes
- Notify assigned members
```

### 3. Task Management Hooks

#### Task Assignment Hooks
```javascript
// Pre-assignment validation
- Check service group availability
- Validate assignment date
- Verify member eligibility

// Post-assignment hooks
- Update assignment status
- Send assignment notifications
- Log assignment history
```

### 4. File Management Hooks

#### Letter Upload Hooks
```javascript
// Pre-upload validation
- Validate file type (PDF only)
- Check file size limits
- Verify user permissions

// Post-upload hooks
- Generate unique filename
- Create database record
- Set file permissions
- Log upload activity
```

## Database Constraints as Hooks

### 1. Foreign Key Constraints

#### Referential Integrity Hooks
```sql
-- Automatic validation on INSERT/UPDATE
FOREIGN KEY (congregation_id) REFERENCES congregations(id)
FOREIGN KEY (member_id) REFERENCES members(id) ON DELETE SET NULL
FOREIGN KEY (meeting_id) REFERENCES midweek_meetings(id) ON DELETE CASCADE
```

#### Cascade Actions
```sql
-- Automatic cleanup on DELETE
ON DELETE CASCADE  -- Delete related records
ON DELETE SET NULL -- Clear references
ON DELETE RESTRICT -- Prevent deletion
```

### 2. Unique Constraints

#### Duplicate Prevention
```sql
-- Automatic duplicate checking
UNIQUE KEY unique_setting (congregation_id, setting_key)
UNIQUE KEY congregation_id (congregation_id)
UNIQUE KEY filename (filename)
```

### 3. Check Constraints

#### Data Validation
```sql
-- Automatic value validation
CHECK (meeting_location IN ('salon', 'zoom'))
CHECK (is_active IN (0, 1))
CHECK (created_at <= updated_at)
```

## Potential Database Triggers

### 1. Audit Trail Triggers

#### Recommended Implementation
```sql
-- Track changes to sensitive tables
CREATE TRIGGER audit_member_changes
AFTER UPDATE ON members
FOR EACH ROW
BEGIN
    INSERT INTO audit_log (
        table_name, 
        record_id, 
        old_values, 
        new_values, 
        changed_by, 
        changed_at
    ) VALUES (
        'members', 
        NEW.id, 
        JSON_OBJECT('role_id', OLD.role_id, 'is_active', OLD.is_active),
        JSON_OBJECT('role_id', NEW.role_id, 'is_active', NEW.is_active),
        USER(), 
        NOW()
    );
END;
```

### 2. Business Logic Triggers

#### Meeting Validation Trigger
```sql
-- Prevent scheduling conflicts
CREATE TRIGGER validate_meeting_schedule
BEFORE INSERT ON midweek_meetings
FOR EACH ROW
BEGIN
    DECLARE conflict_count INT;
    
    SELECT COUNT(*) INTO conflict_count
    FROM midweek_meetings
    WHERE meeting_date = NEW.meeting_date
    AND meeting_location = NEW.meeting_location
    AND id != NEW.id;
    
    IF conflict_count > 0 THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Meeting conflict detected';
    END IF;
END;
```

#### Permission Validation Trigger
```sql
-- Validate elder permissions
CREATE TRIGGER validate_elder_permissions
BEFORE INSERT ON elder_permissions
FOR EACH ROW
BEGIN
    DECLARE member_role VARCHAR(50);
    
    SELECT r.name INTO member_role
    FROM members m
    JOIN roles r ON m.role_id = r.id
    WHERE m.id = NEW.elder_id;
    
    IF member_role NOT IN ('elder', 'overseer_coordinator') THEN
        SIGNAL SQLSTATE '45000' 
        SET MESSAGE_TEXT = 'Only elders can receive elder permissions';
    END IF;
END;
```

### 3. Data Cleanup Triggers

#### Orphaned Record Cleanup
```sql
-- Clean up orphaned meeting parts
CREATE TRIGGER cleanup_orphaned_parts
AFTER DELETE ON midweek_meetings
FOR EACH ROW
BEGIN
    DELETE FROM midweek_meeting_parts 
    WHERE meeting_id = OLD.id;
    
    DELETE FROM midweek_meeting_songs 
    WHERE meeting_id = OLD.id;
END;
```

## Event Hooks in Application

### 1. Real-time Updates

#### WebSocket Events
```javascript
// Meeting updates
socket.emit('meeting_updated', {
    meetingId: meeting.id,
    changes: updatedFields,
    updatedBy: user.id
});

// Assignment notifications
socket.emit('assignment_created', {
    assignmentId: assignment.id,
    memberId: member.id,
    taskId: task.id
});
```

### 2. Email Notifications

#### Automated Notifications
```javascript
// Meeting assignment notifications
async function notifyMeetingAssignment(assignment) {
    const member = await getMember(assignment.member_id);
    const meeting = await getMeeting(assignment.meeting_id);
    
    await sendEmail({
        to: member.email,
        subject: 'New Meeting Assignment',
        template: 'meeting_assignment',
        data: { member, meeting, assignment }
    });
}
```

### 3. Backup Hooks

#### Automated Backup Triggers
```javascript
// Daily backup hook
cron.schedule('0 2 * * *', async () => {
    try {
        await createDatabaseBackup();
        await cleanupOldBackups();
        console.log('Daily backup completed');
    } catch (error) {
        console.error('Backup failed:', error);
        await notifyAdministrators(error);
    }
});
```

## Performance Monitoring Hooks

### 1. Query Performance

#### Slow Query Detection
```javascript
// Monitor slow queries
db.on('query', (query, duration) => {
    if (duration > 1000) { // 1 second threshold
        console.warn('Slow query detected:', {
            query: query.sql,
            duration: duration,
            timestamp: new Date()
        });
    }
});
```

### 2. Connection Monitoring

#### Connection Pool Monitoring
```javascript
// Monitor connection pool health
pool.on('connection', (connection) => {
    console.log('New connection established:', connection.threadId);
});

pool.on('error', (error) => {
    console.error('Database connection error:', error);
    // Implement reconnection logic
});
```

## Security Hooks

### 1. Access Control

#### Permission Validation
```javascript
// Validate user permissions before database operations
async function validatePermission(userId, action, resource) {
    const permissions = await getUserPermissions(userId);
    
    if (!permissions.includes(`${action}_${resource}`)) {
        throw new Error('Insufficient permissions');
    }
}
```

### 2. Data Sanitization

#### Input Sanitization Hooks
```javascript
// Sanitize input before database operations
function sanitizeInput(data) {
    return {
        ...data,
        name: validator.escape(data.name),
        email: validator.normalizeEmail(data.email),
        phone: validator.escape(data.phone)
    };
}
```

## Recommendations

### 1. Implement Missing Triggers

#### High Priority
1. **Audit Trail Triggers** - Track sensitive data changes
2. **Meeting Conflict Prevention** - Prevent scheduling conflicts
3. **Permission Validation** - Ensure role-based access

#### Medium Priority
1. **Data Cleanup Triggers** - Automatic orphaned record cleanup
2. **Notification Triggers** - Automated member notifications
3. **Backup Validation** - Verify backup integrity

### 2. Performance Optimization

#### Database Level
1. **Index Optimization** - Add missing indexes for frequent queries
2. **Query Optimization** - Optimize slow JOIN operations
3. **Connection Pooling** - Implement proper connection management

#### Application Level
1. **Caching Layer** - Implement Redis for frequent queries
2. **Batch Operations** - Optimize bulk data operations
3. **Async Processing** - Move heavy operations to background jobs

### 3. Monitoring and Alerting

#### Implementation
1. **Query Performance Monitoring** - Track slow queries
2. **Error Rate Monitoring** - Monitor database errors
3. **Capacity Planning** - Track database growth
4. **Security Monitoring** - Monitor unauthorized access attempts

## Conclusion

The Coral Oeste App database currently relies primarily on application-level hooks and basic database constraints. Implementing additional database triggers and monitoring hooks would improve data integrity, performance, and security. The recommended triggers and hooks should be implemented gradually, starting with audit trails and conflict prevention mechanisms.
