require('dotenv').config();
const express = require('express');
const cors = require('cors');
const mysql = require('mysql2/promise');
const path = require('path');
const fs = require('fs');
const jwt = require('jsonwebtoken');

// File upload configuration is now in the letters route

// Create Express app
const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  console.log('Request headers:', req.headers);

  if (req.method === 'POST' && req.url === '/api/auth/login') {
    console.log('Login request body:', req.body);
  }

  if (req.url.includes('/api/songs')) {
    console.log('Song API request detected:', req.method, req.url);
    console.log('Request body:', req.body);
  }

  next();
});

// We'll add error handling middleware after all routes

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Database connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Log database connection details (without password)
console.log('Database connection pool created with:');
console.log(`- Host: ${process.env.DB_HOST}`);
console.log(`- User: ${process.env.DB_USER}`);
console.log(`- Database: ${process.env.DB_NAME}`);

// Serve index.html for the root route
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Configuration endpoint for frontend
app.get('/api/config', (req, res) => {
  res.json({
    success: true,
    config: {
      port: process.env.PORT || 8000,
      apiBaseUrl: `http://localhost:${process.env.PORT || 8000}`,
      environment: process.env.NODE_ENV || 'development'
    }
  });
});

// Test endpoint for elder members
app.get('/api/elder-members-test', async (req, res) => {
  try {
    // Get elder and ministerial servant members
    const query = `
      SELECT m.id, m.name, r.name as role
      FROM members m
      JOIN roles r ON m.role_id = r.id
      WHERE r.name IN ('elder', 'ministerial_servant')
      AND m.is_active = 1
      ORDER BY m.name
    `;

    const [members] = await pool.execute(query);

    res.json({
      success: true,
      members
    });
  } catch (error) {
    console.error('Error getting elder and ministerial servant members:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get elder and ministerial servant members',
      error: error.message
    });
  }
});

// Test endpoint for database backups
app.get('/api/database-backups-test', (req, res) => {
  try {
    const backupDir = path.join(__dirname, 'public/backups');

    // Create backups directory if it doesn't exist
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
      return res.json({
        success: true,
        backups: []
      });
    }

    // Get list of backup files
    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.sql'))
      .map(file => {
        const stats = fs.statSync(path.join(backupDir, file));
        return {
          name: file,
          size: stats.size,
          date: stats.mtime
        };
      })
      .sort((a, b) => b.date - a.date); // Sort by date, newest first

    res.json({
      success: true,
      backups: backupFiles
    });
  } catch (error) {
    console.error('Error getting backup list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get backup list',
      error: error.message
    });
  }
});

// Direct API endpoint for database backups
app.get('/api/database/backups', (req, res) => {
  try {
    const backupDir = path.join(__dirname, 'public/backups');

    // Create backups directory if it doesn't exist
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
      return res.json({
        success: true,
        backups: []
      });
    }

    // Get list of backup files
    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.endsWith('.sql'))
      .map(file => {
        const stats = fs.statSync(path.join(backupDir, file));
        return {
          name: file,
          size: stats.size,
          date: stats.mtime
        };
      })
      .sort((a, b) => b.date - a.date); // Sort by date, newest first

    res.json({
      success: true,
      backups: backupFiles
    });
  } catch (error) {
    console.error('Error getting backup list:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get backup list',
      error: error.message
    });
  }
});

// Database restore endpoint is now handled in the database routes with proper authentication

// Direct API endpoint for elder members
app.get('/api/elder-members', async (req, res) => {
  try {
    console.log('GET /api/elder-members');

    // Get elder and ministerial servant members
    const query = `
      SELECT m.id, m.name, r.name as role
      FROM members m
      JOIN roles r ON m.role_id = r.id
      WHERE r.name IN ('elder', 'ministerial_servant')
      AND m.is_active = 1
      ORDER BY m.name
    `;

    console.log('Query:', query);

    const [members] = await pool.execute(query);
    console.log(`Found ${members.length} members`);

    res.json({
      success: true,
      members
    });
  } catch (error) {
    console.error('Error getting elder and ministerial servant members:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get elder and ministerial servant members',
      error: error.message
    });
  }
});

// API route to test database connection
app.get('/api/db-test', async (req, res) => {
  try {
    const [rows] = await pool.execute('SELECT 1 as test');

    // Get table counts
    const [tables] = await pool.execute('SHOW TABLES');
    const tableCounts = {};

    for (const table of Object.values(tables)) {
      const tableName = Object.values(table)[0];
      // Escape table name with backticks to handle reserved keywords like 'groups'
      const [result] = await pool.execute(`SELECT COUNT(*) as count FROM \`${tableName}\``);
      tableCounts[tableName] = result[0].count;
    }

    res.json({
      success: true,
      message: 'Database connection successful',
      test: rows[0].test,
      tables: tables.length,
      tableCounts
    });
  } catch (error) {
    console.error('Database connection test error:', error);
    res.status(500).json({
      success: false,
      message: 'Database connection failed',
      error: error.message
    });
  }
});

// Log all requests
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  console.log('Headers:', req.headers);
  console.log('Body:', req.body);
  next();
});

// Direct login endpoint for congregation credentials
app.post('/api/direct-login', async (req, res) => {
  console.log('Direct login endpoint called');
  console.log('Request body:', req.body);
  try {
    const { congregationId, pin } = req.body;
    // Use pin parameter as password for backward compatibility
    const password = pin;

    console.log('Direct login attempt with:', { congregationId, password });

    // Check if congregation exists
    const [congregations] = await pool.execute(
      'SELECT * FROM congregations WHERE congregation_id = ?',
      [congregationId]
    );

    console.log('Found congregations:', congregations);

    if (congregations.length === 0) {
      console.log('Congregation not found:', congregationId);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    const congregation = congregations[0];

    // Get member by password
    const [members] = await pool.execute(
      'SELECT m.*, r.name as role FROM members m JOIN roles r ON m.role_id = r.id WHERE m.password = ?',
      [password]
    );

    console.log('Found members:', members);

    if (members.length === 0) {
      console.log('Member not found for password:', password);
      return res.status(401).json({
        success: false,
        message: 'Invalid credentials'
      });
    }

    const member = members[0];

    // Check if member is active
    if (!member.is_active) {
      return res.status(401).json({
        success: false,
        message: 'Account is inactive'
      });
    }

    // Get role permissions
    const [rolePermissions] = await pool.execute(`
      SELECT p.name
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN roles r ON rp.role_id = r.id
      WHERE r.id = ?
    `, [member.role_id]);

    // Get user-specific permissions
    const [userPermissions] = await pool.execute(`
      SELECT p.name
      FROM permissions p
      JOIN user_permissions up ON p.id = up.permission_id
      WHERE up.user_id = ?
    `, [member.id]);

    // Combine permissions
    const permissions = {};
    [...rolePermissions, ...userPermissions].forEach(p => {
      permissions[p.name] = true;
    });

    // Create JWT token
    const token = jwt.sign(
      {
        id: member.id,
        name: member.name,
        role: member.role,
        congregation_id: member.congregation_id
      },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
    );

    // Update last login time
    await pool.execute(
      'UPDATE members SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
      [member.id]
    );

    // Return token and user data
    res.json({
      success: true,
      token,
      user: {
        id: member.id,
        name: member.name,
        role: member.role,
        congregation: congregation.name
      },
      permissions
    });
  } catch (error) {
    console.error('Direct login error:', error);
    res.status(500).json({
      success: false,
      message: 'An error occurred during login',
      error: error.message
    });
  }
});

// API route to verify congregation credentials
app.post('/api/congregations/verify', async (req, res) => {
  try {
    const { region, congregationId, congregationPin } = req.body;

    console.log('Congregation verification request:', { region, congregationId, congregationPin });

    if (!region || !congregationId || !congregationPin) {
      return res.status(400).json({
        success: false,
        message: 'Region, Congregation ID, and Congregation PIN are required'
      });
    }

    // First, check if the congregations table exists and has data
    const [tableCheck] = await pool.execute('SHOW TABLES LIKE "congregations"');
    if (tableCheck.length === 0) {
      console.error('Congregations table does not exist');
      return res.status(500).json({
        success: false,
        message: 'Database setup issue: Congregations table does not exist'
      });
    }

    // Check if there's any data in the congregations table
    const [countCheck] = await pool.execute('SELECT COUNT(*) as count FROM congregations');
    console.log('Congregations count:', countCheck[0].count);

    if (countCheck[0].count === 0) {
      console.error('No congregations found in database');

      // Insert the default congregation if none exists
      await pool.execute(
        'INSERT INTO congregations (name, region, congregation_id, congregation_pin) VALUES (?, ?, ?, ?)',
        ['Coral Oeste', 'North America', '1441', '1930']
      );
      console.log('Default congregation inserted');
    } else {
      // Log existing congregations but don't override their values
      console.log('Congregations already exist in the database');
    }

    // List all congregations for debugging
    const [allCongregations] = await pool.execute('SELECT * FROM congregations');
    console.log('All congregations:', allCongregations);

    // Query the database to verify congregation credentials
    const [rows] = await pool.execute(
      'SELECT * FROM congregations WHERE region = ? AND congregation_id = ? AND congregation_pin = ?',
      [region, congregationId, congregationPin]
    );

    console.log('Query results:', rows);

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid congregation credentials'
      });
    }

    // Return congregation information
    const congregation = rows[0];
    res.json({
      success: true,
      message: 'Congregation verified successfully',
      congregation: {
        id: congregation.id,
        name: congregation.name,
        region: congregation.region
      }
    });
  } catch (error) {
    console.error('Congregation verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify congregation',
      error: error.message
    });
  }
});

// API endpoints for letters are now handled by the letters route

// Direct API endpoint for letters with actual PDF files
app.get('/api/letters-direct', (req, res) => {
  try {
    console.log('GET /api/letters-direct request received');

    // Read files from the uploads/letters directory
    const uploadDir = path.join(__dirname, 'public/uploads/letters');
    console.log(`Reading files from directory: ${uploadDir}`);

    // Read all files in the letters directory
    const files = fs.readdirSync(uploadDir);
    console.log(`Found ${files.length} files in directory:`, files);

    // Filter for PDF files only
    const pdfFiles = files.filter(file => file.toLowerCase().endsWith('.pdf'));
    console.log(`Found ${pdfFiles.length} PDF files:`, pdfFiles);

    // Create letters array from PDF files
    const letters = pdfFiles.map(filename => {
      // Get file stats to get the creation date
      const filePath = path.join(uploadDir, filename);
      const stats = fs.statSync(filePath);
      const fileDate = stats.mtime || stats.ctime || stats.birthtime || new Date();

      // Format the date as YYYY-MM-DD
      const formattedDate = fileDate.toISOString().split('T')[0];

      // Extract title from filename as a fallback
      const titleFromFilename = filename.split('.')[0].replace(/_/g, ' ').replace(/-/g, ' ');

      // Create a letter object
      return {
        filename,
        path: `/uploads/letters/${filename}`,
        url: `${req.protocol}://${req.get('host')}/uploads/letters/${filename}`,
        originalTitle: titleFromFilename,
        date: formattedDate,
        category: 'General',
        visibility: 'All Members'
      };
    });

    console.log(`Sending response with ${letters.length} letters from directory`);
    res.json({
      success: true,
      count: letters.length,
      letters
    });
  } catch (error) {
    console.error('Error getting letters from direct endpoint:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({
      success: false,
      message: 'Failed to get letters',
      error: error.message
    });
  }
});

// Import routes
const membersRoutes = require('./server/routes/members')(pool);
const memberAuthRoutes = require('./server/routes/memberAuth')(pool);
const databaseRoutes = require('./server/routes/database')(pool);
const rolesRoutes = require('./server/routes/roles')(pool);
const membersByRoleRoutes = require('./server/routes/membersByRole')(pool);
const debugRoutes = require('./server/routes/debug')(pool);

const directMembersByRoleRoutes = require('./server/routes/directMembersByRole')(pool);
const elderMembersRoutes = require('./server/routes/elderMembers')(pool);
const lettersRoutes = require('./server/routes/letters');
const eventsRoutes = require('./server/routes/events');
const midweekRoutes = require('./server/routes/midweek');
const midweekWolRoutes = require('./server/routes/midweekWolRoutes');
const midweekWeeksRoutes = require('./server/routes/midweekWeeks');
const midweekMeetingsRoutes = require('./server/routes/midweekMeetings');
const songRoutes = require('./server/routes/song-routes');
// const jwScraperRoutes = require('./server/routes/jw-scraper'); // Removed

// Task-related routes
const tasksRoutes = require('./server/routes/tasks-new')(pool);
const taskCategoriesRoutes = require('./server/routes/taskCategories')(pool);
const taskAssignmentsRoutes = require('./server/routes/taskAssignments')(pool);
const taskMultiAssignmentsRoutes = require('./server/routes/taskMultiAssignments')(pool);
const serviceGroupsRoutes = require('./server/routes/serviceGroups')(pool);
const frontendTasksRoutes = require('./server/routes/frontendTasks')(pool);
// Use routes
app.use('/api/members', membersRoutes);
app.use('/api/auth', memberAuthRoutes);
console.log('Registered auth routes:');
memberAuthRoutes.stack.forEach(r => {
  if (r.route && r.route.path) {
    console.log(`${Object.keys(r.route.methods).join(', ').toUpperCase()} /api/auth${r.route.path}`);
  }
});
// Import authentication middleware
const authMiddleware = require('./server/middleware/auth');

// Apply authentication middleware to database routes
// Only elders and above can access database management functions
app.use('/api/database', authMiddleware.authenticate, authMiddleware.isElder, databaseRoutes);
app.use('/api/roles', rolesRoutes);
app.use('/api/members-by-role', membersByRoleRoutes);
app.use('/api/debug', debugRoutes);

app.use('/api/direct-members-by-role', directMembersByRoleRoutes);
app.use('/api/elder-members', elderMembersRoutes);
app.use('/api/letters', lettersRoutes);
app.use('/api/events', eventsRoutes);
app.use('/api/midweek', midweekRoutes);
app.use('/api/midweek/wol', midweekWolRoutes);
app.use('/api/midweek/weeks', midweekWeeksRoutes);
// Register new midweek meetings API
app.use('/api/midweek-meetings', midweekMeetingsRoutes);
// Register song routes
app.use('/api/songs', songRoutes);
console.log('Registered song routes:');
songRoutes.stack.forEach(r => {
  if (r.route && r.route.path) {
    console.log(`${Object.keys(r.route.methods).join(', ').toUpperCase()} /api/songs${r.route.path}`);
  }
});
// app.use('/api/jw-scraper', jwScraperRoutes); // Removed

// Register task-related routes
// Note: Order matters! More specific routes should come first
app.use('/api/tasks/categories', taskCategoriesRoutes);
app.use('/api/tasks/assignments', taskAssignmentsRoutes);
app.use('/api/service-groups', serviceGroupsRoutes);

// Task settings routes
const taskSettingsRoutes = require('./server/routes/taskSettings')(pool);
app.use('/api/tasks/settings', taskSettingsRoutes);

// Frontend tasks route
app.use('/api/frontend-tasks', frontendTasksRoutes);

// Register the main tasks routes last so it doesn't override the more specific routes
app.use('/api/tasks', tasksRoutes);
app.use('/api', taskMultiAssignmentsRoutes);

console.log('Registered letters routes:');
lettersRoutes.stack.forEach(r => {
  if (r.route && r.route.path) {
    console.log(`${Object.keys(r.route.methods).join(', ').toUpperCase()} /api/letters${r.route.path}`);
  }
});

console.log('Registered events routes:');
eventsRoutes.stack.forEach(r => {
  if (r.route && r.route.path) {
    console.log(`${Object.keys(r.route.methods).join(', ').toUpperCase()} /api/events${r.route.path}`);
  }
});

console.log('Registered midweek routes:');
midweekRoutes.stack.forEach(r => {
  if (r.route && r.route.path) {
    console.log(`${Object.keys(r.route.methods).join(', ').toUpperCase()} /api/midweek${r.route.path}`);
  }
});

console.log('Registered midweek WOL routes:');
midweekWolRoutes.stack.forEach(r => {
  if (r.route && r.route.path) {
    console.log(`${Object.keys(r.route.methods).join(', ').toUpperCase()} /api/midweek/wol${r.route.path}`);
  }
});
// Root route is handled by express.static middleware

// Set port
const PORT = process.env.PORT || 5000;

// Initialize database and start server
async function startServer() {
  try {
    // Initialize the database first
    console.log('Initializing database...');

    // Database initialization temporarily disabled
    console.log('Database initialization skipped - using existing database');

    /* Original initialization code:
    try {
      await require('./server/database/init')();
      console.log('Database initialization completed');
    } catch (dbError) {
      console.error('Database initialization error:', dbError);
      console.log('Continuing with server startup despite database error');
    }
    */

    // Start server
    app.listen(PORT, () => {
      console.log(`Server running on port ${PORT}`);
      console.log(`API available at http://localhost:${PORT}`);
      console.log(`Test database connection at http://localhost:${PORT}/api/db-test`);
      console.log(`Web interface available at http://localhost:${PORT}`);

      // Log all registered routes
      console.log('\nRegistered Routes:');

      console.log('\nManually registered routes:');
      console.log('GET /');
      console.log('POST /api/letters/upload');
      console.log('DELETE /api/letters/:filename');
      console.log('GET /api/elder-members-test');
      console.log('GET /api/database-backups-test');
      console.log('GET /api/database/backups');
      console.log('GET /api/elder-members');
      console.log('GET /api/db-test');
      console.log('GET /api/letters-direct');
      console.log('POST /api/direct-login');
      console.log('/api/members');
      console.log('/api/auth');
      console.log('/api/database');
      console.log('/api/roles');
      console.log('/api/members-by-role');
      console.log('/api/debug');
      console.log('/api/direct-members-by-role');
      console.log('/api/elder-members');
      console.log('/api/events');
      console.log('/api/midweek');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Add error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: err.message
  });
});

// Start the server
startServer();
