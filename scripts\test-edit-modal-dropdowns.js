// Test script to check edit modal dropdown functionality
require('dotenv').config();
const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

async function testEditModalDropdowns() {
  try {
    console.log('Testing edit modal dropdown functionality...\n');
    
    const connection = await mysql.createConnection(dbConfig);
    
    // Test 1: Check if we have brothers available for task assignments
    console.log('1. Checking available brothers for task assignments...');
    const [members] = await connection.execute(`
      SELECT id, name, role_id 
      FROM members 
      WHERE role_id IN (1, 2, 3)
      ORDER BY name
    `);
    
    console.log(`   Found ${members.length} brothers available for task assignments:`);
    members.forEach(member => {
      const roleNames = {1: 'overseer_coordinator', 2: 'elder', 3: 'ministerial_servant'};
      console.log(`   - ID: ${member.id}, Name: ${member.name}, Role: ${roleNames[member.role_id]}`);
    });
    
    // Test 2: Check a specific meeting to see if it has task assignments
    console.log('\n2. Checking a specific meeting for task assignments...');
    const [meetings] = await connection.execute(`
      SELECT 
        id, 
        meeting_date,
        audio_video_id,
        platform_id,
        microphone1_id,
        microphone2_id,
        cleaning_group
      FROM midweek_meetings 
      WHERE id = 270
    `);
    
    if (meetings.length > 0) {
      const meeting = meetings[0];
      console.log(`   Meeting ${meeting.id} (${meeting.meeting_date}):`);
      console.log(`     - Audio/Video ID: ${meeting.audio_video_id || 'NULL'}`);
      console.log(`     - Platform ID: ${meeting.platform_id || 'NULL'}`);
      console.log(`     - Microphone 1 ID: ${meeting.microphone1_id || 'NULL'}`);
      console.log(`     - Microphone 2 ID: ${meeting.microphone2_id || 'NULL'}`);
      console.log(`     - Cleaning Group: ${meeting.cleaning_group || 'NULL'}`);
      
      // If there are assignments, get the member names
      if (meeting.audio_video_id || meeting.platform_id || meeting.microphone1_id || meeting.microphone2_id) {
        console.log('\n   Assigned member names:');
        
        if (meeting.audio_video_id) {
          const [audioMember] = await connection.execute('SELECT name FROM members WHERE id = ?', [meeting.audio_video_id]);
          if (audioMember.length > 0) {
            console.log(`     - Audio/Video: ${audioMember[0].name}`);
          }
        }
        
        if (meeting.platform_id) {
          const [platformMember] = await connection.execute('SELECT name FROM members WHERE id = ?', [meeting.platform_id]);
          if (platformMember.length > 0) {
            console.log(`     - Platform: ${platformMember[0].name}`);
          }
        }
        
        if (meeting.microphone1_id) {
          const [mic1Member] = await connection.execute('SELECT name FROM members WHERE id = ?', [meeting.microphone1_id]);
          if (mic1Member.length > 0) {
            console.log(`     - Microphone 1: ${mic1Member[0].name}`);
          }
        }
        
        if (meeting.microphone2_id) {
          const [mic2Member] = await connection.execute('SELECT name FROM members WHERE id = ?', [meeting.microphone2_id]);
          if (mic2Member.length > 0) {
            console.log(`     - Microphone 2: ${mic2Member[0].name}`);
          }
        }
      }
    } else {
      console.log('   Meeting 270 not found');
    }
    
    // Test 3: Test updating a meeting with task assignments
    console.log('\n3. Testing task assignment update...');
    if (members.length > 0) {
      const testMember = members[0];
      console.log(`   Using test member: ${testMember.name} (ID: ${testMember.id})`);
      
      // Update meeting 270 with task assignments
      await connection.execute(`
        UPDATE midweek_meetings 
        SET 
          audio_video_id = ?,
          platform_id = ?,
          microphone1_id = ?,
          microphone2_id = ?,
          cleaning_group = ?
        WHERE id = 270
      `, [
        testMember.id,
        testMember.id,
        testMember.id,
        testMember.id,
        '1'
      ]);
      
      // Verify the update
      const [updatedMeeting] = await connection.execute(`
        SELECT 
          audio_video_id,
          platform_id,
          microphone1_id,
          microphone2_id,
          cleaning_group
        FROM midweek_meetings 
        WHERE id = 270
      `);
      
      if (updatedMeeting.length > 0) {
        const meeting = updatedMeeting[0];
        console.log(`   ✅ Update successful:`);
        console.log(`     - Audio/Video ID: ${meeting.audio_video_id}`);
        console.log(`     - Platform ID: ${meeting.platform_id}`);
        console.log(`     - Microphone 1 ID: ${meeting.microphone1_id}`);
        console.log(`     - Microphone 2 ID: ${meeting.microphone2_id}`);
        console.log(`     - Cleaning Group: ${meeting.cleaning_group}`);
      }
    }
    
    // Test 4: Check if the API endpoint is working
    console.log('\n4. Testing API endpoint...');
    try {
      const fetch = require('node-fetch');
      const response = await fetch('http://localhost:8000/api/members', {
        headers: {
          'Authorization': 'Bearer ' + 'test-token' // This will fail but we can see the response
        }
      });
      
      console.log(`   API Response status: ${response.status}`);
      if (response.status === 401) {
        console.log('   ✅ API is responding (authentication required as expected)');
      } else {
        const data = await response.json();
        console.log(`   API Response data:`, data);
      }
    } catch (error) {
      console.log(`   ❌ API Error: ${error.message}`);
    }
    
    await connection.end();
    console.log('\n✅ Edit modal dropdown test completed!');
    
  } catch (error) {
    console.error('❌ Error testing edit modal dropdowns:', error.message);
  }
}

testEditModalDropdowns();
