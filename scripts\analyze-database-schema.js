/**
 * Database Schema Analysis Script
 * 
 * This script analyzes the current database schema, relationships, and hooks
 * to provide a comprehensive overview of the Coral Oeste App database structure.
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// Database configuration
const config = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

async function analyzeDatabase() {
  let connection;
  
  try {
    console.log('🔍 Starting Database Schema Analysis...\n');
    
    // Create connection
    connection = await mysql.createConnection(config);
    console.log('✅ Connected to database:', config.database);
    
    // Get all tables
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);
    
    console.log(`\n📊 Found ${tableNames.length} tables:\n`);
    
    const analysis = {
      tables: {},
      relationships: [],
      indexes: [],
      constraints: [],
      summary: {
        totalTables: tableNames.length,
        totalColumns: 0,
        totalIndexes: 0,
        totalConstraints: 0
      }
    };
    
    // Analyze each table
    for (const tableName of tableNames) {
      console.log(`🔍 Analyzing table: ${tableName}`);
      
      // Get table structure
      const [columns] = await connection.execute(`DESCRIBE \`${tableName}\``);

      // Get table indexes
      const [indexes] = await connection.execute(`SHOW INDEX FROM \`${tableName}\``);
      
      // Get table constraints
      const [constraints] = await connection.execute(`
        SELECT
          kcu.CONSTRAINT_NAME,
          tc.CONSTRAINT_TYPE,
          kcu.COLUMN_NAME,
          kcu.REFERENCED_TABLE_NAME,
          kcu.REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE kcu
        JOIN information_schema.TABLE_CONSTRAINTS tc
          ON kcu.CONSTRAINT_NAME = tc.CONSTRAINT_NAME
          AND kcu.TABLE_SCHEMA = tc.TABLE_SCHEMA
        WHERE kcu.TABLE_SCHEMA = ? AND kcu.TABLE_NAME = ?
      `, [config.database, tableName]);
      
      // Get row count
      const [countResult] = await connection.execute(`SELECT COUNT(*) as count FROM \`${tableName}\``);
      const rowCount = countResult[0].count;
      
      analysis.tables[tableName] = {
        columns: columns,
        indexes: indexes,
        constraints: constraints,
        rowCount: rowCount
      };
      
      analysis.summary.totalColumns += columns.length;
      analysis.summary.totalIndexes += indexes.length;
      analysis.summary.totalConstraints += constraints.length;
      
      // Extract foreign key relationships
      constraints.forEach(constraint => {
        if (constraint.CONSTRAINT_TYPE === 'FOREIGN KEY' && constraint.REFERENCED_TABLE_NAME) {
          analysis.relationships.push({
            fromTable: tableName,
            fromColumn: constraint.COLUMN_NAME,
            toTable: constraint.REFERENCED_TABLE_NAME,
            toColumn: constraint.REFERENCED_COLUMN_NAME,
            constraintName: constraint.CONSTRAINT_NAME
          });
        }
      });
    }
    
    console.log('\n✅ Analysis complete!\n');
    
    return analysis;
    
  } catch (error) {
    console.error('❌ Error analyzing database:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

async function generateReport(analysis) {
  console.log('📝 Generating Database Schema Report...\n');
  
  let report = `# Coral Oeste App - Database Schema Analysis Report

Generated on: ${new Date().toISOString()}

## Summary

- **Total Tables**: ${analysis.summary.totalTables}
- **Total Columns**: ${analysis.summary.totalColumns}
- **Total Indexes**: ${analysis.summary.totalIndexes}
- **Total Constraints**: ${analysis.summary.totalConstraints}
- **Total Relationships**: ${analysis.relationships.length}

## Tables Overview

`;

  // Tables overview
  Object.entries(analysis.tables).forEach(([tableName, tableData]) => {
    report += `### ${tableName}
- **Columns**: ${tableData.columns.length}
- **Indexes**: ${tableData.indexes.length}
- **Constraints**: ${tableData.constraints.length}
- **Row Count**: ${tableData.rowCount}

`;
  });

  report += `## Detailed Table Structures

`;

  // Detailed table structures
  Object.entries(analysis.tables).forEach(([tableName, tableData]) => {
    report += `### Table: \`${tableName}\`

**Row Count**: ${tableData.rowCount}

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
`;
    
    tableData.columns.forEach(column => {
      report += `| ${column.Field} | ${column.Type} | ${column.Null} | ${column.Key} | ${column.Default || 'NULL'} | ${column.Extra} |\n`;
    });
    
    if (tableData.indexes.length > 0) {
      report += `\n#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
`;
      
      const uniqueIndexes = {};
      tableData.indexes.forEach(index => {
        const key = `${index.Key_name}-${index.Column_name}`;
        if (!uniqueIndexes[key]) {
          uniqueIndexes[key] = index;
          report += `| ${index.Key_name} | ${index.Column_name} | ${index.Non_unique === 0 ? 'Yes' : 'No'} | ${index.Index_type} |\n`;
        }
      });
    }
    
    if (tableData.constraints.length > 0) {
      report += `\n#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
`;
      
      tableData.constraints.forEach(constraint => {
        const reference = constraint.REFERENCED_TABLE_NAME ? 
          `${constraint.REFERENCED_TABLE_NAME}.${constraint.REFERENCED_COLUMN_NAME}` : 
          '-';
        report += `| ${constraint.CONSTRAINT_NAME} | ${constraint.CONSTRAINT_TYPE} | ${constraint.COLUMN_NAME} | ${reference} |\n`;
      });
    }
    
    report += '\n---\n\n';
  });

  // Relationships section
  if (analysis.relationships.length > 0) {
    report += `## Database Relationships

The following foreign key relationships exist in the database:

| From Table | From Column | To Table | To Column | Constraint Name |
|------------|-------------|----------|-----------|-----------------|
`;
    
    analysis.relationships.forEach(rel => {
      report += `| ${rel.fromTable} | ${rel.fromColumn} | ${rel.toTable} | ${rel.toColumn} | ${rel.constraintName} |\n`;
    });
    
    report += '\n';
  }

  // Entity Relationship Analysis
  report += `## Entity Relationship Analysis

### Core Entities

#### Authentication & Authorization
- **congregations**: Stores congregation information
- **roles**: Defines user roles (publisher, ministerial_servant, elder, etc.)
- **permissions**: Defines system permissions
- **role_permissions**: Maps roles to permissions
- **user_permissions**: Maps individual users to permissions
- **members**: User accounts with role assignments

#### Meeting Management
- **midweek_meetings**: Stores midweek meeting information
- **midweek_meeting_parts**: Individual parts of midweek meetings
- **elder_permissions**: Elder-specific permissions
- **elder_role_permissions**: Role-based permissions for elders

#### Task Management
- **task_categories**: Categories for tasks
- **tasks**: Individual tasks
- **task_assignments**: Assignment of tasks to dates/groups
- **service_groups**: Service group information

#### Event Management
- **event_categories**: Categories for events
- **events**: Congregation events

#### Communication
- **letters**: Letter management system

#### Songs & Worship
- **songs**: Song catalog management
- **custom_songs**: Custom songs added by congregation

### Key Relationships

1. **Members → Roles**: Each member has a role that determines their permissions
2. **Congregations → Members**: Members belong to specific congregations
3. **Meetings → Members**: Meeting parts are assigned to specific members
4. **Tasks → Categories**: Tasks are organized by categories
5. **Events → Categories**: Events are organized by categories
6. **Permissions → Roles**: Roles have specific permissions assigned

### Data Integrity Features

- Foreign key constraints ensure referential integrity
- Cascade deletes where appropriate (e.g., meeting parts when meeting is deleted)
- Set NULL on delete for optional references (e.g., when a member is deleted)
- Unique constraints prevent duplicate data
- Default values ensure consistent data entry

`;

  return report;
}

// Main execution
async function main() {
  try {
    const analysis = await analyzeDatabase();
    const report = await generateReport(analysis);
    
    // Write report to file
    const fs = require('fs');
    const path = require('path');
    
    const reportsDir = path.join(__dirname, '..', 'docs');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    const reportPath = path.join(reportsDir, 'database-schema-analysis.md');
    fs.writeFileSync(reportPath, report);
    
    console.log(`📄 Report saved to: ${reportPath}`);
    console.log('\n🎉 Database analysis complete!');
    
  } catch (error) {
    console.error('❌ Failed to analyze database:', error);
    process.exit(1);
  }
}

// Run the analysis
if (require.main === module) {
  main();
}

module.exports = { analyzeDatabase, generateReport };
