<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
  <meta http-equiv="Pragma" content="no-cache">
  <meta http-equiv="Expires" content="0">
  <title>Administración de Reuniones Entre Semana - Coral Oeste</title>

  <!-- Fonts and Icons -->
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
  <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">

  <!-- Base Styles -->
  <link href="/css/admin-mobile.css" rel="stylesheet">
  <link href="/css/midweek-meeting/base.css" rel="stylesheet">

  <!-- React Dependencies -->
  <script src="https://unpkg.com/react@18/umd/react.production.min.js" crossorigin></script>
  <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js" crossorigin></script>
  <script src="https://unpkg.com/react-query@3/dist/react-query.production.min.js" crossorigin></script>

  <!-- App Styles -->
  <link rel="stylesheet" href="/css/midweek-meeting-react.css">

  <style>
    /* Loading spinner */
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      width: 100%;
    }

    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: var(--primary-color);
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 16px;
      font-size: 16px;
      color: var(--text-secondary);
    }

    /* Debug panel */
    #debug-panel {
      display: none;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 16px;
      font-family: monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
      z-index: 9999;
    }

    .debug-title {
      font-weight: bold;
      margin-bottom: 8px;
    }

    .debug-content {
      white-space: pre-wrap;
    }

    .debug-toggle {
      position: fixed;
      bottom: 16px;
      right: 16px;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10000;
    }
  </style>
</head>
<body>
  <!-- App Header -->
  <header class="app-header">
    <button class="back-button" onclick="window.location.href='/admin'">
      <i class="material-icons">arrow_back</i>
      Volver
    </button>
    <h1 class="app-title">Administración de Reuniones Entre Semana</h1>
  </header>

  <!-- Loading Indicator (shown until React loads) -->
  <div id="initial-loading" class="loading-container">
    <div class="spinner"></div>
    <div class="loading-text">Cargando aplicación...</div>
  </div>

  <!-- React Root -->
  <div id="react-midweek-meetings-root" style="display: none;"></div>

  <!-- Debug Panel -->
  <div id="debug-panel">
    <div class="debug-title">Debug Information</div>
    <div id="debug-content" class="debug-content"></div>
  </div>

  <button id="debug-toggle" class="debug-toggle" style="display: none;">
    <i class="material-icons">bug_report</i>
  </button>

  <!-- App Scripts -->
  <script src="/js/midweek-meeting-react-components.js" onerror="console.error('Failed to load midweek-meeting-react-components.js')"></script>

  <!-- Fallback script in case the main script fails to load -->
  <script>
    // Check if the script loaded correctly
    setTimeout(function() {
      if (typeof window.CoralOeste === 'undefined' || typeof window.CoralOeste.initializeMidweekMeetingApp === 'undefined') {
        console.error('CoralOeste.initializeMidweekMeetingApp is not available after script load');
        document.getElementById('debug-panel').style.display = 'block';
        document.getElementById('debug-toggle').style.display = 'flex';

        // Show error in loading container
        const loadingContainer = document.getElementById('initial-loading');
        if (loadingContainer) {
          loadingContainer.innerHTML = `
            <div style="color: var(--error-color); text-align: center; padding: 20px;">
              <i class="material-icons" style="font-size: 48px;">error</i>
              <h2>Error al cargar la aplicación</h2>
              <p>No se pudo cargar el script de la aplicación. Por favor, intente nuevamente.</p>
              <button onclick="window.location.reload()" class="button" style="margin-top: 20px;">
                Reintentar
              </button>
            </div>
          `;
        }
      }
    }, 1000);
  </script>

  <script>
    // Debug utilities
    function addDebugInfo(message) {
      const debugContent = document.getElementById('debug-content');
      if (debugContent) {
        const timestamp = new Date().toISOString().split('T')[1].split('.')[0];
        debugContent.innerHTML += `[${timestamp}] ${message}\n`;
        debugContent.scrollTop = debugContent.scrollHeight;
      }
    }

    function toggleDebugPanel() {
      const debugPanel = document.getElementById('debug-panel');
      if (debugPanel) {
        debugPanel.style.display = debugPanel.style.display === 'none' ? 'block' : 'none';
      }
    }

    function logGlobalObjects() {
      addDebugInfo('Global objects:');
      addDebugInfo(`- React: ${typeof React}`);
      addDebugInfo(`- ReactDOM: ${typeof ReactDOM}`);
      addDebugInfo(`- ReactQuery: ${typeof ReactQuery}`);
      addDebugInfo(`- CoralOeste: ${typeof window.CoralOeste}`);

      if (window.CoralOeste) {
        addDebugInfo(`- CoralOeste functions: ${Object.keys(window.CoralOeste).join(', ')}`);
      }
    }

    // Initialize debug toggle
    document.getElementById('debug-toggle').addEventListener('click', toggleDebugPanel);

    // Initialize the application when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
      try {
        // Show debug toggle in development
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
          document.getElementById('debug-toggle').style.display = 'flex';
        }

        // Log initialization
        addDebugInfo('DOM loaded, initializing application...');
        logGlobalObjects();

        // Get the root element
        const rootElement = document.getElementById('react-midweek-meetings-root');
        if (!rootElement) {
          throw new Error('React root element not found');
        }

        // Initialize the React app
        if (window.CoralOeste && window.CoralOeste.initializeMidweekMeetingApp) {
          addDebugInfo('Initializing Midweek Meeting App...');

          // Hide loading indicator and show React root
          document.getElementById('initial-loading').style.display = 'none';
          rootElement.style.display = 'block';

          // Initialize the app
          window.CoralOeste.initializeMidweekMeetingApp('react-midweek-meetings-root', {
            showDevtools: window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
          });

          addDebugInfo('Midweek Meeting App initialized successfully');
        } else {
          throw new Error('CoralOeste.initializeMidweekMeetingApp not available');
        }
      } catch (error) {
        // Log error
        addDebugInfo(`Error: ${error.message}`);
        console.error('Error initializing application:', error);

        // Show debug panel
        document.getElementById('debug-panel').style.display = 'block';
        document.getElementById('debug-toggle').style.display = 'flex';

        // Show error in loading container
        const loadingContainer = document.getElementById('initial-loading');
        if (loadingContainer) {
          loadingContainer.innerHTML = `
            <div style="color: var(--error-color); text-align: center; padding: 20px;">
              <i class="material-icons" style="font-size: 48px;">error</i>
              <h2>Error al cargar la aplicación</h2>
              <p>${error.message}</p>
              <button onclick="window.location.reload()" class="button" style="margin-top: 20px;">
                Reintentar
              </button>
            </div>
          `;
        }
      }
    });
  </script>
</body>
</html>
