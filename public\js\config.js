// Configuration utility for frontend applications
// This file provides a single source of truth for API configuration

class AppConfig {
  constructor() {
    this.config = null;
    this.loaded = false;
  }

  // Load configuration from server
  async loadConfig() {
    if (this.loaded) {
      return this.config;
    }

    try {
      const response = await fetch('/api/config');
      if (!response.ok) {
        throw new Error(`Failed to load config: ${response.status}`);
      }
      
      const data = await response.json();
      if (data.success) {
        this.config = data.config;
        this.loaded = true;
        console.log('Configuration loaded:', this.config);
        return this.config;
      } else {
        throw new Error('Invalid config response');
      }
    } catch (error) {
      console.error('Failed to load configuration:', error);
      // Fallback configuration
      this.config = {
        port: 8000,
        apiBaseUrl: window.location.origin,
        environment: 'development'
      };
      this.loaded = true;
      return this.config;
    }
  }

  // Get API base URL
  async getApiBaseUrl() {
    const config = await this.loadConfig();
    return config.apiBaseUrl;
  }

  // Get full API URL for a given endpoint
  async getApiUrl(endpoint) {
    const baseUrl = await this.getApiBaseUrl();
    // Remove leading slash from endpoint if present to avoid double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${baseUrl}/${cleanEndpoint}`;
  }

  // Get port
  async getPort() {
    const config = await this.loadConfig();
    return config.port;
  }

  // Get environment
  async getEnvironment() {
    const config = await this.loadConfig();
    return config.environment;
  }

  // Helper method to make API requests with correct base URL
  async apiRequest(endpoint, options = {}) {
    const url = await this.getApiUrl(endpoint);
    
    // Set default headers
    const defaultHeaders = {
      'Content-Type': 'application/json'
    };

    // Merge headers
    const headers = {
      ...defaultHeaders,
      ...(options.headers || {})
    };

    // Make the request
    const response = await fetch(url, {
      ...options,
      headers
    });

    return response;
  }

  // Helper method for GET requests
  async get(endpoint, headers = {}) {
    return this.apiRequest(endpoint, {
      method: 'GET',
      headers
    });
  }

  // Helper method for POST requests
  async post(endpoint, data = null, headers = {}) {
    const options = {
      method: 'POST',
      headers
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    return this.apiRequest(endpoint, options);
  }

  // Helper method for PUT requests
  async put(endpoint, data = null, headers = {}) {
    const options = {
      method: 'PUT',
      headers
    };

    if (data) {
      options.body = JSON.stringify(data);
    }

    return this.apiRequest(endpoint, options);
  }

  // Helper method for DELETE requests
  async delete(endpoint, headers = {}) {
    return this.apiRequest(endpoint, {
      method: 'DELETE',
      headers
    });
  }
}

// Create global instance
window.appConfig = new AppConfig();

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AppConfig;
}

// Convenience function for quick API URL generation
window.getApiUrl = async function(endpoint) {
  return window.appConfig.getApiUrl(endpoint);
};

// Convenience function for API requests
window.apiRequest = async function(endpoint, options = {}) {
  return window.appConfig.apiRequest(endpoint, options);
};

console.log('Configuration utility loaded. Use window.appConfig for API configuration.');
