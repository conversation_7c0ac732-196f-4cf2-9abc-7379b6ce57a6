/**
 * Authentication Middleware
 *
 * This middleware provides authentication and authorization functions.
 */

const jwt = require('jsonwebtoken');
const db = require('../database/db');
const logger = require('../utils/logger');

/**
 * Authenticate user using JWT token
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
exports.authenticate = async (req, res, next) => {
  try {
    // Get token from authorization header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        message: 'No token provided'
      });
    }

    // Extract token
    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');

    // Check if user exists
    const [members] = await db.query(
      'SELECT id, name, email, congregation_id FROM members WHERE id = ?',
      [decoded.id]
    );

    if (members.length === 0) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    // Set user in request object
    req.user = members[0];

    // Continue
    next();
  } catch (error) {
    logger.error('Authentication error:', error);

    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }

    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Authentication error',
      error: error.message
    });
  }
};

/**
 * Check if user has a specific permission
 * @param {string} permission - Permission to check
 * @returns {Function} Middleware function
 */
exports.hasPermission = (permission) => {
  return async (req, res, next) => {
    try {
      // Get user from request object
      const { id, role, congregation_id } = req.user;

      // If user is an admin, allow access
      if (role === 'admin') {
        return next();
      }

      // Check if user has the required permission
      const [permissions] = await db.query(
        `SELECT * FROM elder_permissions
         WHERE user_id = ? AND congregation_id = ? AND permission = ?`,
        [id, congregation_id, permission]
      );

      // If user has the permission, allow access
      if (permissions.length > 0) {
        return next();
      }

      // If user is an elder, check if they have the permission by role
      if (role === 'elder') {
        const [elderPermissions] = await db.query(
          `SELECT * FROM elder_role_permissions
           WHERE role = ? AND permission = ?`,
          ['elder', permission]
        );

        if (elderPermissions.length > 0) {
          return next();
        }
      }

      // If user is a ministerial servant, check if they have the permission by role
      if (role === 'ms') {
        const [msPermissions] = await db.query(
          `SELECT * FROM elder_role_permissions
           WHERE role = ? AND permission = ?`,
          ['ms', permission]
        );

        if (msPermissions.length > 0) {
          return next();
        }
      }

      // If user doesn't have the permission, deny access
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    } catch (error) {
      logger.error('Permission check error:', error);
      return res.status(500).json({
        success: false,
        message: 'Permission check error',
        error: error.message
      });
    }
  };
};

/**
 * Check if user has required role (legacy method)
 * @param {Array<string>} roles - Array of allowed roles
 * @returns {Function} Middleware function
 */
exports.checkRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (roles.includes(req.user.role)) {
      next();
    } else {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
  };
};

// Legacy export for backward compatibility
module.exports.verifyToken = exports.authenticate;
