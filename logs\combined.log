{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-14 23:03:35"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:04:24"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:04:28"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:04:28"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:04:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:04:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:04:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:04:34"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:04:34"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:04:35"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-14 23:05:07"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:05:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:05:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:05:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:05:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:05:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:05:24"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:05:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:07:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:08:01"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:08:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:08:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:08:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:10:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:10:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:10:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:10:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:10:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:10:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:10:44"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:10:44"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:10:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:10:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:10:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:11:10"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:11:11"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:11:11"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:11:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:11:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:11:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:11:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:30"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:33"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:39"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:46"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:50"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:52"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:20:56"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:03"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:19"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:19"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:19"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:27"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:33"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:41"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:21:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:21:53"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:22:52"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:23:19"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-14 23:24:06"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:24:27"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-14 23:48:39"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:49"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:53"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:48:59"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:58:30"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:58:46"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:00"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:06"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:11"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:11"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:11"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:59:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:59:19"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:59:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:59:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:59:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-14 23:59:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-14 23:59:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:11"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:04:54"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:54"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:04:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:05:41"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:05:41"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:05:41"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:05:46"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:46"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:06:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:08:44"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:08:44"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:08:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:08:56"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:08:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:08:57"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:08:59"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:09"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:09"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:09"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:13"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:13"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:13"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:09:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:09:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:24"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:24"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:24"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:27"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:27"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:09:27"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:11:36"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:11:36"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:11:36"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:30"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:30"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:32"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:13:39"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:13:39"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:13:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:13:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:13:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:00"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:00"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:00"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:12"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:365:14)\n    at param (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:376:14)\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:421:3)","timestamp":"2025-05-15 00:14:38"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-05-15 00:14:38"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:02:21"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:06:20"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:08:10"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:08:32"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:15:23"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:18:29"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 10:57:35"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 17:30:14"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 20:16:03"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-15 23:20:44"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Error connecting to database: Access denied for user 'jwdbu'@'localhost' (using password: YES)","service":"coral-oeste-api","sqlMessage":"Access denied for user 'jwdbu'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'jwdbu'@'localhost' (using password: YES)\n    at Packet.asError (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-05-30 16:52:52"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-05-30 16:54:43"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 12:59:37"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 13:10:17"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 13:11:01"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 13:26:19"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 13:51:10"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 13:51:24"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 14:37:52"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-01 14:47:24"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Table 'coraldb01.users' doesn't exist","service":"coral-oeste-api","sql":"SELECT id, name, email, role, congregation_id FROM users WHERE id = 6","sqlMessage":"Table 'coraldb01.users' doesn't exist","sqlState":"42S02","stack":"Error: Table 'coraldb01.users' doesn't exist\n    at PromisePool.query (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\mysql2\\lib\\promise\\pool.js:36:22)\n    at exports.authenticate (C:\\laragon\\www\\SalonDelReino\\server\\middleware\\authMiddleware.js:36:30)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\laragon\\www\\SalonDelReino\\server\\node_modules\\express\\lib\\router\\index.js:175:3)","timestamp":"2025-06-01 14:48:18"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 14:48:36"}
{"level":"info","message":"Database connection established","service":"coral-oeste-api","timestamp":"2025-06-01 14:49:11"}
