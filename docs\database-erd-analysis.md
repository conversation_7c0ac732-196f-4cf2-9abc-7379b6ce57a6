# Coral Oeste App - Entity Relationship Diagram Analysis

## Database Relationships Overview

This document provides a detailed analysis of the entity relationships in the Coral Oeste App database.

## Core Entity Groups

### 1. Authentication & User Management

```mermaid
erDiagram
    congregations ||--o{ members : "belongs_to"
    members }o--|| roles : "has_role"
    roles ||--o{ role_permissions : "has_permissions"
    permissions ||--o{ role_permissions : "granted_to_roles"
    members ||--o{ user_permissions : "has_individual_permissions"
    permissions ||--o{ user_permissions : "granted_to_users"
    members ||--o{ elder_permissions : "elder_specific"
    congregations ||--o{ congregation_settings : "has_settings"
    
    congregations {
        int id PK
        string name
        string region
        string congregation_id UK
        string congregation_pin
        timestamp created_at
        timestamp updated_at
    }
    
    members {
        int id PK
        string username
        string password
        string name
        string email
        string phone
        int role_id FK
        int congregation_id FK
        boolean is_active
        timestamp last_login
        timestamp created_at
        timestamp updated_at
    }
    
    roles {
        int id PK
        string name UK
        string description
        timestamp created_at
        timestamp updated_at
    }
```

### 2. Meeting Management System

```mermaid
erDiagram
    midweek_meetings ||--o{ midweek_meeting_parts : "contains"
    midweek_meetings ||--o{ midweek_meeting_songs : "has_songs"
    midweek_meetings }o--|| members : "chairman"
    midweek_meeting_parts }o--|| members : "assignee"
    midweek_meeting_parts }o--|| members : "assistant"
    midweek_workbooks ||--o{ midweek_meeting_weeks : "contains"
    midweek_meeting_weeks ||--o{ midweek_meetings : "scheduled_for"
    midweek_sections ||--o{ midweek_part_definitions : "contains"
    midweek_part_definitions ||--o{ midweek_parts : "instances"
    midweek_parts }o--|| members : "assigned_to"
    
    midweek_meetings {
        int id PK
        date meeting_date
        time meeting_time
        string meeting_location
        int chairman_id FK
        string zoom_id
        string zoom_password
        timestamp created_at
        timestamp updated_at
    }
    
    midweek_meeting_parts {
        int id PK
        int meeting_id FK
        string part_type
        string title
        int assignee_id FK
        int assistant_id FK
        int order_index
        timestamp created_at
        timestamp updated_at
    }
```

### 3. Task Management System

```mermaid
erDiagram
    task_categories ||--o{ tasks : "categorizes"
    tasks ||--o{ task_assignments : "scheduled"
    task_assignments ||--o{ task_assignment_members : "assigned_to"
    task_assignment_members }o--|| members : "member"
    service_groups ||--o{ task_assignments : "assigned_to_group"
    service_groups ||--o{ members : "contains"
    
    tasks {
        int id PK
        string name
        string description
        int category_id FK
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    task_assignments {
        int id PK
        int task_id FK
        date assignment_date
        int service_group_id FK
        timestamp created_at
    }
    
    task_assignment_members {
        int id PK
        int assignment_id FK
        int member_id FK
        string role
        timestamp created_at
        timestamp updated_at
    }
```

### 4. Event Management

```mermaid
erDiagram
    event_categories ||--o{ events : "categorizes"
    events }o--|| members : "created_by"
    
    events {
        int id PK
        string title
        text description
        datetime start_date
        datetime end_date
        string location
        int category_id FK
        int created_by FK
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    event_categories {
        int id PK
        string name UK
        text description
        timestamp created_at
        timestamp updated_at
    }
```

### 5. Communication & Content

```mermaid
erDiagram
    letters {
        int id PK
        string filename UK
        string title
        date letter_date
        string category
        boolean is_visible
        timestamp created_at
        timestamp updated_at
    }
    
    songs {
        int id PK
        int number UK
        string title_es
        string title_en
        string category
        timestamp created_at
        timestamp updated_at
    }
    
    special_songs {
        int id PK
        int number UK
        string title_es
        string title_en
        string occasion
        timestamp created_at
        timestamp updated_at
    }
```

## Key Relationship Patterns

### 1. One-to-Many Relationships
- **congregations** → **members** (1:N)
- **roles** → **members** (1:N)
- **midweek_meetings** → **midweek_meeting_parts** (1:N)
- **tasks** → **task_assignments** (1:N)
- **event_categories** → **events** (1:N)

### 2. Many-to-Many Relationships
- **roles** ↔ **permissions** (via role_permissions)
- **members** ↔ **permissions** (via user_permissions)
- **task_assignments** ↔ **members** (via task_assignment_members)

### 3. Self-Referencing Relationships
- **members** can reference other **members** (elder_permissions.granted_by)
- **service_groups** can have hierarchical structures

## Data Flow Patterns

### Authentication Flow
```
1. User provides congregation_id + password
2. System validates against congregations table
3. System finds member with matching password
4. JWT token generated with member.id, role, congregation_id
5. Subsequent requests validated against member permissions
```

### Meeting Management Flow
```
1. Meeting created in midweek_meetings
2. Parts added to midweek_meeting_parts
3. Members assigned to parts via foreign keys
4. Songs linked via midweek_meeting_songs
5. Settings applied from midweek_meeting_settings
```

### Task Assignment Flow
```
1. Task defined in tasks table
2. Assignment created for specific date
3. Service group or individual members assigned
4. Assignment tracked via task_assignment_members
```

## Referential Integrity Rules

### CASCADE Deletes
- midweek_meetings → midweek_meeting_parts
- task_assignments → task_assignment_members
- events → event_categories (restricted)

### SET NULL on Delete
- member references in assignments
- optional foreign keys in meetings

### RESTRICT Deletes
- congregations (if members exist)
- roles (if members assigned)
- categories (if items exist)

## Index Strategy

### Primary Indexes
- All tables have auto-increment primary keys
- Unique constraints on business keys (congregation_id, usernames)

### Foreign Key Indexes
- All foreign keys automatically indexed
- Composite indexes on frequently queried combinations

### Performance Indexes
- Date-based indexes for meeting queries
- Name-based indexes for search functionality
- Status-based indexes for active/inactive filtering

## Security Considerations

### Data Isolation
- All data scoped by congregation_id
- Member access controlled by role permissions
- Elder permissions provide additional granular control

### Audit Trail
- created_at/updated_at on all tables
- Soft deletes where appropriate
- Permission changes tracked via elder_permissions

### Data Validation
- ENUM constraints on status fields
- Foreign key constraints prevent orphaned records
- Unique constraints prevent duplicates

## Scalability Considerations

### Current Capacity
- Single congregation: 64 members
- Meeting parts: 390 records
- Songs: 161 records
- Tasks: 8 active tasks

### Growth Projections
- Multiple congregations supported
- Unlimited meetings and parts
- Extensible permission system
- Modular feature flags

## Maintenance Recommendations

### Regular Tasks
1. Monitor foreign key constraint violations
2. Analyze query performance on large tables
3. Archive old meeting data periodically
4. Backup database with proper dependency order

### Performance Monitoring
1. Track slow queries on midweek_meeting_parts
2. Monitor member authentication frequency
3. Optimize JOIN operations on meeting views
4. Consider partitioning for multi-congregation deployments
