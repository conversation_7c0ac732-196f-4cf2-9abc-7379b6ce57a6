// Test script to check task dropdown functionality
require('dotenv').config();
const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

async function testTaskDropdowns() {
  try {
    console.log('Testing task dropdown functionality...\n');
    
    const connection = await mysql.createConnection(dbConfig);
    
    // Test 1: Check if task columns exist in database
    console.log('1. Checking database columns...');
    const [columns] = await connection.execute('DESCRIBE midweek_meetings');
    const taskColumns = ['audio_video_id', 'platform_id', 'microphone1_id', 'microphone2_id', 'cleaning_group'];
    
    taskColumns.forEach(colName => {
      const exists = columns.some(col => col.Field === colName);
      console.log(`   - ${colName}: ${exists ? '✅ EXISTS' : '❌ MISSING'}`);
    });
    
    // Test 2: Check if we have male members for task assignments
    console.log('\n2. Checking available male members...');
    const [members] = await connection.execute(`
      SELECT id, name, gender, role 
      FROM members 
      WHERE gender = 'male' 
      ORDER BY name
    `);
    
    console.log(`   Found ${members.length} male members:`);
    members.forEach(member => {
      console.log(`   - ${member.name} (${member.role})`);
    });
    
    // Test 3: Check current task assignments in meetings
    console.log('\n3. Checking current task assignments...');
    const [meetings] = await connection.execute(`
      SELECT 
        id, 
        meeting_date,
        audio_video_id,
        platform_id,
        microphone1_id,
        microphone2_id,
        cleaning_group
      FROM midweek_meetings 
      WHERE meeting_date >= CURDATE() - INTERVAL 30 DAY
      ORDER BY meeting_date DESC
      LIMIT 5
    `);
    
    console.log(`   Recent meetings with task assignments:`);
    meetings.forEach(meeting => {
      console.log(`   Meeting ${meeting.id} (${meeting.meeting_date}):`);
      console.log(`     - Audio/Video: ${meeting.audio_video_id || 'NOT ASSIGNED'}`);
      console.log(`     - Platform: ${meeting.platform_id || 'NOT ASSIGNED'}`);
      console.log(`     - Microphone 1: ${meeting.microphone1_id || 'NOT ASSIGNED'}`);
      console.log(`     - Microphone 2: ${meeting.microphone2_id || 'NOT ASSIGNED'}`);
      console.log(`     - Cleaning Group: ${meeting.cleaning_group || 'NOT ASSIGNED'}`);
    });
    
    // Test 4: Test updating a meeting with task assignments
    console.log('\n4. Testing task assignment update...');
    if (meetings.length > 0 && members.length > 0) {
      const testMeeting = meetings[0];
      const testMember = members[0];
      
      console.log(`   Updating meeting ${testMeeting.id} with test assignments...`);
      
      await connection.execute(`
        UPDATE midweek_meetings 
        SET 
          audio_video_id = ?,
          platform_id = ?,
          microphone1_id = ?,
          microphone2_id = ?,
          cleaning_group = ?
        WHERE id = ?
      `, [
        testMember.id,
        testMember.id,
        testMember.id,
        testMember.id,
        '1',
        testMeeting.id
      ]);
      
      // Verify the update
      const [updatedMeeting] = await connection.execute(`
        SELECT 
          audio_video_id,
          platform_id,
          microphone1_id,
          microphone2_id,
          cleaning_group
        FROM midweek_meetings 
        WHERE id = ?
      `, [testMeeting.id]);
      
      if (updatedMeeting.length > 0) {
        const meeting = updatedMeeting[0];
        console.log(`   ✅ Update successful:`);
        console.log(`     - Audio/Video: ${meeting.audio_video_id}`);
        console.log(`     - Platform: ${meeting.platform_id}`);
        console.log(`     - Microphone 1: ${meeting.microphone1_id}`);
        console.log(`     - Microphone 2: ${meeting.microphone2_id}`);
        console.log(`     - Cleaning Group: ${meeting.cleaning_group}`);
      } else {
        console.log(`   ❌ Update failed - could not retrieve updated meeting`);
      }
    } else {
      console.log(`   ⚠️  Skipping update test - no meetings or members available`);
    }
    
    await connection.end();
    console.log('\n✅ Task dropdown test completed successfully!');
    
  } catch (error) {
    console.error('❌ Error testing task dropdowns:', error.message);
  }
}

testTaskDropdowns();
