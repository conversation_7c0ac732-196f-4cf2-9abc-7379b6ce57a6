// Debug script to trace the exact midweek meeting task assignment flow
require('dotenv').config();
const mysql = require('mysql2/promise');

const dbConfig = {
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
};

async function debugTaskFlow() {
  try {
    console.log('🔍 DEBUGGING MIDWEEK MEETING TASK ASSIGNMENT FLOW\n');
    
    const connection = await mysql.createConnection(dbConfig);
    
    // Step 1: Check what meeting 270 looks like RIGHT NOW
    console.log('1️⃣ CURRENT STATE OF MEETING 270:');
    const [currentMeeting] = await connection.execute(`
      SELECT * FROM midweek_meetings WHERE id = 270
    `);
    
    if (currentMeeting.length > 0) {
      const meeting = currentMeeting[0];
      console.log('   Meeting Date:', meeting.meeting_date);
      console.log('   Audio/Video ID:', meeting.audio_video_id);
      console.log('   Platform ID:', meeting.platform_id);
      console.log('   Microphone 1 ID:', meeting.microphone1_id);
      console.log('   Microphone 2 ID:', meeting.microphone2_id);
      console.log('   Cleaning Group:', meeting.cleaning_group);
      console.log('   Chairman ID:', meeting.chairman_id);
      console.log('   Opening Prayer ID:', meeting.opening_prayer_id);
      console.log('   Closing Prayer ID:', meeting.closing_prayer_id);
    } else {
      console.log('   ❌ Meeting 270 not found!');
      return;
    }
    
    // Step 2: Check what members are available and their roles
    console.log('\n2️⃣ AVAILABLE MEMBERS AND THEIR ROLES:');
    const [members] = await connection.execute(`
      SELECT m.id, m.name, r.name as role_name, m.role_id, m.is_active
      FROM members m
      LEFT JOIN roles r ON m.role_id = r.id
      ORDER BY r.id, m.name
    `);
    
    console.log(`   Total members: ${members.length}`);
    
    // Group by role
    const membersByRole = {};
    members.forEach(member => {
      const role = member.role_name || 'unknown';
      if (!membersByRole[role]) membersByRole[role] = [];
      membersByRole[role].push(member);
    });
    
    Object.keys(membersByRole).forEach(role => {
      console.log(`   ${role}: ${membersByRole[role].length} members`);
      membersByRole[role].forEach(member => {
        console.log(`     - ${member.name} (ID: ${member.id}, Active: ${member.is_active})`);
      });
    });
    
    // Step 3: Check what the API endpoint returns for this meeting
    console.log('\n3️⃣ TESTING API ENDPOINT:');
    try {
      const fetch = require('node-fetch');
      
      // Test the GET endpoint that the "Editar" button uses
      console.log('   Testing GET /api/midweek/wol/meetings/270...');
      const getResponse = await fetch('http://localhost:8000/api/midweek/wol/meetings/270');
      console.log(`   Status: ${getResponse.status}`);
      
      if (getResponse.ok) {
        const meetingData = await getResponse.json();
        console.log('   ✅ API Response received');
        console.log('   Meeting data structure:');
        console.log('     - audio_video_id:', meetingData.audio_video_id);
        console.log('     - platform_id:', meetingData.platform_id);
        console.log('     - microphone1_id:', meetingData.microphone1_id);
        console.log('     - microphone2_id:', meetingData.microphone2_id);
        console.log('     - cleaning_group:', meetingData.cleaning_group);
      } else {
        console.log('   ❌ API request failed');
        const errorText = await getResponse.text();
        console.log('   Error:', errorText);
      }
      
    } catch (apiError) {
      console.log('   ❌ API test failed:', apiError.message);
    }
    
    // Step 4: Check the members API that populates dropdowns
    console.log('\n4️⃣ TESTING MEMBERS API:');
    try {
      const fetch = require('node-fetch');
      
      console.log('   Testing GET /api/members...');
      const membersResponse = await fetch('http://localhost:8000/api/members');
      console.log(`   Status: ${membersResponse.status}`);
      
      if (membersResponse.ok) {
        const membersData = await membersResponse.json();
        console.log('   ✅ Members API Response received');
        console.log(`   Total members returned: ${membersData.length}`);
        
        // Check if members have role information
        const sampleMember = membersData[0];
        if (sampleMember) {
          console.log('   Sample member structure:');
          console.log('     - id:', sampleMember.id);
          console.log('     - name:', sampleMember.name);
          console.log('     - role:', sampleMember.role);
          console.log('     - role_id:', sampleMember.role_id);
          console.log('     - is_active:', sampleMember.is_active);
        }
        
        // Count brothers (eligible for tasks)
        const brothers = membersData.filter(m => 
          m.role === 'elder' || 
          m.role === 'ministerial_servant' || 
          m.role === 'overseer_coordinator' ||
          m.role === 'developer'
        );
        console.log(`   Brothers eligible for tasks: ${brothers.length}`);
        
      } else {
        console.log('   ❌ Members API request failed');
        const errorText = await membersResponse.text();
        console.log('   Error:', errorText);
      }
      
    } catch (apiError) {
      console.log('   ❌ Members API test failed:', apiError.message);
    }
    
    // Step 5: Test a simulated form submission
    console.log('\n5️⃣ SIMULATING FORM SUBMISSION:');
    
    // Get some brothers for testing
    const [testBrothers] = await connection.execute(`
      SELECT m.id, m.name 
      FROM members m
      JOIN roles r ON m.role_id = r.id
      WHERE r.name IN ('elder', 'ministerial_servant', 'overseer_coordinator', 'developer')
      AND m.is_active = 1
      LIMIT 4
    `);
    
    if (testBrothers.length >= 4) {
      const testData = {
        meeting_date: '2025-06-06',
        audio_video_id: testBrothers[0].id,
        platform_id: testBrothers[1].id,
        microphone1_id: testBrothers[2].id,
        microphone2_id: testBrothers[3].id,
        cleaning_group: '2'
      };
      
      console.log('   Test assignment data:');
      console.log(`     - Audio/Video: ${testBrothers[0].name} (ID: ${testData.audio_video_id})`);
      console.log(`     - Platform: ${testBrothers[1].name} (ID: ${testData.platform_id})`);
      console.log(`     - Microphone 1: ${testBrothers[2].name} (ID: ${testData.microphone1_id})`);
      console.log(`     - Microphone 2: ${testBrothers[3].name} (ID: ${testData.microphone2_id})`);
      console.log(`     - Cleaning Group: ${testData.cleaning_group}`);
      
      // Test the PUT endpoint
      try {
        const fetch = require('node-fetch');
        
        console.log('\n   Testing PUT /api/midweek/wol/meetings/270...');
        const putResponse = await fetch('http://localhost:8000/api/midweek/wol/meetings/270', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(testData)
        });
        
        console.log(`   Status: ${putResponse.status}`);
        
        if (putResponse.ok) {
          const responseData = await putResponse.json();
          console.log('   ✅ PUT request successful');
          console.log('   Response:', responseData);
          
          // Verify the update in database
          const [updatedMeeting] = await connection.execute(`
            SELECT audio_video_id, platform_id, microphone1_id, microphone2_id, cleaning_group
            FROM midweek_meetings WHERE id = 270
          `);
          
          if (updatedMeeting.length > 0) {
            const updated = updatedMeeting[0];
            console.log('\n   ✅ Database verification:');
            console.log(`     - Audio/Video ID: ${updated.audio_video_id} (Expected: ${testData.audio_video_id})`);
            console.log(`     - Platform ID: ${updated.platform_id} (Expected: ${testData.platform_id})`);
            console.log(`     - Microphone 1 ID: ${updated.microphone1_id} (Expected: ${testData.microphone1_id})`);
            console.log(`     - Microphone 2 ID: ${updated.microphone2_id} (Expected: ${testData.microphone2_id})`);
            console.log(`     - Cleaning Group: ${updated.cleaning_group} (Expected: ${testData.cleaning_group})`);
            
            // Check if values match
            const matches = {
              audio_video: updated.audio_video_id == testData.audio_video_id,
              platform: updated.platform_id == testData.platform_id,
              microphone1: updated.microphone1_id == testData.microphone1_id,
              microphone2: updated.microphone2_id == testData.microphone2_id,
              cleaning: updated.cleaning_group == testData.cleaning_group
            };
            
            console.log('\n   Match verification:');
            Object.keys(matches).forEach(key => {
              console.log(`     - ${key}: ${matches[key] ? '✅ MATCH' : '❌ NO MATCH'}`);
            });
            
          }
          
        } else {
          console.log('   ❌ PUT request failed');
          const errorText = await putResponse.text();
          console.log('   Error:', errorText);
        }
        
      } catch (putError) {
        console.log('   ❌ PUT request error:', putError.message);
      }
      
    } else {
      console.log('   ⚠️ Not enough brothers available for testing');
    }
    
    await connection.end();
    console.log('\n🏁 DEBUG COMPLETE');
    
  } catch (error) {
    console.error('❌ Debug script error:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

debugTaskFlow();
