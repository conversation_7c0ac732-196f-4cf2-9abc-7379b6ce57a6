# Coral Oeste App - Comprehensive Database Schema Analysis

**Generated on:** June 1, 2025  
**Server Status:** ✅ Running on port 8000  
**Database:** coraldb01 (MySQL)  
**Total Tables:** 43  

## Executive Summary

The Coral Oeste App database is a comprehensive church management system with 43 tables, 434 columns, and 25 foreign key relationships. The database supports authentication, meeting management, task assignment, event planning, communication, and song management.

## Server Status

- **Backend Server:** ✅ Running on http://localhost:8000
- **Database Connection:** ✅ Connected to coraldb01
- **API Endpoints:** ✅ All routes registered and functional
- **Authentication:** ✅ JWT-based authentication working
- **File Uploads:** ✅ Letters upload directory configured

## Core Database Architecture

### 1. Authentication & Authorization Layer

#### Primary Tables:
- **congregations** (1 row) - Stores congregation information
- **members** (64 rows) - User accounts and profiles
- **roles** (5 rows) - System roles (publisher, elder, etc.)
- **permissions** (17 rows) - System permissions
- **role_permissions** (45 rows) - Maps roles to permissions
- **user_permissions** (0 rows) - Individual user permissions
- **elder_permissions** (43 rows) - Elder-specific permissions

#### Key Relationships:
```
congregations (1) ←→ (N) members
members (N) ←→ (1) roles
roles (N) ←→ (N) permissions (via role_permissions)
members (N) ←→ (N) permissions (via user_permissions)
```

### 2. Meeting Management System

#### Midweek Meetings:
- **midweek_meetings** (30 rows) - Main meeting records
- **midweek_meeting_parts** (390 rows) - Individual meeting parts
- **midweek_meeting_settings** (1 row) - Meeting configuration
- **midweek_meeting_songs** (90 rows) - Song assignments
- **midweek_workbooks** (3 rows) - JW.org workbook data
- **midweek_meeting_weeks** (0 rows) - Weekly meeting data
- **midweek_parts** (136 rows) - Part definitions
- **midweek_sections** (3 rows) - Meeting sections
- **midweek_part_definitions** (18 rows) - Part templates
- **midweek_part_roles** (74 rows) - Role assignments for parts

#### Views:
- **midweek_meeting_view** - Comprehensive meeting data view
- **midweek_assignments_view** - Assignment overview

### 3. Task Management System

#### Core Tables:
- **tasks** (8 rows) - Task definitions
- **task_categories** (5 rows) - Task categorization
- **task_assignments** (14 rows) - Task scheduling
- **task_assignment_members** (18 rows) - Member assignments
- **task_settings** (2 rows) - Task configuration
- **service_groups** (7 rows) - Service group organization

### 4. Event Management

#### Tables:
- **events** (8 rows) - Congregation events
- **event_categories** (5 rows) - Event categorization

### 5. Communication System

#### Tables:
- **letters** (7 rows) - Letter management
- **jw_scraper_logs** (0 rows) - JW.org scraping logs

### 6. Song Management

#### Tables:
- **songs** (161 rows) - Main song catalog
- **special_songs** (3 rows) - Special occasion songs

### 7. Additional Systems

#### Tables:
- **field_service_records** (0 rows) - Service time tracking
- **territories** (0 rows) - Territory management
- **groups** (7 rows) - Group organization
- **feature_flags** (4 rows) - Feature toggles
- **congregation_settings** (5 rows) - Congregation configuration

## Critical Database Relationships

### 1. Authentication Flow
```
User Login → congregations.congregation_id + member.password
↓
JWT Token Generation (member.id, role, congregation_id)
↓
Role-based Access Control (permissions via roles)
```

### 2. Meeting Management Flow
```
midweek_meetings (main record)
├── midweek_meeting_parts (individual parts)
├── midweek_meeting_songs (song assignments)
└── member assignments (via foreign keys)
```

### 3. Task Assignment Flow
```
tasks → task_assignments (date-based)
└── task_assignment_members (member assignments)
```

## Database Integrity Features

### Foreign Key Constraints (25 total):
1. **Cascade Deletes:** Meeting parts deleted when meeting is deleted
2. **Set NULL:** Member references set to NULL when member is deleted
3. **Restrict Deletes:** Event categories cannot be deleted if events exist

### Indexes (110 total):
- Primary keys on all tables
- Foreign key indexes for performance
- Unique constraints on critical fields
- Date-based indexes for queries

### Data Validation:
- ENUM constraints for status fields
- NOT NULL constraints on required fields
- Unique constraints preventing duplicates
- Default values ensuring data consistency

## Performance Considerations

### High-Traffic Tables:
1. **midweek_meeting_parts** (390 rows) - Most active table
2. **members** (64 rows) - Frequent authentication queries
3. **midweek_parts** (136 rows) - Meeting part lookups
4. **songs** (161 rows) - Song title lookups

### Optimization Strategies:
- Indexed foreign keys for JOIN performance
- Date-based indexes for meeting queries
- Congregation-based partitioning for multi-tenant support

## Security Implementation

### Authentication:
- JWT tokens with expiration
- Password-based member authentication
- Congregation-level isolation

### Authorization:
- Role-based permissions
- Elder-specific permissions
- Feature flags for controlled access

### Data Protection:
- Foreign key constraints prevent orphaned records
- Soft deletes where appropriate (SET NULL)
- Audit trails via created_at/updated_at timestamps

## Migration and Backup Strategy

### Current State:
- All tables properly created with constraints
- Sample data populated for testing
- Relationships properly established

### Backup Considerations:
- Regular database backups essential
- Foreign key dependencies require ordered restoration
- Large tables (midweek_meeting_parts) need special handling

## Recommendations

### Immediate Actions:
1. ✅ Server running successfully on port 8000
2. ✅ Database connections stable
3. ✅ All relationships properly configured

### Future Enhancements:
1. **Performance Monitoring:** Add query performance tracking
2. **Data Archival:** Implement archival strategy for old meetings
3. **Backup Automation:** Automated backup scheduling
4. **Index Optimization:** Monitor and optimize slow queries

## API Integration Status

### Working Endpoints:
- ✅ Authentication (`/api/auth/*`)
- ✅ Members (`/api/members`)
- ✅ Midweek Meetings (`/api/midweek/*`)
- ✅ Songs (`/api/songs/*`)
- ✅ Letters (`/api/letters/*`)
- ✅ Events (`/api/events/*`)
- ✅ Database Test (`/api/db-test`)

### Database Connection Pool:
- Host: localhost
- User: jwdbu
- Database: coraldb01
- Status: ✅ Active and stable

## Conclusion

The Coral Oeste App database is well-structured with proper relationships, constraints, and indexes. The server is running successfully on port 8000 with all systems operational. The database supports the full functionality of the church management system with room for future expansion.
