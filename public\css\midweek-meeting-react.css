/* Midweek Meeting React CSS */

:root {
  --primary-color: #673ab7;
  --secondary-color: #4285f4;
  --background-color: #f8f9fa;
  --surface-color: #ffffff;
  --error-color: #b00020;
  --success-color: #0f9d58;
  --warning-color: #f4b400;
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-disabled: #9aa0a6;
  --border-color: #dadce0;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.5;
}

.app-header {
  background: linear-gradient(135deg, #673ab7, #5e35b1);
  color: white;
  padding: 16px;
  text-align: center;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-title {
  font-size: 20px;
  font-weight: 500;
}

.back-button {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: white;
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 16px;
}

.back-button i {
  margin-right: 4px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.card {
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
}

.card-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 16px;
}

.tab {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  transition: all 0.3s;
}

.tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

.button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.button:hover {
  background-color: #5e35b1;
}

.button i {
  margin-right: 8px;
}

.button-secondary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.button-secondary:hover {
  background-color: rgba(103, 58, 183, 0.04);
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  text-align: left;
  padding: 12px;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: 500;
  color: var(--text-secondary);
}

tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
}

.modal {
  background-color: var(--surface-color);
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 500px;
  position: relative;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-small {
  max-width: 400px;
}

.modal-medium {
  max-width: 600px;
}

.modal-large {
  max-width: 800px;
}

.modal-full {
  max-width: 1200px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--primary-color);
  color: white;
}

.modal-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.modal-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.modal-content {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 24px;
  border-top: 1px solid var(--border-color);
  background-color: var(--background-color);
}

/* Original Modal Styles for View/Edit Modals */
#viewMeetingModal, #editMeetingModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

#viewMeetingModal.show, #editMeetingModal.show {
  display: flex;
}

/* Special styling for the view meeting modal */
#viewMeetingModal .modal-content {
  max-width: 480px;
}

/* View meeting styles to match frontend */
.view-meeting-modal .modal-body {
  padding: 20px;
}

.view-meeting-modal .modal-header {
  background-color: var(--primary-color);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color);
}

.view-meeting-modal .close {
  color: white;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.view-meeting-modal .close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Meeting view content styles */
.meeting-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.meeting-date {
  font-size: 18px;
  font-weight: 500;
  color: var(--primary-color);
  margin: 5px 0;
}

.meeting-time, .meeting-location {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 3px 0;
}

.meeting-section {
  margin-bottom: 20px;
}

.meeting-section h3 {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  padding-bottom: 5px;
  margin-bottom: 15px;
}

.meeting-part {
  background-color: var(--background-color);
  padding: 10px 15px;
  margin-bottom: 10px;
  border-radius: 5px;
  border-left: 3px solid var(--primary-color);
}

.meeting-part h4 {
  margin: 0 0 5px 0;
  color: var(--text-primary);
}

.meeting-part p {
  margin: 3px 0;
  color: var(--text-secondary);
  font-size: 14px;
}

.zoom-details {
  background-color: var(--info-color);
  color: white;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.zoom-details a {
  color: white;
  text-decoration: underline;
}

/* Loading Styles */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
}

.loading-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.loading-spinner {
  position: relative;
  width: 40px;
  height: 40px;
  margin-bottom: 16px;
}

.spinner-small {
  width: 24px;
  height: 24px;
}

.spinner-medium {
  width: 40px;
  height: 40px;
}

.spinner-large {
  width: 56px;
  height: 56px;
}

.spinner-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
  text-align: center;
}

/* Error Styles */
.error-message-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px;
  background-color: #ffebee;
  border-radius: 8px;
  margin: 16px 0;
  text-align: center;
}

.error-icon {
  color: var(--error-color);
  font-size: 48px;
  margin-bottom: 16px;
}

.error-icon i {
  font-size: 48px;
}

.error-title {
  color: var(--error-color);
  font-weight: 500;
  margin: 0 0 8px 0;
  font-size: 18px;
}

.error-details {
  color: var(--text-secondary);
  margin: 0 0 16px 0;
  font-size: 14px;
}
