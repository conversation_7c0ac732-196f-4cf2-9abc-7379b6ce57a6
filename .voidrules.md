# Coral Oeste App - User Guidelines

## Table of Contents

1. [Project Overview](#project-overview)
2. [Project Structure](#project-structure)
3. [Database Schema](#database-schema)
4. [Authentication System](#authentication-system)
5. [UI Design Guidelines](#ui-design-guidelines)
6. [Development Rules](#development-rules)
7. [Meeting Management](#meeting-management)
8. [Lessons Learned from Letters Section](#lessons-learned-from-letters-section)
9. [Lessons Learned from Song Management](#lessons-learned-from-song-management)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)
12. [Senior Developer Guidelines](#senior-developer-guidelines)
13. [Authentication Best Practices](#authentication-best-practices)
14. [Current Implementation Status](#current-implementation-status)

## Project Overview

The Coral Oeste App is a church management application designed for a congregation. The app provides functionality for managing various aspects of church activities including administration, field service, meetings, tasks, and assignments. The application features a modern UI, multilingual support, and integration with data from jw.org.

### Key Features

- **Member Dashboard**: Central hub with access to all features
- **Service Management**: Field service tracking and management
- **Midweek Meeting Management**: Information about midweek meetings and parts
- **Weekend Meeting Management**: Information about weekend meetings and parts
- **Assignments**: Personal and congregation assignments
- **Letters**: Communication management
- **Events**: Congregation event management
- **Admin Section**: Administrative tools for congregation leaders
- **Song Management**: Multilingual song catalog with JW.org integration
- **Database Backup/Restore**: Tools for database maintenance and recovery

### Technology Stack

- **Frontend**: HTML, CSS, JavaScript
- **Backend**: Node.js with Express
- **Database**: MySQL
- **Authentication**: JWT-based authentication
- **File Storage**: Local filesystem for PDF documents and other files

## Project Structure

The project follows a feature-based organization with the following structure:

```
/
├── docs/                  # Project documentation
├── public/                # Static files and frontend
│   ├── admin/             # Admin interface
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── uploads/           # Uploaded files
│   │   └── letters/       # Uploaded letter PDFs
│   └── *.html             # HTML pages
├── server/                # Server-side code
│   ├── config/            # Configuration files
│   ├── controllers/       # Request handlers
│   ├── database/          # Database scripts and migrations
│   ├── middleware/        # Express middleware
│   ├── models/            # Data models
│   └── routes/            # API routes
├── scripts/               # Utility scripts
├── database/              # Database setup and migrations
├── .env                   # Environment variables
└── server.js              # Main server entry point
```

## Database Schema

The application uses a MySQL database with the following key tables:

> Note: The database schema is evolving as the application develops. Always check the latest schema in the database directory.

### Core Tables

1. **congregations**: Stores congregation information and authentication details
2. **roles**: Stores user roles for role-based access control
3. **members**: Stores member accounts and profile information
4. **elder_permissions**: Controls access to specific admin sections

### Feature-Specific Tables

5. **letters**: Stores letter metadata linking to PDF files
6. **midweek_meetings**: Stores information for midweek meetings
7. **weekend_meetings**: Stores information for weekend meetings
8. **midweek_meeting_parts**: Stores individual parts for midweek meetings
9. **weekend_meeting_parts**: Stores individual parts for weekend meetings
10. **tasks**: Stores task definitions and metadata
11. **task_assignments**: Stores task assignments to dates
12. **field_service_records**: Tracks service time and activity
13. **songs**: Stores song catalog information in multiple languages

### Database Connection

The database connection is configured using environment variables:

```
# Example environment variables - replace with your actual values
DB_HOST=your_database_host
DB_PORT=your_database_port
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
```

> **Security Note**: Never commit actual database credentials to version control. Use environment variables and keep sensitive information in a secure, non-versioned `.env` file.

## Authentication System

The application uses a simplified authentication system based on Congregation ID and member PIN:

### Authentication Flow

1. **Congregation Connection**:
   - Users connect to their congregation using region, congregation ID, and congregation PIN
   - Default Congregation ID for Coral Oeste: [Contact administrator for access]
   - Default Congregation PIN: [Contact administrator for access]

2. **Role-Based Access**:
   - Different roles have different access levels:
     - **Developer**: Full access to all features [Contact administrator for access]
     - **Overseer/Coordinator**: Full access to all features
     - **Elder**: Access to most administrative features
     - **Ministerial Servant**: Limited administrative access
     - **Publisher**: Basic access to frontend features only

3. **JWT Authentication**:
   - The system uses JWT tokens for API authentication
   - Tokens include user ID, role, and congregation information
   - Protected routes verify the token before allowing access
   - All API endpoints must use proper authentication middleware

## UI Design Guidelines

The application follows specific UI design guidelines:

### General UI Guidelines

- **Modern, Mobile-Friendly Design**: All pages should be responsive and work well on mobile devices
- **Color Scheme**: White backgrounds with grey/black text for better readability
- **Icons**: Smaller, modern icons for a cleaner look
- **Toggle Buttons**: Modern toggle buttons instead of checkboxes

### Admin Section Guidelines

- **Header Design**: Each admin page should have a header with a background color matching its dashboard icon color
- **Navigation**: Only include a back button in the header, no other navigation elements
- **Section Colors**:
  - Database Management: Cyan/Teal (#00bcd4)
  - Permissions: Yellow/Gold (#9e9d24)
  - Other sections: Match their dashboard icon colors

### Dashboard Layout

- **Grid Layout**: Dashboard items should be arranged in a grid
- **Ordering**: Follow the specific ordering: Servicio al Campo, Programas, Entre Semana (separate section), Fin de semana (separate section), Actividades (Asignaciones, Tareas), Cartas & Eventos
- **Welcome Message**: Display the congregation name in the welcome message

## Development Rules

To maintain consistency and quality in the codebase, follow these rules:

### Frontend-Backend Integration

1. **Preserve Frontend UI**: Always maintain the existing frontend UI design and user experience
2. **Adapt Backend to Frontend**: Implement backend features to match the needs of the existing frontend UI
3. **Data Migration Process**:
   - When working on a section, create a migration file for existing hardcoded data
   - Migrate all hardcoded frontend data to the database
   - Only after implementation is complete and verified, remove hardcoded data from frontend
   - Replace with authenticated database-driven data
4. **Consistent User Experience**: Ensure the user experience remains consistent before and after migration
5. **Verify Both Sides**: Test both frontend and backend thoroughly after integration

### General Rules

1. **Environment Variables**: Always use environment variables for sensitive information
2. **API-First Approach**: Implement proper API endpoints for all features
3. **Mobile Responsiveness**: Ensure all pages work well on mobile devices
4. **Error Handling**: Implement proper error handling and user feedback
5. **Documentation**: Document all new features and changes
6. **Check Existing Resources**: Always check for existing scripts and documentation before creating new ones
7. **Understand Before Changing**: Get thoroughly familiar with the codebase before making changes

### Database Rules

1. **Table Creation**: Always check if a table exists before creating it
2. **Schema Consistency**: Follow the established schema patterns for new tables
3. **Data Validation**: Validate all data before inserting or updating
4. **Connection Management**: Use connection pools for better performance
5. **Avoid External Commands**: Use Node.js libraries instead of external commands for database operations
6. **Batch Processing**: Implement batch processing for large database operations
7. **Error Handling**: Provide detailed error information and user-friendly messages

### Authentication Rules

1. **No Hardcoding**: Never hardcode authentication credentials
2. **Role Verification**: Always verify user roles before allowing access to protected features
3. **Token Management**: Properly handle token expiration and renewal
4. **Congregation Settings**: Allow configuration of congregation ID and PIN from admin settings
5. **Middleware Usage**: Always use middleware for authentication
6. **Bypass Prevention**: Never bypass authentication for convenience
7. **Security First**: Consider security implications of all authentication-related changes

### UI Rules

1. **Consistency**: Maintain consistent UI across all pages
2. **Mobile-First**: Design for mobile first, then adapt for larger screens
3. **Accessibility**: Ensure all UI elements are accessible
4. **Performance**: Optimize UI for performance, especially on mobile devices

## Meeting Management

The meetings section is divided into two distinct parts due to their different nature and requirements:

### Midweek Meetings

1. **Structure**:
   - Midweek meetings follow the "Life and Ministry Meeting Workbook" structure
   - They include multiple parts with different participants
   - Parts are organized into sections: Treasures, Digging for Gems, Living as Christians, etc.

2. **Database Design**:
   - The `midweek_meetings` table stores the overall meeting information (date, chairman, etc.)
   - The `midweek_meeting_parts` table stores individual parts with assignments
   - Each part links to a specific meeting and may have an assigned member

3. **Implementation Requirements**:
   - Ability to fetch meeting workbook data from jw.org
   - Support for assigning members to different parts
   - Tracking of completed parts and attendance

### Weekend Meetings

1. **Structure**:
   - Weekend meetings have a simpler structure with a public talk and Watchtower study
   - Public talks may have visiting speakers from other congregations
   - The Watchtower study follows a standard format with a conductor and reader

2. **Database Design**:
   - The `weekend_meetings` table stores meeting information (date, public talk speaker, etc.)
   - The `weekend_meeting_parts` table stores specific assignments (conductor, reader, etc.)

3. **Implementation Requirements**:
   - Support for tracking visiting speakers and their congregations
   - Management of Watchtower study assignments
   - Tracking of attendance

### Meeting Location Management

Both meeting types require the ability to manage meeting locations:

- Support for both in-person (Kingdom Hall) and virtual (Zoom) meetings
- Admin control over meeting location (Salon/Zoom)
- Storage and display of Zoom meeting details when applicable

## Lessons Learned from Letters Section

The letters section implementation provides valuable lessons for other sections:

### Database-First Approach

1. **Verify Database Tables First**: Always check if the table for a section already exists before creating it.

2. **Database-First Approach**: Create a proper database table for each section before implementing the frontend or admin interfaces.

### API Design

3. **Consistent API Design**: Design API endpoints that can be used by both frontend and admin interfaces to ensure consistency.

4. **Error Handling**: Implement proper error handling and fallbacks to ensure a good user experience even when things go wrong.

### File Management

5. **File Storage**: When dealing with file uploads, ensure proper linking between database records and files in the filesystem.

6. **Title Management**: Preserve user-entered titles rather than automatically generating them from filenames.

7. **Consistent Display**: Ensure the same data is displayed in both frontend and admin interfaces by fetching from the same API endpoints.

### Implementation Process

8. **Database Table Creation**: Create the table with all necessary fields (filename, title, date, category, visibility).

9. **API Endpoints**: Implement endpoints for CRUD operations (GET, POST, PUT, DELETE).

10. **Data Migration**: Create migration scripts to move hardcoded data to the database.

11. **Frontend Integration**: Update the frontend to fetch data from the API instead of using hardcoded values.

## Lessons Learned from Song Management

The Song Management implementation provides valuable lessons for handling external data sources and multilingual support:

### Multiple Data Sources with Fallbacks

1. **Resilient Data Fetching**: Implement multiple data sources with fallbacks to ensure reliability:
   - Primary source: JW.org song catalog
   - Secondary source: Custom songs database
   - Fallback: Generic song titles when specific titles are unavailable

2. **Caching Mechanism**: Implement caching to improve performance and reduce dependency on external sources:
   - Cache song catalog in local files
   - Use in-memory caching for frequently accessed data
   - Implement cache invalidation when data is updated

### Multilingual Support

1. **Unified Data Structure**: Use a consistent data structure for all languages:
   ```javascript
   songCatalog = {
     es: { /* Spanish song titles */ },
     en: { /* English song titles */ },
     lastUpdated: { es: timestamp, en: timestamp }
   };
   ```

2. **Language-Specific Processing**: Handle language-specific requirements while maintaining a consistent API:
   - Different URLs for different languages
   - Language-specific text cleaning
   - Consistent song numbering across languages

### Integration with Other Systems

1. **Meeting Management Integration**: Ensure seamless integration with the meeting management system:
   - Extract song numbers from meeting data
   - Use the Dynamic Song Title Service to get titles
   - Handle special songs appropriately
   - Allow administrators to override any song title

## Troubleshooting

Common issues and their solutions:

### Database Issues

1. **Connection Errors**:
   - Check database credentials in .env file
   - Verify MySQL server is running
   - Run the database connection test script: `npm run db:test`

2. **Missing Tables**:
   - Run the database setup script: `npm run db:setup`
   - Check for errors in the console output

3. **Data Inconsistency**:
   - Verify that all sections are fetching data from the same database tables
   - Check for hardcoded data that should be coming from the database

### Authentication Issues

1. **Login Failures**:
   - Verify congregation ID and PIN
   - Check user role and permissions
   - Clear browser storage and try again

2. **Access Denied**:
   - Verify user role has permission for the requested feature
   - Check elder_permissions table for specific permissions

### File Upload Issues

1. **Upload Failures**:
   - Check file size and type
   - Verify uploads directory exists and has write permissions
   - Check server logs for specific errors

2. **Missing Files**:
   - Verify file paths in the database match actual file locations
   - Check for file deletion outside the application

## Best Practices

### Code Organization

1. **Modular Code**: Organize code into small, reusable modules
2. **Separation of Concerns**: Keep business logic separate from UI code
3. **Consistent Naming**: Use consistent naming conventions across the codebase
4. **Comments**: Add comments for complex logic or non-obvious behavior
5. **Check Existing Resources**: Always check for existing scripts and documentation before creating new ones
6. **Avoid Duplication**: Do not create multiple files for the same functionality
7. **Understand Before Changing**: Get thoroughly familiar with the codebase before making changes

### API Design

1. **RESTful Endpoints**: Follow RESTful principles for API design
2. **Versioning**: Version APIs to allow for future changes
3. **Validation**: Validate all input data before processing
4. **Error Responses**: Return meaningful error messages and status codes

### Database Management

1. **Indexing**: Add indexes for frequently queried columns
2. **Relationships**: Define proper relationships between tables
3. **Migrations**: Use migration scripts for database changes
4. **Backups**: Implement regular database backups

### Security

1. **Input Sanitization**: Sanitize all user input to prevent injection attacks
2. **Authentication**: Properly authenticate all requests to protected resources
3. **Authorization**: Verify user permissions before allowing access
4. **Sensitive Data**: Never expose sensitive data in responses or logs
5. **Environment Variables**: Always use environment variables for sensitive information
6. **Authentication Middleware**: Always use middleware for authentication
7. **SQL Injection Prevention**: Use parameterized queries for all database operations

### Testing

1. **Unit Tests**: Write tests for individual components
2. **Integration Tests**: Test interactions between components
3. **End-to-End Tests**: Test complete user flows
4. **Manual Testing**: Perform manual testing on different devices and browsers

### Performance Optimization

1. **Database Performance**:
   - Use connection pooling for better resource management
   - Optimize queries with proper indexes and JOINs
   - Process large datasets in batches

2. **Frontend Performance**:
   - Minimize and bundle JavaScript files
   - Optimize image sizes
   - Use lazy loading for non-critical resources
   - Minimize DOM manipulations
   - Implement caching strategies

### Error Handling

1. **Client-Side Error Handling**:
   - Handle API request errors gracefully
   - Provide user-friendly error messages
   - Implement fallback mechanisms for offline use

2. **Server-Side Error Handling**:
   - Log detailed errors for debugging
   - Return appropriate status codes and messages
   - Implement transaction handling for multi-step operations

### Additional Guidelines

1. **Server and Deployment**:
   - Do not hardcode `localhost:5000` or port `:5000` in any files
   - Use relative URLs for API endpoints
   - Ensure all features work with multiple congregations

2. **Code Organization**:
   - Place all test scripts in the `scripts/` directory
   - Update existing documentation as features are implemented
   - Migrate all hardcoded data to the database

## Senior Developer Guidelines

As senior developers, we should adhere to the following guidelines to maintain code quality and project integrity:

### Code Organization and Documentation

1. **Check Existing Resources First**:
   - Always check if an existing script related to your work is available before creating a new one
   - Always check if an existing documentation file (.md) is available before creating a new one
   - Update existing resources rather than creating duplicates

2. **Avoid Duplication**:
   - Do not create multiple test files for the same functionality
   - Consolidate related functionality in a single module
   - Reuse existing components and utilities

3. **Comprehensive Documentation**:
   - Document all new features and changes
   - Update existing documentation when modifying features
   - Include examples and usage instructions

### Development Approach

1. **Understand Before Changing**:
   - Get thoroughly familiar with the codebase before making changes
   - Understand the architecture and design patterns
   - Rushing to make changes without seeing the whole picture will lead to deleting or messing up existing functionalities

2. **Systematic Testing**:
   - Test changes thoroughly before submitting
   - Consider edge cases and error scenarios
   - Verify that changes don't break existing functionality

3. **Incremental Development**:
   - Make small, focused changes
   - Test each change before moving to the next
   - Commit frequently with clear messages

## Authentication Best Practices

1. **Always Use Middleware**: All API endpoints must be protected with authentication middleware:
   ```javascript
   // Apply authentication middleware to routes
   app.use('/api/protected-route', authMiddleware.authenticate, routeHandler);
   ```

2. **Role-Based Access Control**: Verify user roles before allowing access to protected features:
   ```javascript
   // Verify user is an elder before allowing access
   app.use('/api/admin-feature', authMiddleware.authenticate, authMiddleware.isElder, routeHandler);
   ```

3. **Token Management**: Properly handle JWT tokens:
   - Store tokens securely (not in localStorage for production)
   - Include proper expiration
   - Implement token refresh mechanism
   - Validate tokens on the server for every request

4. **Testing Authentication**: When testing authenticated endpoints:
   - Always use the proper login flow
   - Never hardcode tokens
   - Never bypass authentication for convenience

5. **Security Warning**: Never use code like this that bypasses authentication:
   ```javascript
   // INCORRECT - Do not do this
   fetch('/api/auth/congregation-login', {
     method: 'POST',
     body: JSON.stringify({
       congregationId: 'HARDCODED_ID', // Never hardcode credentials
       pin: 'HARDCODED_PIN'            // Never hardcode credentials
     })
   })
   ```

   Instead, always use the proper authentication flow:
   ```javascript
   // CORRECT - Use proper authentication
   const loginResponse = await fetch('/api/auth/congregation-login', {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json'
     },
     body: JSON.stringify({
       congregationId: 'YOUR_CONGREGATION_ID',
       pin: 'YOUR_PIN'
     })
   });

   const { token } = await loginResponse.json();

   // Then make authenticated requests
   const response = await fetch('/api/protected-endpoint', {
     headers: {
       'Authorization': `Bearer ${token}`
     }
   });
   ```

## Current Implementation Status

The current status of the application:

> Note: This status is as of the document update date. Check the project board or repository for the most up-to-date status.

### Completed Features

- **Authentication System**: Implemented with role-based access and JWT tokens
- **Member Management**: Complete member management with role assignment
- **Letters Section**: Complete implementation with file upload and management
- **Dashboard**: Fully functional dashboard with navigation to all sections
- **Task Management**: Complete task management with history view and service group support
- **Song Management**: Complete implementation with JW.org integration and multilingual support
- **Database Backup/Restore**: Robust implementation with Node.js MySQL library and batch processing

### In Progress

- **Permissions System**: Assigning permissions to Elders and Ministerial Servants
- **Events Management**: Creating and managing congregation events
- **Field Service Management**: Tracking and reporting field service activity

### Planned Features

- **Midweek Meeting Management**: Managing midweek meetings and parts
- **Weekend Meeting Management**: Managing weekend meetings and speakers
- **Chat Functionality**: Communication between elders and ministerial servants
- **WhatsApp Integration**: Integration with WhatsApp API
- **Language Options**: Multilingual support with custom translations

## API Documentation Note

> **Important**: The API endpoints listed in this document may evolve over time. For the most up-to-date API documentation:
>
> 1. Review the route files in the `server/routes` directory for the current implementation
> 2. Check the API documentation comments in the route handlers
> 3. Consult the API integration documentation in `docs/api_integration.md`