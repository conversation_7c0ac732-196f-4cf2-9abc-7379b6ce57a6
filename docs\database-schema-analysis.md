# Coral Oeste App - Database Schema Analysis Report

Generated on: 2025-06-01T18:40:51.578Z

## Summary

- **Total Tables**: 43
- **Total Columns**: 434
- **Total Indexes**: 110
- **Total Constraints**: 1837
- **Total Relationships**: 25

## Tables Overview

### congregation_settings
- **Columns**: 6
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 5

### congregations
- **Columns**: 7
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 1

### elder_permissions
- **Columns**: 7
- **Indexes**: 4
- **Constraints**: 41
- **Row Count**: 43

### event_categories
- **Columns**: 5
- **Indexes**: 2
- **Constraints**: 46
- **Row Count**: 5

### events
- **Columns**: 11
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 8

### feature_flags
- **Columns**: 7
- **Indexes**: 2
- **Constraints**: 46
- **Row Count**: 4

### field_service_records
- **Columns**: 11
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 0

### groups
- **Columns**: 9
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 7

### jw_scraper_logs
- **Columns**: 6
- **Indexes**: 1
- **Constraints**: 41
- **Row Count**: 0

### letters
- **Columns**: 8
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 7

### meeting_parts
- **Columns**: 9
- **Indexes**: 4
- **Constraints**: 41
- **Row Count**: 0

### meetings
- **Columns**: 11
- **Indexes**: 4
- **Constraints**: 41
- **Row Count**: 0

### member_congregations
- **Columns**: 7
- **Indexes**: 5
- **Constraints**: 43
- **Row Count**: 4

### members
- **Columns**: 12
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 64

### midweek_assignments_view
- **Columns**: 33
- **Indexes**: 0
- **Constraints**: 0
- **Row Count**: 1

### midweek_attendance
- **Columns**: 6
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 0

### midweek_meeting_assignments
- **Columns**: 8
- **Indexes**: 4
- **Constraints**: 42
- **Row Count**: 0

### midweek_meeting_parts
- **Columns**: 10
- **Indexes**: 4
- **Constraints**: 42
- **Row Count**: 390

### midweek_meeting_settings
- **Columns**: 9
- **Indexes**: 2
- **Constraints**: 43
- **Row Count**: 1

### midweek_meeting_songs
- **Columns**: 8
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 90

### midweek_meeting_view
- **Columns**: 59
- **Indexes**: 0
- **Constraints**: 0
- **Row Count**: 1

### midweek_meeting_weeks
- **Columns**: 10
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 0

### midweek_meeting_workbooks
- **Columns**: 8
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 0

### midweek_meetings
- **Columns**: 42
- **Indexes**: 6
- **Constraints**: 46
- **Row Count**: 30

### midweek_part_definitions
- **Columns**: 10
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 18

### midweek_part_roles
- **Columns**: 5
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 74

### midweek_parts
- **Columns**: 13
- **Indexes**: 5
- **Constraints**: 45
- **Row Count**: 136

### midweek_sections
- **Columns**: 6
- **Indexes**: 2
- **Constraints**: 46
- **Row Count**: 3

### midweek_settings
- **Columns**: 9
- **Indexes**: 2
- **Constraints**: 43
- **Row Count**: 1

### midweek_workbooks
- **Columns**: 9
- **Indexes**: 3
- **Constraints**: 43
- **Row Count**: 3

### permissions
- **Columns**: 5
- **Indexes**: 2
- **Constraints**: 46
- **Row Count**: 17

### role_permissions
- **Columns**: 3
- **Indexes**: 3
- **Constraints**: 84
- **Row Count**: 45

### roles
- **Columns**: 5
- **Indexes**: 2
- **Constraints**: 46
- **Row Count**: 5

### service_groups
- **Columns**: 5
- **Indexes**: 1
- **Constraints**: 41
- **Row Count**: 7

### songs
- **Columns**: 7
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 161

### special_songs
- **Columns**: 7
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 3

### task_assignment_members
- **Columns**: 6
- **Indexes**: 4
- **Constraints**: 44
- **Row Count**: 18

### task_assignments
- **Columns**: 5
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 14

### task_categories
- **Columns**: 5
- **Indexes**: 1
- **Constraints**: 41
- **Row Count**: 5

### task_settings
- **Columns**: 5
- **Indexes**: 1
- **Constraints**: 41
- **Row Count**: 2

### tasks
- **Columns**: 8
- **Indexes**: 2
- **Constraints**: 42
- **Row Count**: 8

### territories
- **Columns**: 9
- **Indexes**: 2
- **Constraints**: 41
- **Row Count**: 0

### user_permissions
- **Columns**: 3
- **Indexes**: 3
- **Constraints**: 83
- **Row Count**: 0

## Detailed Table Structures

### Table: `congregation_settings`

**Row Count**: 5

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| congregation_id | int | NO | MUL | NULL |  |
| setting_key | varchar(100) | NO |  | NULL |  |
| setting_value | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_setting | congregation_id | Yes | BTREE |
| unique_setting | setting_key | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_setting | UNIQUE | congregation_id | - |
| unique_setting | UNIQUE | setting_key | - |

---

### Table: `congregations`

**Row Count**: 1

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(255) | NO |  | NULL |  |
| region | varchar(50) | YES |  | NULL |  |
| congregation_id | varchar(20) | YES | UNI | NULL |  |
| congregation_pin | varchar(20) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| congregation_id | congregation_id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| congregation_id | UNIQUE | congregation_id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `elder_permissions`

**Row Count**: 43

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| elder_id | int | NO | MUL | NULL |  |
| permission | varchar(50) | NO |  | NULL |  |
| granted_by | int | NO | MUL | NULL |  |
| congregation_id | int | NO | MUL | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| granted_by | granted_by | No | BTREE |
| congregation_id | congregation_id | No | BTREE |
| idx_elder_permissions_elder_id | elder_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `event_categories`

**Row Count**: 5

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(100) | NO | UNI | NULL |  |
| description | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| name | name | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `events`

**Row Count**: 8

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| title | varchar(255) | NO |  | NULL |  |
| category_id | int | NO | MUL | NULL |  |
| date | date | NO |  | NULL |  |
| time | time | NO |  | NULL |  |
| location | varchar(255) | NO |  | NULL |  |
| description | text | YES |  | NULL |  |
| status | enum('upcoming','completed') | NO |  | upcoming |  |
| attendance | int | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| category_id | category_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| events_ibfk_1 | FOREIGN KEY | category_id | event_categories.id |

---

### Table: `feature_flags`

**Row Count**: 4

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(255) | NO | UNI | NULL |  |
| description | text | YES |  | NULL |  |
| is_enabled | tinyint(1) | NO |  | 0 |  |
| targeting_rules | json | YES |  | NULL |  |
| created_at | datetime | NO |  | NULL |  |
| updated_at | datetime | NO |  | NULL |  |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| name | name | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `field_service_records`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| user_id | int | NO | MUL | NULL |  |
| month | varchar(7) | NO |  | NULL |  |
| hours | int | NO |  | NULL |  |
| placements | int | YES |  | 0 |  |
| videos | int | YES |  | 0 |  |
| return_visits | int | YES |  | 0 |  |
| bible_studies | int | YES |  | 0 |  |
| notes | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| user_id | user_id | Yes | BTREE |
| user_id | month | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| user_id | UNIQUE | user_id | - |
| user_id | UNIQUE | month | - |

---

### Table: `groups`

**Row Count**: 7

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| congregation_id | int | NO | MUL | NULL |  |
| group_number | int | NO |  | NULL |  |
| name | varchar(100) | NO |  | NULL |  |
| description | text | YES |  | NULL |  |
| leader_id | int | YES |  | NULL |  |
| assistant_id | int | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_group | congregation_id | Yes | BTREE |
| unique_group | group_number | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_group | UNIQUE | congregation_id | - |
| unique_group | UNIQUE | group_number | - |

---

### Table: `jw_scraper_logs`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| url | varchar(255) | NO |  | NULL |  |
| status | varchar(20) | NO |  | NULL |  |
| message | text | YES |  | NULL |  |
| data | json | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `letters`

**Row Count**: 7

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| filename | varchar(255) | NO | UNI | NULL |  |
| title | varchar(255) | NO |  | NULL |  |
| date | date | NO |  | NULL |  |
| category | varchar(50) | NO |  | General |  |
| visibility | varchar(50) | NO |  | All Members |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| filename | filename | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| filename | UNIQUE | filename | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `meeting_parts`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| meeting_id | int | NO | MUL | NULL |  |
| title | varchar(255) | NO |  | NULL |  |
| duration | int | NO |  | NULL |  |
| assignee_id | int | YES | MUL | NULL |  |
| assistant_id | int | YES | MUL | NULL |  |
| notes | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| meeting_id | meeting_id | No | BTREE |
| assignee_id | assignee_id | No | BTREE |
| assistant_id | assistant_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `meetings`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| date | date | NO |  | NULL |  |
| type | enum('midweek','weekend') | NO |  | NULL |  |
| theme | varchar(255) | YES |  | NULL |  |
| chairman_id | int | YES | MUL | NULL |  |
| opening_prayer_id | int | YES | MUL | NULL |  |
| closing_prayer_id | int | YES | MUL | NULL |  |
| attendance | int | YES |  | NULL |  |
| notes | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| chairman_id | chairman_id | No | BTREE |
| opening_prayer_id | opening_prayer_id | No | BTREE |
| closing_prayer_id | closing_prayer_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `member_congregations`

**Row Count**: 4

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| member_id | int | NO | MUL | NULL |  |
| congregation_id | int | NO | MUL | NULL |  |
| is_verified | tinyint(1) | YES |  | 0 |  |
| verification_pin | varchar(10) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| member_id | member_id | Yes | BTREE |
| member_id | congregation_id | Yes | BTREE |
| idx_member_congregations_member_id | member_id | No | BTREE |
| idx_member_congregations_congregation_id | congregation_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| member_id | UNIQUE | member_id | - |
| member_id | UNIQUE | congregation_id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `members`

**Row Count**: 64

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| username | varchar(50) | YES | UNI | NULL |  |
| password | varchar(255) | NO |  | NULL |  |
| name | varchar(100) | NO |  | NULL |  |
| email | varchar(100) | YES |  | NULL |  |
| phone | varchar(20) | YES |  | NULL |  |
| role_id | int | NO | MUL | NULL |  |
| is_active | tinyint(1) | YES |  | 1 |  |
| last_login | timestamp | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| congregation_id | varchar(50) | YES |  | NULL |  |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| username | username | Yes | BTREE |
| idx_members_role_id | role_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| username | UNIQUE | username | - |
| fk_members_role | FOREIGN KEY | role_id | roles.id |

---

### Table: `midweek_assignments_view`

**Row Count**: 1

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO |  | 0 |  |
| meeting_date | int | NO |  | 0 |  |
| meeting_time | int | NO |  | 0 |  |
| meeting_location | int | NO |  | 0 |  |
| chairman_id | int | NO |  | 0 |  |
| prayer_beginning_id | int | NO |  | 0 |  |
| treasures_speaker_id | int | NO |  | 0 |  |
| gems_presenter_id | int | NO |  | 0 |  |
| bible_reading_id | int | NO |  | 0 |  |
| first_ministry_student_id | int | NO |  | 0 |  |
| first_ministry_assistant_id | int | NO |  | 0 |  |
| second_ministry_student_id | int | NO |  | 0 |  |
| second_ministry_assistant_id | int | NO |  | 0 |  |
| ministry_speaker_id | int | NO |  | 0 |  |
| christian_life_part_speaker_id | int | NO |  | 0 |  |
| congregation_bible_study_id | int | NO |  | 0 |  |
| prayer_end_id | int | NO |  | 0 |  |
| audio_video_id | int | NO |  | 0 |  |
| platform_id | int | NO |  | 0 |  |
| microphone1_id | int | NO |  | 0 |  |
| microphone2_id | int | NO |  | 0 |  |
| opening_song_number | int | NO |  | 0 |  |
| opening_song_title | int | NO |  | 0 |  |
| middle_song_number | int | NO |  | 0 |  |
| middle_song_title | int | NO |  | 0 |  |
| closing_song_number | int | NO |  | 0 |  |
| closing_song_title | int | NO |  | 0 |  |
| bible_reading | int | NO |  | 0 |  |
| cleaning_group | int | NO |  | 0 |  |
| zoom_id | int | NO |  | 0 |  |
| zoom_password | int | NO |  | 0 |  |
| week_start_date | int | NO |  | 0 |  |
| week_end_date | int | NO |  | 0 |  |

---

### Table: `midweek_attendance`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| meeting_id | int | NO | MUL | NULL |  |
| attendance_count | int | NO |  | 0 |  |
| recorded_by | int | YES | MUL | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| meeting_id | meeting_id | No | BTREE |
| recorded_by | recorded_by | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| midweek_attendance_ibfk_1 | FOREIGN KEY | meeting_id | midweek_meetings.id |
| midweek_attendance_ibfk_2 | FOREIGN KEY | recorded_by | members.id |

---

### Table: `midweek_meeting_assignments`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| part_id | int | NO | MUL | NULL |  |
| member_id | int | YES | MUL | NULL |  |
| member_name | varchar(255) | YES |  | NULL |  |
| assistant_id | int | YES | MUL | NULL |  |
| assistant_name | varchar(255) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| idx_part_id | part_id | No | BTREE |
| idx_member_id | member_id | No | BTREE |
| idx_assistant_id | assistant_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| midweek_meeting_assignments_ibfk_1 | FOREIGN KEY | part_id | midweek_meeting_parts.id |

---

### Table: `midweek_meeting_parts`

**Row Count**: 390

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| meeting_id | int | NO | MUL | NULL |  |
| part_type | varchar(50) | NO | MUL | NULL |  |
| section | varchar(50) | NO | MUL | NULL |  |
| title | varchar(255) | NO |  | NULL |  |
| duration | int | YES |  | NULL |  |
| description | text | YES |  | NULL |  |
| sort_order | int | NO |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| idx_meeting_id | meeting_id | No | BTREE |
| idx_part_type | part_type | No | BTREE |
| idx_section | section | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| midweek_meeting_parts_ibfk_1 | FOREIGN KEY | meeting_id | midweek_meetings.id |

---

### Table: `midweek_meeting_settings`

**Row Count**: 1

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| congregation_id | int | NO | UNI | NULL |  |
| default_day_of_week | tinyint | NO |  | 5 |  |
| default_time | time | NO |  | 19:30:00 |  |
| default_location | varchar(255) | NO |  | Salón del Reino |  |
| default_zoom_meeting_id | varchar(255) | YES |  | NULL |  |
| default_zoom_meeting_password | varchar(255) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_congregation | congregation_id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_congregation | UNIQUE | congregation_id | - |
| unique_congregation | UNIQUE | congregation_id | - |

---

### Table: `midweek_meeting_songs`

**Row Count**: 90

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| meeting_id | int | NO | MUL | NULL |  |
| song_number | int | NO |  | NULL |  |
| title | varchar(255) | YES |  | NULL |  |
| display_order | int | NO |  | NULL |  |
| section | varchar(50) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| meeting_id | meeting_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| midweek_meeting_songs_ibfk_1 | FOREIGN KEY | meeting_id | midweek_meetings.id |

---

### Table: `midweek_meeting_view`

**Row Count**: 1

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO |  | 0 |  |
| week_start | int | NO |  | 0 |  |
| week_end | int | NO |  | 0 |  |
| workbook_id | int | NO |  | 0 |  |
| week_start_date | int | NO |  | 0 |  |
| week_end_date | int | NO |  | 0 |  |
| theme | int | NO |  | 0 |  |
| url_path | int | NO |  | 0 |  |
| chairman_id | int | NO |  | 0 |  |
| prayer_beginning_id | int | NO |  | 0 |  |
| prayer_end_id | int | NO |  | 0 |  |
| meeting_date | int | NO |  | 0 |  |
| meeting_time | int | NO |  | 0 |  |
| meeting_location | int | NO |  | 0 |  |
| zoom_link | int | NO |  | 0 |  |
| zoom_id | int | NO |  | 0 |  |
| zoom_password | int | NO |  | 0 |  |
| status | int | NO |  | 0 |  |
| created_at | int | NO |  | 0 |  |
| updated_at | int | NO |  | 0 |  |
| opening_song_number | int | NO |  | 0 |  |
| opening_song_title | int | NO |  | 0 |  |
| middle_song_number | int | NO |  | 0 |  |
| middle_song_title | int | NO |  | 0 |  |
| closing_song_number | int | NO |  | 0 |  |
| closing_song_title | int | NO |  | 0 |  |
| treasures_speaker_id | int | NO |  | 0 |  |
| gems_presenter_id | int | NO |  | 0 |  |
| bible_reading_id | int | NO |  | 0 |  |
| bible_reading | int | NO |  | 0 |  |
| first_ministry_student_id | int | NO |  | 0 |  |
| first_ministry_assistant_id | int | NO |  | 0 |  |
| second_ministry_student_id | int | NO |  | 0 |  |
| second_ministry_assistant_id | int | NO |  | 0 |  |
| ministry_speaker_id | int | NO |  | 0 |  |
| christian_life_part_speaker_id | int | NO |  | 0 |  |
| congregation_bible_study_id | int | NO |  | 0 |  |
| audio_video_id | int | NO |  | 0 |  |
| platform_id | int | NO |  | 0 |  |
| microphone1_id | int | NO |  | 0 |  |
| microphone2_id | int | NO |  | 0 |  |
| cleaning_group | int | NO |  | 0 |  |
| chairman_id_from_parts | int | NO |  | 0 |  |
| prayer_beginning_id_from_parts | int | NO |  | 0 |  |
| prayer_end_id_from_parts | int | NO |  | 0 |  |
| treasures_speaker_id_from_parts | int | NO |  | 0 |  |
| gems_presenter_id_from_parts | int | NO |  | 0 |  |
| bible_reading_id_from_parts | int | NO |  | 0 |  |
| first_ministry_student_id_from_parts | int | NO |  | 0 |  |
| first_ministry_assistant_id_from_parts | int | NO |  | 0 |  |
| second_ministry_student_id_from_parts | int | NO |  | 0 |  |
| second_ministry_assistant_id_from_parts | int | NO |  | 0 |  |
| ministry_speaker_id_from_parts | int | NO |  | 0 |  |
| christian_life_part_speaker_id_from_parts | int | NO |  | 0 |  |
| congregation_bible_study_id_from_parts | int | NO |  | 0 |  |
| audio_video_id_from_parts | int | NO |  | 0 |  |
| platform_id_from_parts | int | NO |  | 0 |  |
| microphone1_id_from_parts | int | NO |  | 0 |  |
| microphone2_id_from_parts | int | NO |  | 0 |  |

---

### Table: `midweek_meeting_weeks`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| workbook_id | varchar(50) | NO | MUL | NULL |  |
| week_id | varchar(100) | NO | UNI | NULL |  |
| title | varchar(255) | NO |  | NULL |  |
| start_date | date | NO |  | NULL |  |
| end_date | date | NO |  | NULL |  |
| url | varchar(255) | NO |  | NULL |  |
| content | longtext | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_week_id | week_id | Yes | BTREE |
| idx_workbook_id | workbook_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_week_id | UNIQUE | week_id | - |
| midweek_meeting_weeks_ibfk_1 | FOREIGN KEY | workbook_id | midweek_meeting_workbooks.workbook_id |

---

### Table: `midweek_meeting_workbooks`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| workbook_id | varchar(50) | NO | UNI | NULL |  |
| title | varchar(255) | NO |  | NULL |  |
| start_date | date | NO |  | NULL |  |
| end_date | date | NO |  | NULL |  |
| url | varchar(255) | NO |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_workbook_id | workbook_id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_workbook_id | UNIQUE | workbook_id | - |

---

### Table: `midweek_meetings`

**Row Count**: 30

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| week_start | datetime | YES |  | NULL |  |
| week_end | datetime | YES |  | NULL |  |
| workbook_id | int | NO | MUL | NULL |  |
| week_start_date | date | NO | UNI | NULL |  |
| week_end_date | date | NO |  | NULL |  |
| theme | varchar(255) | NO |  | NULL |  |
| url_path | varchar(255) | NO |  | NULL |  |
| chairman_id | int | YES | MUL | NULL |  |
| prayer_beginning_id | int | YES | MUL | NULL |  |
| prayer_end_id | int | YES | MUL | NULL |  |
| meeting_date | date | YES |  | NULL |  |
| meeting_time | time | YES |  | 19:00:00 |  |
| meeting_location | varchar(255) | YES |  | NULL |  |
| zoom_link | varchar(255) | YES |  | NULL |  |
| zoom_id | varchar(50) | YES |  | NULL |  |
| zoom_password | varchar(50) | YES |  | NULL |  |
| status | enum('draft','scheduled','completed') | YES |  | draft |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| opening_song_number | varchar(10) | YES |  | NULL |  |
| opening_song_title | varchar(255) | YES |  | NULL |  |
| middle_song_number | varchar(10) | YES |  | NULL |  |
| middle_song_title | varchar(255) | YES |  | NULL |  |
| closing_song_number | varchar(10) | YES |  | NULL |  |
| closing_song_title | varchar(255) | YES |  | NULL |  |
| treasures_speaker_id | int | YES |  | NULL |  |
| gems_presenter_id | int | YES |  | NULL |  |
| bible_reading_id | int | YES |  | NULL |  |
| bible_reading | varchar(255) | YES |  | NULL |  |
| first_ministry_student_id | int | YES |  | NULL |  |
| first_ministry_assistant_id | int | YES |  | NULL |  |
| second_ministry_student_id | int | YES |  | NULL |  |
| second_ministry_assistant_id | int | YES |  | NULL |  |
| ministry_speaker_id | int | YES |  | NULL |  |
| christian_life_part_speaker_id | int | YES |  | NULL |  |
| congregation_bible_study_id | int | YES |  | NULL |  |
| audio_video_id | int | YES |  | NULL |  |
| platform_id | int | YES |  | NULL |  |
| microphone1_id | int | YES |  | NULL |  |
| microphone2_id | int | YES |  | NULL |  |
| cleaning_group | varchar(50) | YES |  | NULL |  |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| week_start_date | week_start_date | Yes | BTREE |
| workbook_id | workbook_id | No | BTREE |
| chairman_id | chairman_id | No | BTREE |
| prayer_beginning_id_fk | prayer_beginning_id | No | BTREE |
| prayer_end_id_fk | prayer_end_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| week_start_date | UNIQUE | week_start_date | - |
| midweek_meetings_ibfk_1 | FOREIGN KEY | workbook_id | midweek_workbooks.id |
| midweek_meetings_ibfk_2 | FOREIGN KEY | chairman_id | members.id |
| prayer_beginning_id_fk | FOREIGN KEY | prayer_beginning_id | members.id |
| prayer_end_id_fk | FOREIGN KEY | prayer_end_id | members.id |

---

### Table: `midweek_part_definitions`

**Row Count**: 18

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| congregation_id | int | YES |  | NULL |  |
| type | varchar(50) | NO | MUL | NULL |  |
| name | varchar(100) | NO |  | NULL |  |
| duration | int | NO |  | 5 |  |
| order_index | int | NO |  | 0 |  |
| elder_only | tinyint(1) | NO |  | 0 |  |
| ms_only | tinyint(1) | NO |  | 0 |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_part_type_congregation | type | Yes | BTREE |
| unique_part_type_congregation | congregation_id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_part_type_congregation | UNIQUE | type | - |
| unique_part_type_congregation | UNIQUE | congregation_id | - |

---

### Table: `midweek_part_roles`

**Row Count**: 74

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| part_type | varchar(50) | NO |  | NULL |  |
| role_id | int | NO | MUL | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| role_id | role_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| midweek_part_roles_ibfk_1 | FOREIGN KEY | role_id | roles.id |

---

### Table: `midweek_parts`

**Row Count**: 136

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| meeting_id | int | NO | MUL | NULL |  |
| section_id | int | NO | MUL | NULL |  |
| title | varchar(255) | NO |  | NULL |  |
| description | text | YES |  | NULL |  |
| duration | int | NO |  | NULL |  |
| assignee_id | int | YES | MUL | NULL |  |
| assistant_id | int | YES | MUL | NULL |  |
| display_order | int | NO |  | NULL |  |
| jw_number | int | YES |  | NULL |  |
| is_section_header | tinyint(1) | YES |  | 0 |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| meeting_id | meeting_id | No | BTREE |
| section_id | section_id | No | BTREE |
| assignee_id | assignee_id | No | BTREE |
| assistant_id | assistant_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| midweek_parts_ibfk_1 | FOREIGN KEY | meeting_id | midweek_meetings.id |
| midweek_parts_ibfk_2 | FOREIGN KEY | section_id | midweek_sections.id |
| midweek_parts_ibfk_3 | FOREIGN KEY | assignee_id | members.id |
| midweek_parts_ibfk_4 | FOREIGN KEY | assistant_id | members.id |

---

### Table: `midweek_sections`

**Row Count**: 3

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(50) | NO | UNI | NULL |  |
| display_name | varchar(100) | NO |  | NULL |  |
| display_order | int | NO |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| name | name | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `midweek_settings`

**Row Count**: 1

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| congregation_id | int | NO | UNI | NULL |  |
| default_day_of_week | tinyint | YES |  | 5 |  |
| default_time | varchar(10) | YES |  | 19:30 |  |
| default_location | varchar(100) | YES |  | Salon del Reino |  |
| default_zoom_meeting_id | varchar(50) | YES |  | NULL |  |
| default_zoom_meeting_password | varchar(50) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_congregation | congregation_id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_congregation | UNIQUE | congregation_id | - |
| unique_congregation | UNIQUE | congregation_id | - |

---

### Table: `midweek_workbooks`

**Row Count**: 3

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| month_period | varchar(50) | NO | MUL | NULL |  |
| year | int | NO |  | NULL |  |
| url_path | varchar(255) | NO |  | NULL |  |
| source_url | varchar(255) | NO |  | NULL |  |
| is_processed | tinyint(1) | YES |  | 0 |  |
| processed_at | timestamp | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| month_period | month_period | Yes | BTREE |
| month_period | year | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| month_period | UNIQUE | month_period | - |
| month_period | UNIQUE | year | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `permissions`

**Row Count**: 17

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(50) | NO | UNI | NULL |  |
| description | varchar(255) | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| name | name | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `role_permissions`

**Row Count**: 45

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| role_id | int | NO | PRI | NULL |  |
| permission_id | int | NO | PRI | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | role_id | Yes | BTREE |
| PRIMARY | permission_id | Yes | BTREE |
| permission_id | permission_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | role_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| role_permissions_ibfk_1 | FOREIGN KEY | role_id | roles.id |
| role_permissions_ibfk_2 | FOREIGN KEY | permission_id | permissions.id |

---

### Table: `roles`

**Row Count**: 5

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(50) | NO | UNI | NULL |  |
| description | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| name | name | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| name | UNIQUE | name | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `service_groups`

**Row Count**: 7

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(100) | NO |  | NULL |  |
| group_number | int | NO |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `songs`

**Row Count**: 161

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| number | int | NO | UNI | NULL |  |
| title_es | varchar(255) | NO |  | NULL |  |
| title_en | varchar(255) | YES |  | NULL |  |
| is_custom | tinyint(1) | YES |  | 0 |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_song_number | number | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_song_number | UNIQUE | number | - |

---

### Table: `special_songs`

**Row Count**: 3

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| key_name | varchar(50) | NO | UNI | NULL |  |
| title_es | varchar(255) | NO |  | NULL |  |
| title_en | varchar(255) | YES |  | NULL |  |
| is_custom | tinyint(1) | YES |  | 0 |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| unique_song_key | key_name | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| unique_song_key | UNIQUE | key_name | - |

---

### Table: `task_assignment_members`

**Row Count**: 18

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| assignment_id | int | NO | MUL | NULL |  |
| user_id | int | YES | MUL | NULL |  |
| service_group_id | int | YES | MUL | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| assignment_id | assignment_id | No | BTREE |
| user_id | user_id | No | BTREE |
| service_group_id | service_group_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| task_assignment_members_ibfk_1 | FOREIGN KEY | assignment_id | task_assignments.id |
| task_assignment_members_ibfk_2 | FOREIGN KEY | user_id | members.id |
| task_assignment_members_ibfk_3 | FOREIGN KEY | service_group_id | service_groups.id |

---

### Table: `task_assignments`

**Row Count**: 14

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| task_id | int | NO | MUL | NULL |  |
| assignment_date | date | NO |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| task_id | task_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| task_assignments_ibfk_1 | FOREIGN KEY | task_id | tasks.id |

---

### Table: `task_categories`

**Row Count**: 5

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(100) | NO |  | NULL |  |
| description | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `task_settings`

**Row Count**: 2

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| setting_key | varchar(50) | NO |  | NULL |  |
| setting_value | json | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `tasks`

**Row Count**: 8

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(100) | NO |  | NULL |  |
| category_id | int | YES | MUL | NULL |  |
| description | text | YES |  | NULL |  |
| status | enum('pending','completed') | YES |  | pending |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |
| display_order | int | YES |  | 0 |  |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| category_id | category_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| tasks_ibfk_1 | FOREIGN KEY | category_id | task_categories.id |

---

### Table: `territories`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| id | int | NO | PRI | NULL | auto_increment |
| name | varchar(100) | NO |  | NULL |  |
| description | text | YES |  | NULL |  |
| status | enum('available','assigned','completed') | YES |  | available |  |
| assigned_to | int | YES | MUL | NULL |  |
| last_worked | date | YES |  | NULL |  |
| notes | text | YES |  | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |
| updated_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED on update CURRENT_TIMESTAMP |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | id | Yes | BTREE |
| assigned_to | assigned_to | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |
| PRIMARY | PRIMARY KEY | id | - |

---

### Table: `user_permissions`

**Row Count**: 0

#### Columns:
| Column | Type | Null | Key | Default | Extra |
|--------|------|------|-----|---------|-------|
| user_id | int | NO | PRI | NULL |  |
| permission_id | int | NO | PRI | NULL |  |
| created_at | timestamp | YES |  | CURRENT_TIMESTAMP | DEFAULT_GENERATED |

#### Indexes:
| Key Name | Column | Unique | Type |
|----------|--------|--------|------|
| PRIMARY | user_id | Yes | BTREE |
| PRIMARY | permission_id | Yes | BTREE |
| permission_id | permission_id | No | BTREE |

#### Constraints:
| Constraint Name | Type | Column | References |
|----------------|------|--------|------------|
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | user_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| PRIMARY | PRIMARY KEY | permission_id | - |
| user_permissions_ibfk_1 | FOREIGN KEY | permission_id | permissions.id |

---

## Database Relationships

The following foreign key relationships exist in the database:

| From Table | From Column | To Table | To Column | Constraint Name |
|------------|-------------|----------|-----------|-----------------|
| events | category_id | event_categories | id | events_ibfk_1 |
| members | role_id | roles | id | fk_members_role |
| midweek_attendance | meeting_id | midweek_meetings | id | midweek_attendance_ibfk_1 |
| midweek_attendance | recorded_by | members | id | midweek_attendance_ibfk_2 |
| midweek_meeting_assignments | part_id | midweek_meeting_parts | id | midweek_meeting_assignments_ibfk_1 |
| midweek_meeting_parts | meeting_id | midweek_meetings | id | midweek_meeting_parts_ibfk_1 |
| midweek_meeting_songs | meeting_id | midweek_meetings | id | midweek_meeting_songs_ibfk_1 |
| midweek_meeting_weeks | workbook_id | midweek_meeting_workbooks | workbook_id | midweek_meeting_weeks_ibfk_1 |
| midweek_meetings | workbook_id | midweek_workbooks | id | midweek_meetings_ibfk_1 |
| midweek_meetings | chairman_id | members | id | midweek_meetings_ibfk_2 |
| midweek_meetings | prayer_beginning_id | members | id | prayer_beginning_id_fk |
| midweek_meetings | prayer_end_id | members | id | prayer_end_id_fk |
| midweek_part_roles | role_id | roles | id | midweek_part_roles_ibfk_1 |
| midweek_parts | meeting_id | midweek_meetings | id | midweek_parts_ibfk_1 |
| midweek_parts | section_id | midweek_sections | id | midweek_parts_ibfk_2 |
| midweek_parts | assignee_id | members | id | midweek_parts_ibfk_3 |
| midweek_parts | assistant_id | members | id | midweek_parts_ibfk_4 |
| role_permissions | role_id | roles | id | role_permissions_ibfk_1 |
| role_permissions | permission_id | permissions | id | role_permissions_ibfk_2 |
| task_assignment_members | assignment_id | task_assignments | id | task_assignment_members_ibfk_1 |
| task_assignment_members | user_id | members | id | task_assignment_members_ibfk_2 |
| task_assignment_members | service_group_id | service_groups | id | task_assignment_members_ibfk_3 |
| task_assignments | task_id | tasks | id | task_assignments_ibfk_1 |
| tasks | category_id | task_categories | id | tasks_ibfk_1 |
| user_permissions | permission_id | permissions | id | user_permissions_ibfk_1 |

## Entity Relationship Analysis

### Core Entities

#### Authentication & Authorization
- **congregations**: Stores congregation information
- **roles**: Defines user roles (publisher, ministerial_servant, elder, etc.)
- **permissions**: Defines system permissions
- **role_permissions**: Maps roles to permissions
- **user_permissions**: Maps individual users to permissions
- **members**: User accounts with role assignments

#### Meeting Management
- **midweek_meetings**: Stores midweek meeting information
- **midweek_meeting_parts**: Individual parts of midweek meetings
- **elder_permissions**: Elder-specific permissions
- **elder_role_permissions**: Role-based permissions for elders

#### Task Management
- **task_categories**: Categories for tasks
- **tasks**: Individual tasks
- **task_assignments**: Assignment of tasks to dates/groups
- **service_groups**: Service group information

#### Event Management
- **event_categories**: Categories for events
- **events**: Congregation events

#### Communication
- **letters**: Letter management system

#### Songs & Worship
- **songs**: Song catalog management
- **custom_songs**: Custom songs added by congregation

### Key Relationships

1. **Members → Roles**: Each member has a role that determines their permissions
2. **Congregations → Members**: Members belong to specific congregations
3. **Meetings → Members**: Meeting parts are assigned to specific members
4. **Tasks → Categories**: Tasks are organized by categories
5. **Events → Categories**: Events are organized by categories
6. **Permissions → Roles**: Roles have specific permissions assigned

### Data Integrity Features

- Foreign key constraints ensure referential integrity
- Cascade deletes where appropriate (e.g., meeting parts when meeting is deleted)
- Set NULL on delete for optional references (e.g., when a member is deleted)
- Unique constraints prevent duplicate data
- Default values ensure consistent data entry

