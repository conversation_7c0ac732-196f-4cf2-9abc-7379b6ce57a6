/**
 * Midweek Meeting React Components
 * 
 * This file contains React components for the Midweek Meeting management system.
 * These components can be used directly in HTML files without a build step.
 */

// Create a namespace for our components
window.CoralOeste = window.CoralOeste || {};
window.CoralOeste.MidweekMeeting = {};

(function() {
  'use strict';
  
  // API Service
  const api = {
    // Get authentication token
    getToken: function() {
      return localStorage.getItem('token');
    },
    
    // Fetch meetings
    fetchMeetings: async function() {
      try {
        const token = this.getToken();
        
        if (!token) {
          throw new Error('No authentication token found');
        }
        
        // Try the WOL endpoint first
        const wolResponse = await fetch('/api/midweek/wol/meetings', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (wolResponse.ok) {
          const wolData = await wolResponse.json();
          
          if (wolData.success && wolData.meetings) {
            console.log('Successfully fetched meetings from WOL endpoint:', wolData.meetings.length);
            return wolData.meetings;
          }
        }
        
        // Fallback to the regular API
        const response = await fetch('/api/midweek/meetings', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch meetings: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.message || 'Failed to fetch meetings');
        }
        
        return data.meetings;
      } catch (error) {
        console.error('Error fetching meetings:', error);
        throw error;
      }
    },
    
    // Fetch members
    fetchMembers: async function() {
      try {
        const token = this.getToken();
        
        if (!token) {
          throw new Error('No authentication token found');
        }
        
        const response = await fetch('/api/members', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch members: ${response.status}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
          throw new Error(data.message || 'Failed to fetch members');
        }
        
        return data.members;
      } catch (error) {
        console.error('Error fetching members:', error);
        throw error;
      }
    }
  };
  
  // Header Component
  function Header({ title }) {
    return React.createElement('header', { className: 'app-header' },
      React.createElement('button', { 
        className: 'back-button',
        onClick: () => window.location.href = '/admin/index.html'
      },
        React.createElement('i', { className: 'material-icons' }, 'arrow_back'),
        'Volver'
      ),
      React.createElement('h1', { className: 'app-title' }, title)
    );
  }
  
  // Tab Navigation Component
  function TabNavigation({ activeTab, onTabChange }) {
    const tabs = [
      { id: 'schedule', label: 'Programación' },
      { id: 'history', label: 'Historial' },
      { id: 'members', label: 'Miembros' },
      { id: 'settings', label: 'Configuración' }
    ];
    
    return React.createElement('div', { className: 'tabs' },
      tabs.map(tab => 
        React.createElement('div', { 
          key: tab.id,
          className: `tab ${activeTab === tab.id ? 'active' : ''}`,
          onClick: () => onTabChange(tab.id)
        }, tab.label)
      )
    );
  }
  
  // Meeting Schedule Tab Component
  function MeetingScheduleTab({ meetings, isLoading, onViewMeeting, onEditMeeting, onAddWolMeeting }) {
    // Filter for upcoming meetings
    const upcomingMeetings = meetings.filter(meeting => {
      const meetingDate = new Date(meeting.meeting_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      meetingDate.setHours(0, 0, 0, 0);
      return meetingDate >= today;
    });
    
    // Sort by date (closest first)
    upcomingMeetings.sort((a, b) => {
      const dateA = new Date(a.meeting_date);
      const dateB = new Date(b.meeting_date);
      return dateA - dateB;
    });
    
    // Format date
    const formatDate = (dateString) => {
      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      return new Date(dateString).toLocaleDateString('es-ES', options);
    };
    
    return React.createElement('div', { className: 'tab-content active' },
      React.createElement('div', { className: 'card-actions', style: { marginBottom: '16px' } },
        React.createElement('button', { 
          className: 'button button-secondary',
          onClick: onAddWolMeeting
        },
          React.createElement('i', { className: 'material-icons' }, 'cloud_download'),
          'Importar desde JW.org'
        )
      ),
      
      isLoading ? 
        React.createElement('div', { className: 'message loading-message' }, 'Cargando reuniones...') :
        upcomingMeetings.length === 0 ?
          React.createElement('div', { className: 'message' }, 'No hay reuniones programadas.') :
          React.createElement('table', null,
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'Fecha'),
                React.createElement('th', null, 'Hora'),
                React.createElement('th', null, 'Ubicación'),
                React.createElement('th', null, 'Presidente'),
                React.createElement('th', null, 'Acciones')
              )
            ),
            React.createElement('tbody', null,
              upcomingMeetings.map(meeting => 
                React.createElement('tr', { key: meeting.id },
                  React.createElement('td', null, formatDate(meeting.meeting_date)),
                  React.createElement('td', null, meeting.meeting_time),
                  React.createElement('td', null, meeting.meeting_location === 'salon' ? 'Salón del Reino' : 'Zoom'),
                  React.createElement('td', null, meeting.chairman_name || 'No asignado'),
                  React.createElement('td', null,
                    React.createElement('div', { className: 'action-buttons' },
                      React.createElement('button', { 
                        className: 'button button-secondary',
                        style: { padding: '4px 8px', fontSize: '12px' },
                        onClick: () => onViewMeeting(meeting)
                      }, 'Ver'),
                      React.createElement('button', { 
                        className: 'button',
                        style: { padding: '4px 8px', fontSize: '12px' },
                        onClick: () => onEditMeeting(meeting)
                      }, 'Editar')
                    )
                  )
                )
              )
            )
          )
    );
  }
  
  // Past Meetings Tab Component
  function PastMeetingsTab({ meetings, isLoading, onViewMeeting }) {
    // Filter for past meetings
    const pastMeetings = meetings.filter(meeting => {
      const meetingDate = new Date(meeting.meeting_date);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      meetingDate.setHours(0, 0, 0, 0);
      return meetingDate < today;
    });
    
    // Sort by date (newest first)
    pastMeetings.sort((a, b) => {
      const dateA = new Date(a.meeting_date);
      const dateB = new Date(b.meeting_date);
      return dateB - dateA;
    });
    
    // Format date
    const formatDate = (dateString) => {
      const options = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };
      return new Date(dateString).toLocaleDateString('es-ES', options);
    };
    
    return React.createElement('div', { className: 'tab-content active' },
      isLoading ? 
        React.createElement('div', { className: 'message loading-message' }, 'Cargando reuniones pasadas...') :
        pastMeetings.length === 0 ?
          React.createElement('div', { className: 'message' }, 'No hay reuniones pasadas.') :
          React.createElement('table', null,
            React.createElement('thead', null,
              React.createElement('tr', null,
                React.createElement('th', null, 'Fecha'),
                React.createElement('th', null, 'Hora'),
                React.createElement('th', null, 'Ubicación'),
                React.createElement('th', null, 'Presidente'),
                React.createElement('th', null, 'Acciones')
              )
            ),
            React.createElement('tbody', null,
              pastMeetings.map(meeting => 
                React.createElement('tr', { key: meeting.id },
                  React.createElement('td', null, formatDate(meeting.meeting_date)),
                  React.createElement('td', null, meeting.meeting_time),
                  React.createElement('td', null, meeting.meeting_location === 'salon' ? 'Salón del Reino' : 'Zoom'),
                  React.createElement('td', null, meeting.chairman_name || 'No asignado'),
                  React.createElement('td', null,
                    React.createElement('button', { 
                      className: 'button button-secondary',
                      style: { padding: '4px 8px', fontSize: '12px' },
                      onClick: () => onViewMeeting(meeting)
                    }, 'Ver')
                  )
                )
              )
            )
          )
    );
  }
  
  // Main App Component
  function App() {
    const [activeTab, setActiveTab] = React.useState('schedule');
    const [meetings, setMeetings] = React.useState([]);
    const [members, setMembers] = React.useState([]);
    const [isLoadingMeetings, setIsLoadingMeetings] = React.useState(true);
    const [isLoadingMembers, setIsLoadingMembers] = React.useState(true);
    const [error, setError] = React.useState(null);
    const [selectedMeeting, setSelectedMeeting] = React.useState(null);
    const [showViewMeetingModal, setShowViewMeetingModal] = React.useState(false);
    
    // Fetch meetings and members
    React.useEffect(() => {
      const fetchData = async () => {
        try {
          // Fetch meetings
          setIsLoadingMeetings(true);
          const meetingsData = await api.fetchMeetings();
          setMeetings(meetingsData);
          setIsLoadingMeetings(false);
          
          // Fetch members
          setIsLoadingMembers(true);
          const membersData = await api.fetchMembers();
          setMembers(membersData);
          setIsLoadingMembers(false);
        } catch (error) {
          setError(error.message);
          setIsLoadingMeetings(false);
          setIsLoadingMembers(false);
        }
      };
      
      fetchData();
    }, []);
    
    // Handle tab change
    const handleTabChange = (tab) => {
      setActiveTab(tab);
    };
    
    // Handle view meeting
    const handleViewMeeting = (meeting) => {
      console.log('View meeting:', meeting.id);
      // Use the modal management function
      if (window.midweekMeetingModalManagement && window.midweekMeetingModalManagement.openViewMeetingModal) {
        window.midweekMeetingModalManagement.openViewMeetingModal(meeting.id);
      } else if (typeof openViewMeetingModal === 'function') {
        openViewMeetingModal(meeting.id);
      } else {
        console.error('View meeting modal function not available');
        // Fallback to React modal
        setSelectedMeeting(meeting);
        setShowViewMeetingModal(true);
      }
    };

    // Handle edit meeting
    const handleEditMeeting = (meeting) => {
      console.log('Edit meeting:', meeting.id);
      // Use the modal management function
      if (window.midweekMeetingModalManagement && window.midweekMeetingModalManagement.openEditMeetingModal) {
        window.midweekMeetingModalManagement.openEditMeetingModal(meeting.id);
      } else if (typeof openEditMeetingModal === 'function') {
        openEditMeetingModal(meeting.id);
      } else {
        console.error('Edit meeting modal function not available');
        alert(`Editar reunión: ${meeting.id}`);
      }
    };
    
    // Handle add meeting from WOL
    const handleAddWolMeeting = () => {
      alert('Importar reunión desde JW.org');
    };
    
    // Handle close view meeting modal
    const handleCloseViewMeetingModal = () => {
      setShowViewMeetingModal(false);
      setSelectedMeeting(null);
    };
    
    return React.createElement('div', { className: 'app' },
      React.createElement(Header, { title: 'Administración de Reuniones Entre Semana' }),
      
      React.createElement('div', { className: 'container' },
        error && React.createElement('div', { className: 'message error-message' }, error),
        
        React.createElement('div', { className: 'card' },
          React.createElement('div', { className: 'card-header' },
            React.createElement('h2', { className: 'card-title' }, 'Reuniones Entre Semana'),
            React.createElement('div', { className: 'card-actions' },
              React.createElement('button', { 
                className: 'button',
                onClick: () => alert('Agregar reunión')
              },
                React.createElement('i', { className: 'material-icons' }, 'add'),
                'Agregar Reunión'
              )
            )
          ),
          
          React.createElement(TabNavigation, { 
            activeTab: activeTab,
            onTabChange: handleTabChange
          }),
          
          activeTab === 'schedule' && React.createElement(MeetingScheduleTab, {
            meetings: meetings,
            isLoading: isLoadingMeetings,
            onViewMeeting: handleViewMeeting,
            onEditMeeting: handleEditMeeting,
            onAddWolMeeting: handleAddWolMeeting
          }),
          
          activeTab === 'history' && React.createElement(PastMeetingsTab, {
            meetings: meetings,
            isLoading: isLoadingMeetings,
            onViewMeeting: handleViewMeeting
          }),
          
          activeTab === 'members' && React.createElement('div', { className: 'tab-content active' },
            isLoadingMembers ? 
              React.createElement('div', { className: 'message loading-message' }, 'Cargando miembros...') :
              React.createElement('div', { className: 'message' }, 'Funcionalidad en desarrollo')
          ),
          
          activeTab === 'settings' && React.createElement('div', { className: 'tab-content active' },
            React.createElement('div', { className: 'message' }, 'Funcionalidad en desarrollo')
          )
        )
      ),
      
      showViewMeetingModal && selectedMeeting && React.createElement('div', { className: 'modal view-meeting-modal' },
        React.createElement('div', { className: 'modal-content' },
          React.createElement('button', { 
            className: 'modal-close',
            onClick: handleCloseViewMeetingModal
          }, '×'),
          React.createElement('h2', { className: 'modal-title' }, 'Ver Reunión'),
          
          React.createElement('div', { className: 'meeting-view' },
            React.createElement('div', { className: 'meeting-card' },
              React.createElement('div', { className: 'meeting-header' },
                React.createElement('div', { className: 'meeting-title' }, 'Reunión Entre Semana'),
                React.createElement('div', { className: 'meeting-date' }, new Date(selectedMeeting.meeting_date).toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }))
              ),
              
              React.createElement('div', { className: 'meeting-details' },
                React.createElement('p', null, 
                  React.createElement('strong', null, 'Hora:'), 
                  ' ', 
                  selectedMeeting.meeting_time
                ),
                React.createElement('p', null, 
                  React.createElement('strong', null, 'Ubicación:'), 
                  ' ', 
                  selectedMeeting.meeting_location === 'salon' ? 'Salón del Reino' : 'Zoom'
                ),
                selectedMeeting.meeting_location === 'zoom' && [
                  React.createElement('p', { key: 'zoom-id' }, 
                    React.createElement('strong', null, 'ID de Zoom:'), 
                    ' ', 
                    selectedMeeting.zoom_id || 'No especificado'
                  ),
                  React.createElement('p', { key: 'zoom-password' }, 
                    React.createElement('strong', null, 'Contraseña de Zoom:'), 
                    ' ', 
                    selectedMeeting.zoom_password || 'No especificada'
                  )
                ],
                React.createElement('p', null, 
                  React.createElement('strong', null, 'Presidente:'), 
                  ' ', 
                  selectedMeeting.chairman_name || 'No asignado'
                )
              )
            )
          ),
          
          React.createElement('div', { className: 'form-actions' },
            React.createElement('button', { 
              className: 'button button-secondary',
              onClick: handleCloseViewMeetingModal
            }, 'Cerrar')
          )
        )
      )
    );
  }
  
  // Export components
  window.CoralOeste.MidweekMeeting = {
    App,
    Header,
    TabNavigation,
    MeetingScheduleTab,
    PastMeetingsTab,
    api
  };
  
  // Initialize function
  window.CoralOeste.initializeMidweekMeetingApp = function(elementId, options = {}) {
    const rootElement = typeof elementId === 'string' ? document.getElementById(elementId) : elementId;
    
    if (!rootElement) {
      console.error('Root element not found');
      return null;
    }
    
    ReactDOM.createRoot(rootElement).render(
      React.createElement(App)
    );
    
    return null;
  };
})();
